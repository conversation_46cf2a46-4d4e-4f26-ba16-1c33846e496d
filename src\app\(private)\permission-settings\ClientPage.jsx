'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getOrgMetaData } from '@/lib/api/common';
import { getAllFeaturesList, getAllIdTypes } from '@/lib/api/orgData';
import { PermissionsEditor } from './Editors';
import useNotification from '@/lib/hooks/useNotification';
import { ORGANIZATION_ID, ORGANIZATION_METADATA } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

// TODO: Org Data is the source of truth and all data should be retrieved from there.
function ClientPage() {
  const openSnackbar = useNotification();

  const allFeaturesListQuery = useQuery({
    queryKey: ['allFeaturesList'],
    queryFn: () => getAllFeaturesList(),
  });

  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => getOrgMetaData(),
  });

  const queryClient = useQueryClient();

  const handleFeaturesSaveCallback = async (features) => {
    try {
      const orgDataValues = { ...orgMetaDataQuery.data };

      orgDataValues.features = features.map((feature) => {
        if (feature.enable) {
          return feature.name;
        }
      });

      orgDataValues.features = orgDataValues.features.filter((feature) => feature != null);

      const updateOrgResponse = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}`, {
        method: 'PUT',
        body: JSON.stringify({
          ...orgDataValues,
        }),
      });

      queryClient.setQueryData([ORGANIZATION_METADATA], orgDataValues);
      queryClient.invalidateQueries([ORGANIZATION_METADATA]);
      return updateOrgResponse.ok;
    } catch (error) {
      console.log(error);
      openSnackbar({
        variant: 'error',
        msg: 'Something went wrong while saving permission settings data. Please contact technical support.',
      });
      return false;
    }
  };

  return (
    <PermissionsEditor
      allFeaturesList={allFeaturesListQuery.data?.features}
      orgMetaData={queryClient.getQueryData([ORGANIZATION_METADATA])}
      handleFeaturesFormSaveCallback={handleFeaturesSaveCallback}
    />
  );
}

export default ClientPage;
