if (process.env.NODE_ENV === 'development') {
  const dotenvExpand = require('dotenv-expand');
  const dotenvFlow = require('dotenv-flow');
  const path = require('path');

  /**
   * Default dotenv variables flow in Next.js doesn't support custom modes: staging, qa, etc.
   * Here we initialize our own custom dotenv-flow with dotenv-expand.
   * This is only done in development. Other env uses
   */
  const options = {
    path: path.join(process.cwd(), 'environments'),
    files: [
      '.env',
      ...(process.env.APP_ENV
        ? [`${process.env.APP_ENV}/.env.${process.env.BUILD_ENV}`]
        : [`.env.${process.env.BUILD_ENV}`]),
    ],
    // files: ['.env', `${process.env.APP_ENV}/.env.${process.env.BUILD_ENV}`],
    purge_dotenv: true, // Disabling Next.js dotenv
    //  debug: true,
  };
  const config = dotenvFlow.config(options);

  dotenvExpand.expand(config);
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  poweredByHeader: false,
  eslint: {
    // Run eslint in pre commit
    ignoreDuringBuilds: true,
  },
  logging: {
    // logging level only for development mode
    fetches: {
      fullUrl: true,
    },
  },
};

// Check for Next Output environment variable, required for containerization
if (process.env.NEXT_OUTPUT_STANDALONE === 'true') {
  nextConfig.output = 'standalone';
}

module.exports = nextConfig;
