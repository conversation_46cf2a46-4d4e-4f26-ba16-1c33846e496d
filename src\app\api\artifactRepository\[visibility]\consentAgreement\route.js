import { NextResponse } from 'next/server';
import AwsError from '@/lib/error/AwsError';
import { fetchWithMiddleware, addMachineAccessToken, addUserToken } from '@/lib/fetch/server';
import { ORGANIZATION_ID } from '@/lib/constant';

const getEndpoint = async (req) => {
  const BASE_URL = getArtifactRepoUrlByVisibility(visibility);
  if (!BASE_URL) {
    return NextResponse.json({ message: 'visibility should be private or public' }, { status: 400 });
  }

  try {
    const env = visibility === PRIVATE ? ORGANIZATION : NETWORK;
    const res = await fetchWithMiddleware(addMachineAccessToken(req, env), addUserToken(req, { replaceOrgId: true }))(
      `${BASE_URL}/organizations/${ORGANIZATION_ID}/consent-agreement`,
      {
        cache: NO_STORE,
      },
    );

    const { presignedUrl } = await res.json();

    const response = NextResponse.json({ presignedUrl }, { status: 200 });

    return response;
  } catch (err) {
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const GET = getEndpoint;
