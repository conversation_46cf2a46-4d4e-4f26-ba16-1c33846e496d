'use client';

import { SignInFlow } from '@cambianrepo/ui';
import { useRouter } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { APP_CODE_CAMBIAN } from '@/lib/constant';
import { addCsrfToken, clientFetch } from '@/lib/fetch/client';
import { MFA_TIME_OUT_IN_SECONDS, UNKNOWN_ERROR } from '@/lib/constant';

function ClientPage() {
  const router = useRouter();

  const initateSignInCallback = async ({ username, password }) => {
    try {
      const result = await clientFetch(addCsrfToken)(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/initiateSignIn`,
        {
          method: 'POST',
          body: JSON.stringify({
            username: username.toLowerCase(),
            password,
          }),
        },
      );
      console.log('RESU', result);
      try {
        const data = await result.json();
        if (result.ok) {
          return { success: true, response: data };
        }
        return { success: false, response: {}, errorMsg: data?.message };
      } catch (err) {
        if (result.status === 403) {
          return {
            success: false,
            response: {},
            errorMsg: 'Too many failed sign in attempts. Further attempts from this device will be blocked.',
          };
        } else {
          console.log('initateSignInCallback error ', err);
          return { success: false, response: {}, errorMsg: err?.message };
        }
      }
    } catch (err) {
      console.log('initateSignInCallback error ', err);
      return { success: false, response: {}, errorMsg: err?.message };
    }
  };

  const forceChangePasswordCallback = async ({ newPassword, username, session, userId }) => {
    try {
      const result = await clientFetch(addCsrfToken)(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/newPasswordRequired`,
        {
          method: 'PUT',
          body: JSON.stringify({
            session,
            userId,
            newPassword,
            usernameForMfa: username.toLowerCase(),
          }),
        },
      );

      const data = await result.json();
      if (result.ok) {
        return { success: true, response: data };
      }
      return { success: false, response: {}, errorCode: data?.errorName || UNKNOWN_ERROR };
    } catch (err) {
      console.log('cognito error ', err);
      return { success: false, errorCode: err };
    }
  };

  // one of either session or access token
  const mfaCallback = async ({ verificationCode, challengeName, username, session }) => {
    try {
      const result = await signIn('cognito-mfa', {
        verificationCode,
        username: username.toLowerCase(),
        challengeName,
        session,
        redirect: false,
        callbackUrl: '/home',
      });
      console.log('mfaCallback signin result:', result);
      if (result.ok) {
        router.push(result.url, { replace: true });
        return { success: true, response: result };
      }
      console.error({ result });
      return { success: false, errorMsg: JSON.parse(result.error).message };
    } catch (err) {
      console.log('cognito error ', err);
      return { success: false };
    }
  };

  const changePasswordCallback = async ({ username, verificationCode, newPassword }) => {
    console.table({ verificationCode, newPassword, username });

    try {
      const result = await clientFetch(addCsrfToken)(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/confirmForgotPassword`,
        {
          method: 'POST',
          body: JSON.stringify({
            email: username.toLowerCase(),
            verificationCode,
            newPassword,
          }),
        },
      );

      const data = await result.json();

      if (result.ok) {
        return { success: true, response: data };
      }

      return {
        success: false,
        errorCode: data?.errorName || UNKNOWN_ERROR,
      };
    } catch (error) {
      console.log('change password', error);

      // Handle network or unexpected errors with fallback for error code
      return {
        success: false,
        errorCode: error?.response?.data?.errorName || UNKNOWN_ERROR,
      };
    }
  };

  const forgotPasswordCallback = async ({ username }) => {
    console.table({ username });

    try {
      const result = await clientFetch(addCsrfToken)(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/forgotPassword`,
        {
          method: 'POST',
          body: JSON.stringify({
            email: username.toLowerCase(),
          }),
        },
      );

      const data = await result.json();
      if (result.ok) {
        return { success: true, response: data };
      }
      return { success: false, response: {}, errorMsg: data?.message };
    } catch (error) {
      return { success: false, errorMsg: error.response.data.message };
    }
  };

  if (process.env.NEXT_PUBLIC_APP_CODE == APP_CODE_CAMBIAN) {
    return (
      <SignInFlow
        signInProps={{
          signInCallback: initateSignInCallback,
          textBelowButton: '',
          linkBelowButtonText: '',
          hideSignUpSection: true,
        }}
        signUpProps={{}}
        mfaProps={{
          mfaCallback,
          mfaTimeInSeconds: MFA_TIME_OUT_IN_SECONDS,
        }}
        forceChangePasswordProps={{
          forceChangePasswordCallback: forceChangePasswordCallback,
          passwordRequirements: {
            min: 8,
            max: 20,
            requireLowercase: true,
            requireUppercase: true,
            requireNumber: true,
            requireSpecial: true,
          },
        }}
        forgotPasswordProps={{ forgotPasswordCallback }}
        verifyAndChangePasswordProps={{
          changePasswordCallback,
          resendVerificationCodeCallback: forgotPasswordCallback,
          password: {
            min: 8,
            max: 20,
            requireLowercase: true,
            requireUppercase: true,
            requireNumber: true,
            requireSpecial: true,
          },
        }}
        sharedProps={{ logoUrl: process.env.NEXT_PUBLIC_SIGN_IN_PAGES_LOGO || undefined }}
      />
    );
  } else {
    return (
      <SignInFlow
        signInProps={{
          byline: '',
          logoUrl: process.env.NEXT_PUBLIC_SIGN_IN_PAGES_LOGO,
          signInCallback: initateSignInCallback,
          textBelowButton: '',
          linkBelowButtonText: '',
          hideSignUpSection: true,
        }}
        signUpProps={{}}
        mfaProps={{
          logoUrl: process.env.NEXT_PUBLIC_SIGN_IN_PAGES_LOGO,
          mfaCallback,
          mfaTimeInSeconds: MFA_TIME_OUT_IN_SECONDS,
          title: 'Enter Code',
        }}
        forceChangePasswordProps={{
          forceChangePasswordCallback: forceChangePasswordCallback,
          passwordRequirements: {
            min: 8,
            max: 20,
            requireLowercase: true,
            requireUppercase: true,
            requireNumber: true,
            requireSpecial: true,
          },
        }}
        forgotPasswordProps={{ forgotPasswordCallback, logoUrl: process.env.NEXT_PUBLIC_SIGN_IN_PAGES_LOGO }}
        verifyAndChangePasswordProps={{
          changePasswordCallback,
          resendVerificationCodeCallback: forgotPasswordCallback,
          logoUrl: process.env.NEXT_PUBLIC_SIGN_IN_PAGES_LOGO,
        }}
        sharedProps={{ logoUrl: process.env.NEXT_PUBLIC_SIGN_IN_PAGES_LOGO || undefined }}
      />
    );
  }
}

export default ClientPage;
