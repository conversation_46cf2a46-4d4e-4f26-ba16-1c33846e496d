version: 1
frontend:
  phases:
    preBuild:
      commands:
        - |
          echo "@cambianrepo:registry=https://npm.pkg.github.com/
          //npm.pkg.github.com/:_authToken=$(aws secretsmanager get-secret-value --secret-id "/${ENV}CS/Shared/GithubToken" --query "SecretString" --output text)" > .npmrc
        - nvm use 20
        - yarn install --frozen-lockfile
        - echo $AWS_COMMIT_ID
        - echo $ENV
        - cp environments/.env .env
        - cp environments/.env.$BUILD_ENV .env.production
        - echo -e "\nNEXT_PUBLIC_AWS_COMMIT_ID=${AWS_COMMIT_ID}" >> .env.production
        - echo -e "\nNEXT_PUBLIC_AWS_ENVIRONMENT_NAME=${ENV}" >> .env.production
        - echo -e "\nNEXTAUTH_SECRET=$(aws secretsmanager get-secret-value --secret-id "/${ENV}CS/Shared/NextAuthSecret" --query "SecretString" --output text)" >> .env.production
        - echo -e "\nCOGNITO_ORG_APP_CLIENT_SECRET=$(aws secretsmanager get-secret-value --secret-id "/${ENV}CS/Organization/CF/HumanUserPoolClientSecretSecret" --query "SecretString" --output text)" >> .env.production
        - echo -e "\nCOGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET=$(aws secretsmanager get-secret-value --secret-id "/${ENV}CS/Network/CF/MachineUserPoolClientSecretSecret" --query "SecretString" --output text)" >> .env.production
        - |
          echo -e "\nCOGNITO_NETWORK_MACHINE_CREDENTIALS=$(aws secretsmanager get-secret-value \
            --secret-id "/${ENV}CS/Network/CF/${APP_ENV}CoordinatorMachineCredentials" \
            --query "SecretString" --output text)" >> .env.production
        - echo -e "\nCOGNITO_ORG_MACHINE_APP_CLIENT_SECRET=$(aws secretsmanager get-secret-value --secret-id "/${ENV}CS/Organization/CF/MachineUserPoolClientSecretSecret" --query "SecretString" --output text)" >> .env.production
        - |
          echo -e "\nCOGNITO_ORG_MACHINE_CREDENTIALS=$(aws secretsmanager get-secret-value \
            --secret-id "/${ENV}CS/Organization/CF/CoordinatorMachineCredentials" \
            --query "SecretString" --output text)" >> .env.production
        - echo -e "\nCOORDINATOR_ALLOW_COGNITO_ACCESS_IAM_ACCESS_KEY=$(aws secretsmanager get-secret-value --secret-id "/${ENV}CS/Organization/CF/CoordinatorCognitoIAMAccessKeySecret" --query "SecretString" --output text)" >> .env.production
        - echo -e "\nCOORDINATOR_ALLOW_COGNITO_ACCESS_IAM_SECRET_KEY=$(aws secretsmanager get-secret-value --secret-id "/${ENV}CS/Organization/CF/CoordinatorCognitoIAMSecretAccessKeySecret" --query "SecretString" --output text)" >> .env.production
        - |
          COMPONENT_UI_VERSION=$(grep -o '"version": *"[^"]*"' ./node_modules/@cambianrepo/ui/package.json | grep -o '"[^"]*"$' | sed 's/"//g')
          echo "export const COMPONENT_UI_VERSION = '$COMPONENT_UI_VERSION';" > src/lib/version.js
    build:
      commands:
        - yarn run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
