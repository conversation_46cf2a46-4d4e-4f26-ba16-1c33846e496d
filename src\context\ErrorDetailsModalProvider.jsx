import { createContext, useContext, useEffect, useState } from 'react';
import { Dialog, DialogTitle, DialogContent, Typography, Button } from '@mui/material';
import { TextField } from '@mui/material';
import { server_orgMessaging } from '@/actions/orgMessaging';

const ErrorDetailsModalContext = createContext();

export const useErrorDetailsModal = () => useContext(ErrorDetailsModalContext);

export const ErrorDetailsModalProvider = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [errorDetails, setErrorDetails] = useState('');
  const [errorEmailBody, setErrorEmailBody] = useState('');

  const showModal = ({ errorDetails, errorEmailBody }) => {
    setErrorDetails(errorDetails);
    setErrorEmailBody(errorEmailBody);
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
    setErrorDetails('');
    setErrorEmailBody('');
  };

  return (
    <ErrorDetailsModalContext.Provider value={{ showModal }}>
      {children}
      <Dialog open={open} onClose={hideModal}>
        <DialogTitle>Error Details</DialogTitle>
        <DialogContent>
          <Typography>Please send us this error report to help fix the problem and improve the application.</Typography>
          <TextField
            style={{ marginTop: 16, marginBottom: 16 }}
            multiline
            rows={5}
            maxRows={5}
            label="Additional Details"
            name="details"
            onChange={(event) => console.log(event.target.value)}
          />
          <Typography>Timestamp: {errorDetails.timestamp}</Typography>
          <Typography>Application name: {errorDetails.appName}</Typography>
          <Typography>Application version: {errorDetails.appVersion}</Typography>
          <Typography>Application environment: {errorDetails.appEnvironment}</Typography>
          <Typography>User Email: {errorDetails.userEmail}</Typography>
          <Typography>User ID: {errorDetails.userId}</Typography>
          <Typography>Organization ID: {errorDetails.userOrgId}</Typography>
          <Typography>User Roles: {errorDetails.userRoles}</Typography>
          <br />
          <Typography>Page URL: {errorDetails.pageUrl}</Typography>
          <Typography>Errors:</Typography>
          <Typography>API route: {errorDetails.apiRouteUrl || 'Unavailable'}</Typography>
          <Typography>Request details: {JSON.stringify(errorDetails.options) || 'Unavailable'}</Typography>
          <Typography>Response: {JSON.stringify(errorDetails.apiErrorMsg) || 'Unavailable'}</Typography>
          <br />
          {errorDetails.additionalFailedRoutes ? (
            <Typography>
              Additional routes that failed at the same time:
              {JSON.stringify(errorDetails.additionalFailedRoutes) || 'Unavailable'}
            </Typography>
          ) : (
            <></>
          )}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 16 }}>
            <Button variant="outlined" onClick={hideModal} style={{ marginRight: 8 }}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                server_orgMessaging(
                  'EMAIL',
                  process.env.NEXT_PUBLIC_HELP_DESK_EMAIL,
                  process.env.NEXT_PUBLIC_APP_NAME,
                  `Error in ${process.env.NEXT_PUBLIC_APP_NAME} ${errorDetails.appEnvironment}`,
                  errorEmailBody,
                  errorEmailBody,
                );
              }}
            >
              Send
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </ErrorDetailsModalContext.Provider>
  );
};
