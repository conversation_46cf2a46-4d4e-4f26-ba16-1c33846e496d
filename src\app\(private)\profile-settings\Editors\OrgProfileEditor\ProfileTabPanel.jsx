import { <PERSON><PERSON>, <PERSON><PERSON>, Box } from '@mui/material';
import { FormContainer, TextFieldElement } from 'react-hook-form-mui';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { FileInput } from '@cambianrepo/ui';
import IconInput from './IconInput';
import { dirtyValues } from '../../../../../lib/utility';
import * as Constants from '@/app/globalConstants';

function ProfileTabPanel({ orgMetaData, handleFormSaveCallback }) {
  const formContext = useForm({});
  const { formState } = formContext;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (orgMetaData) {
      formContext.reset({
        organizationName: orgMetaData.organizationName,
        tagline: orgMetaData.tagline,
        aboutUs: orgMetaData.aboutUs,
        address: orgMetaData.address,
        email: orgMetaData.email,
        contactPhone: orgMetaData.contactPhone,
        contactFax: orgMetaData.contactFax,
        logoSrcImg: orgMetaData.iconUrl,
        consentAgreement: orgMetaData.consentAgreementUrl,
      });
    }
  }, [orgMetaData]);

  const handleSubmit = async (orgMetaData) => {
    setLoading(true);
    try {
      const dirtyFields = { ...formState.dirtyFields };
      const dirtyData = dirtyValues(dirtyFields, orgMetaData);
      await handleFormSaveCallback({
        allValues: orgMetaData,
        dirtyValues: dirtyData,
      });
    } catch (error) {
      console.error('Error saving data:', error);
    } finally {
      setLoading(false);
    }
  };

  const isFormChanged = () => Object.keys(formState.dirtyFields).length > 0;

  return (
    <FormContainer onSuccess={handleSubmit} formContext={formContext}>
      <Stack spacing={Constants.formFieldSpacing}>
        <TextFieldElement label="Name" name="organizationName" required />
        <TextFieldElement label="Tagline" name="tagline" required />
        <TextFieldElement multiline maxRows={8} label="About Us" name="aboutUs" required />
        <TextFieldElement label="Address" name="address" required />
        <TextFieldElement label="Email" name="email" type="email" />
        <TextFieldElement
          label="Phone Number"
          name="contactPhone"
          validation={{
            pattern: {
              value: /^[0-9\-+() ]+$/,
              message: 'Please enter a valid phone number format',
            },
          }}
        />
        <TextFieldElement
          label="Fax Number"
          name="contactFax"
          validation={{
            pattern: {
              value: /^[0-9+\-() ]{7,20}$/,
              message: 'Enter a valid fax number',
            },
          }}
        />
        <FileInput
          accept="application/pdf"
          typographyProps={{ text: 'Consent Agreement PDF', paddingLeft: 0.3 }}
          inputName="consentAgreement"
          formContext={formContext}
        />
        <IconInput
          accept="image/*"
          maxWidth="300px"
          typographyProps={{ text: 'Upload Logo', paddingLeft: 0.3 }}
          inputName="logoSrcImg"
          useControllerProps={{ rules: 'required' }}
        />
        <Box width="auto" paddingTop={2} marginTop={2}>
          <Button variant="contained" type="submit" disabled={loading || !isFormChanged()}>
            Save
          </Button>
        </Box>
      </Stack>
    </FormContainer>
  );
}

export default ProfileTabPanel;
