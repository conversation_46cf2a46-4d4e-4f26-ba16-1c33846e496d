'use client';
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID, ORGANIZATION_USER_ID } from '@/lib/constant';

const UserIconContext = createContext();

export const useUserIcon = () => useContext(UserIconContext);

export const UserIconProvider = ({ children }) => {
  const { data: session } = useSession();
  const [iconUrl, setIconUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState({
    firstName: '',
    lastName: '',
    email: '',
  });

  const updateUserData = (newData) => {
    setUserData((prev) => ({ ...prev, ...newData }));
  };

  const fetchUserIcon = useCallback(async () => {
    if (!session?.user?.orgUserId) return;
    setIsLoading(true);
    try {
      const response = await fetchNextRoute(
        'organizationData',
        `/organizations/${ORGANIZATION_ID}/users/${ORGANIZATION_USER_ID}/icon`,
        { errorsToIgnore: [404] },
      );
      if (response.status === 404) {
        setIconUrl(null);
        return;
      }
      const responseData = await response.json();
      setIconUrl(responseData.iconUrl || null);
    } catch (error) {
      if (error.status !== 404) {
        console.error('Failed to fetch user icon', error);
      }
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.orgUserId]);

  useEffect(() => {
    fetchUserIcon();
  }, [fetchUserIcon]);

  const refetchIcon = useCallback(() => {
    return fetchUserIcon();
  }, [fetchUserIcon]);

  return (
    <UserIconContext.Provider value={{ userData, updateUserData, iconUrl, setIconUrl, refetchIcon, isLoading }}>
      {children}
    </UserIconContext.Provider>
  );
};
