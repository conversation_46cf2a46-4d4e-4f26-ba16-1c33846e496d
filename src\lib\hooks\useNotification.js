import { useEffect, useRef, useState } from 'react';
import { Typography, IconButton, useTheme } from '@mui/material';
import { Clear } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useErrorDetailsModal } from '@/context/ErrorDetailsModalProvider';
import { setOpenSnackbar } from '@/lib/fetch/client';
import { setOpenSnackbar as setOpenSnackbarForClientFetchRoutes } from '@/lib/api/services/clientFetchRoutes';
import { usePermissions } from '@/context/UserPermissions';
import { useSession } from 'next-auth/react';

const useNotification = () => {
  const [notifications, setNotifications] = useState([]);
  const bufferRef = useRef([]);
  const timerRef = useRef(null);
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();
  const { showModal } = useErrorDetailsModal();
  const theme = useTheme();
  const { roles } = usePermissions();
  const { data: session } = useSession();

  const action = (key) => (
    <IconButton
      onClick={() => {
        closeSnackbar(key);
      }}
    >
      <Clear fontSize="small" sx={{ color: '#fff' }} />
    </IconButton>
  );

  useEffect(() => {
    // Display the first notification in the array
    if (notifications.length > 0) {
      const conf = notifications[0];
      if (conf?.msg) {
        let variant = 'info';
        if (conf.variant) {
          variant = conf.variant;
        }
        let errorEmailBody;
        if (variant === 'error' && conf.details) {
          for (let i = 1; i < notifications.length; i++) {
            conf.details.additionalFailedRoutes = conf.details.additionalFailedRoutes
              ? [...conf.details.additionalFailedRoutes, notifications[i].details.apiRouteUrl]
              : [notifications[i].details.apiRouteUrl];
          }
          conf.details = {
            appName: process.env.NEXT_PUBLIC_APP_NAME,
            appVersion: process.env.NEXT_PUBLIC_AWS_COMMIT_ID
              ? 'NEXT_PUBLIC_AWS_COMMIT_ID ' + process.env.NEXT_PUBLIC_AWS_COMMIT_ID
              : 'NEXT_PUBLIC_BUILD_SCRIPT_COMMIT_ID ' + process.env.NEXT_PUBLIC_BUILD_SCRIPT_COMMIT_ID,
            appEnvironment: process.env.NEXT_PUBLIC_AWS_ENVIRONMENT_NAME || process.env.NEXT_PUBLIC_BUILD_ENV,
            pageUrl: process.env.NEXT_PUBLIC_DEPLOYMENT_HOST + window.location.pathname,
            userEmail: session?.user.email,
            userId: session?.user.orgUserId,
            userOrgId: session?.user.orgId,
            // userRoles: roles.map((role, idx) => role.name + (idx !== roles.length - 1 ? ', ' : '')),
            userRoles: roles.reduce(
              (rolesString, role, idx) => rolesString + role.name + (idx !== roles.length - 1 ? ', ' : ''),
              '',
            ),
            ...conf.details,
          };
          errorEmailBody = `
          <h5>Basic information</h5>
          <p>Timestamp: ${conf.details.timestamp}</p>
          <p>Application Name: ${conf.details.appName}</p>
          <p>Application Version: ${conf.details.appVersion}</p>
          <p>Application Environment: ${conf.details.appEnvironment}</p>
          <p>User Email: ${conf.details.userEmail}</p>
          <p>User ID: ${conf.details.userId}</p>
          <p>Organization ID: ${conf.details.userOrgId}</p>
          <p>User's role(s): ${conf.details.userRoles}</p>
          <p>Page URL: ${conf.details.pageUrl}</p>
          <h5>Error(s) and request(s) details</h5>
          <p>API route: ${conf.details.apiRouteUrl || 'Unavailable'}</p>
          <p>Request details: ${JSON.stringify(conf.details.options) || 'Unavailable'}</p>
          <p>API response: ${JSON.stringify(conf.details.apiErrorMsg) || 'Unavailable'}</p>
          ${
            conf.details.additionalFailedRoutes
              ? `<p>Additional routes that failed at the same time: ${JSON.stringify(conf.details.additionalFailedRoutes) || 'Unavailable'}</p>`
              : ''
          }`;
        }
        enqueueSnackbar(
          <>
            <Typography sx={{ pl: 1 }}>{conf.msg}</Typography>
            {variant === 'error' && conf.details ? (
              <>
                <Typography
                  sx={{ pl: 1, textDecoration: 'underline' }}
                  onClick={() => {
                    server_orgMessaging(
                      'EMAIL',
                      process.env.NEXT_PUBLIC_HELP_DESK_EMAIL,
                      process.env.NEXT_PUBLIC_APP_NAME,
                      `Error in ${process.env.NEXT_PUBLIC_APP_NAME} ${errorDetails.appEnvironment}`,
                      errorEmailBody,
                      errorEmailBody,
                    );
                  }}
                >
                  Send Report
                </Typography>
                <Typography
                  sx={{ pl: 1, textDecoration: 'underline' }}
                  onClick={() =>
                    showModal({
                      errorDetails: conf.details || 'No details provided',
                      errorEmailBody: errorEmailBody || 'No details provided',
                    })
                  }
                >
                  See Details
                </Typography>
              </>
            ) : (
              <></>
            )}
          </>,
          {
            variant,
            autoHideDuration: 6000,
            action,
            style: variant === 'info' ? { backgroundColor: theme.palette.primary.main } : {},
          },
        );

        setNotifications([]);
      }
    }
  }, [notifications]);

  const openSnackbar = (conf) => {
    if (conf.details) {
      console.log('!!! openSnackbar with details', { conf });
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      bufferRef.current.push(conf);
      // Start a new timer: after 1 second of inactivity,
      // set new notification and flush the buffer
      timerRef.current = setTimeout(() => {
        setNotifications((prev) => [...prev, ...bufferRef.current]);
        bufferRef.current = [];
        timerRef.current = null;
      }, 1000);
    } else {
      console.log('!!! openSnackbar without details', { conf });
      setNotifications((prev) => [...prev, conf]);
    }
  };

  // const openSnackbar = (conf) => {
  //   setNotifications((prevNotifications) => [...prevNotifications, conf]);
  // }

  setOpenSnackbar(openSnackbar);
  setOpenSnackbarForClientFetchRoutes(openSnackbar);

  return openSnackbar;
};

export default useNotification;
