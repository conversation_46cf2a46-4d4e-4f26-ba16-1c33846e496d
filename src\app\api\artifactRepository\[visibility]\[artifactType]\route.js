import { NextResponse } from 'next/server';
import AwsError from '@/lib/error/AwsError';
import {
  ARTICLES,
  ADS,
  PLANS,
  NEWS_ITEMS,
  NO_STORE,
  QUESTIONNAIRES,
  ORGANIZATION_ID,
  PRIVATE,
  ORGANIZATION,
  NETWORK,
} from '@/lib/constant';
import { getArtifactRepoUrlByVisibility } from '@/lib/utility';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const getArtifactList = async (req, { params: { visibility, artifactType } }) => {
  const BASE_URL = getArtifactRepoUrlByVisibility(visibility);
  if (!BASE_URL) {
    return NextResponse.json({ message: 'visibility should be private or public' }, { status: 400 });
  }

  const validArtifactTypes = [QUESTIONNAIRES, NEWS_ITEMS, ARTICLES, PLANS, ADS];
  if (!validArtifactTypes.includes(artifactType)) {
    return NextResponse.json({ message: 'Unsupported Artifact Type' }, { status: 400 });
  }

  const queryParams = new URL(req.url).searchParams;
  const contentStatus = queryParams.get('contentStatus');
  const publishStatuses = queryParams.getAll('publishStatus');

  const queryStrings = [];
  if (contentStatus) {
    queryStrings.push(`contentStatus=${contentStatus}`);
  }
  if (publishStatuses.length > 0) {
    queryStrings.push(`publishStatus=${publishStatuses.join(',')}`);
  }

  const finalQueryString = queryStrings.length > 0 ? `?${queryStrings.join('&')}` : '';

  try {
    const env = visibility === PRIVATE ? ORGANIZATION : NETWORK;
    const res = await fetchWithMiddleware(addMachineAccessToken(req, env), addUserToken(req, { replaceOrgId: true }))(
      `${BASE_URL}/organizations/${ORGANIZATION_ID}/${artifactType}${finalQueryString}`,
      {
        cache: NO_STORE,
      },
    );
    const responseBody = await res.json();

    const response = NextResponse.json({ responseBody }, { status: res.status });
    return response;
  } catch (err) {
    console.log(err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const GET = getArtifactList;
