# Coordinator V2

## Git Repo Branch
- staging: A default branch and the branch for staging environment
- main: A branch for production environment

## Project Structure
This project follows standard Next.js App Router projects with src directory.

TODO: Editor Components like Profile Editor are to be imported from a separate repository.

* `src/app/(private)`: private routes
* `src/app/(public)` : public routes
* `src/app/api` : Next.js Route Handlers (formerly known as API Routes in Next.js Page Router)
* `src/components` : Shared React Components
* `src/lib`: Shared utility Javascript code
* `aws/iam` : SAM template for IAM policies for Coordinator Next.js server machine

## Running Project
1. Use Node.js 20
2. `yarn install`
3. `yarn local` (prerequisite: have environments/.env.local)

## environments/.env.local setup:
Duplicate the .env.... file of the environment to be ran, (for example: .env.dev) and rename it to .env.local
Then change NEXT_PUBLIC_DEPLOYMENT_HOST value to http://localhost:3010 or whatever the yarn local in package.json specifies.
Then add the secrets to the file (!! never commit the secrets).

## env Secrets
For local development or manual builds, the secrets could either be retrieved from AWS Secrets Manager or a AWS Amplify build artifact and then added to the end of the env file being used.
More instructions for secrets retrieval could be found here: http://**************:8090/display/CS2/V2+Development+Set+Up

For automatic builds with Amplify (or other tools), Secrets are to be injected during preBuild phase, refer to the Amplify template in cambian-services-v2 repo.
Secrets with /CF/ in their names are created via CloudFormation, the rest are manually created in the AWS environment.
(except for COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET or /${ENV}CS/Network/CF/MachineUserPoolClientSecretSecret because the User Pool is created only in the corresponding cambian environment, so in pcn environments, we'd need to create them manually)

## New env var setup
* Removed .env files in root directory.
* environments/[APP_ENV]/.env.(developmenmt|staging|dev|etc) : Org or Aws account specific env var
* environments/shared/.env.* : Main shared cambian AWS account
* environments/cambian/.env.* : For cambian-org-dev aws account (Not ready)

* environments/.env : shared environment for all env like header test

package.json has scripts for each org account. For development, `next.config.js` is configured so it reads the environment var from the new env file locations using third party libraries related to dot-env.
For every other environments, `amplify.yml` is configured to copy the .env files from environments to the root diretory of the project so Next.js can pick up then env vars with its built in mechanism. The difference is so there so that amplify does not need to download third party library. Copying over the file in development could cause confusion as the developer will see 2 same env file in our file directory. 


* `yarn dev:shared`: Uses `environments/.env` and `environments/shared/.env.development`. This is the command to use for our main shared account.

#### The caveat is that updating env var will not auto update the env var values in the running Next.js instance. In development, you need to r updating env var will not auto update the env var values in the running Next.js instance. In development, you need to restart the app every time an .env.* files are changed.. 

## Fetching data
A fetchWithMiddleware function is used for both server and client to run middleware like adding auth token. Client and server uses a different module.

`axios` has interceptors where one can intercept the network requests to perform actions like adding tokens. `fetch` API doesn't have a built-in one. Created this function for it.

An example usage of native fetch
```
fetch("api/url", { method: "GET"})
```
An example usage of fetchWithMiddlware
```
fetchWithMiddlware(addMachineAccessToken(), addCookie)("/api/url", { method: "GET"}) 
```
Implemented Middlwares:
1. addMachineAccessToken(env)  : Add Network or Org machine access token to "Machine-Token" header.
2. addUserToken(req, { replaceOrgId, replaceOrgUserId}) : Add user id token to "User-Token" header. Add user id token to "Authorization" header to support deprecated MultipoolAuthorizer in shared account.
3. addCookie: Add Cookie to request when the request is made from server component.
4. addCsrfToken: Add csrf token to request. Currently used for sign in related apis.

Usages:
1. ABPCN team hasn't been using route handler and RSC. This would be the flow for them.
![image](https://github.com/cambianrepo/coordinator/assets/********/f60bf584-ba55-4561-bdab-8add62c2c461)


2. Coordinator team has been using route handler and RSC. I've commented out the RSC one for now due to Caveat 1 and 2. It's still possible to use but I've commented out for now. For new changes, I think we can just follow ABPCN flow.
![image](https://github.com/cambianrepo/coordinator/assets/********/1c76bb8b-b6df-4bd0-be21-7716bd89ca60)


### Caveat
1. You need to make sure you use fetch/client module in client side and fetch/server module in server side (RSC & Route handler). I tried making a single module that will choose one automatically for us by determining client side with `typeof window === 'undefined` but that didn't work. I think it's because client component also needs to be run in the server partially during build or run time and window is not defined there. Welcome to suggestion.
2. The first one is a huge caveat, and this makes a module that calls fetchWithMiddlware hard to be shared between RSC and client component. For example, `getArtifact` function get an artifact from Artifact Repo. It used to be called in both RSC and client component. Now we can't shared it.
3. addMachineAccessToken and addUserToken are closure functions. You need to initialize the function before using it as a param in fetchWithMiddlware. It will throw error if not. 
4. The access tokens are saved in cookie `{env}-machine-access-token` but at the same time added in the header. This bloats the request a lot.


### Tried fixing caveat 1 and 2
Machine access token is saved in a cookie. In the custom fetch for client, browser APIs(window, document) are needed. For server, server only code like `next/headers` is needed.

Due to different implementations we sometimes invoke AWS API endpoints in either client component, server component, and route handler sometimes via "service" modules or using fetch directly. Client component needs the client custom fetch and other needs the server custom fetch. Due to this, it is not possbile to share the "service" module like clientIndexService in both server and client.

To be able to share "service" module, I tried following methods that all failed.

1. Create a single module that conditionally imports client or server custom fetch on runtime using dynamic import. I tried using `window` object to determine client and server. I also tried using a parameter to determine client and server.

```javascript
// const isBrowserEnvrionment= typeof window !== "undefined"
export const loadModules = async (isBrowserEnvironment = true) => {
  let fetchModule;
  if (isBrowserEnvironment) {
    fetchModule = await import('./client');
  } else {
    try {
      fetchModule = await import('./server');
    } catch (serverError) {
      console.log('Server module import failed:', serverError);
      fetchModule = await import('./client');
    }
  }
  return fetchModule;
};
```

2. Create an empty single module. Edit webpack config to resolve path dynamically using `isServer` and `nextRuntime` variables in `next.config.js`.

```javascript
const path = require('path');
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Run eslint in pre commit
    ignoreDuringBuilds: true,
  },
  webpack: (config, { buildId, dev, isServer, defaultLoaders, nextRuntime, webpack }) => {
    config.plugins.push(
      new webpack.NormalModuleReplacementPlugin(/fetch\/main/, (resource) => {
        resource.request = resource.request.replace(
          /main/,
          isServer && nextRuntime !== undefined ? 'server' : 'client',
        );
      }),
    );
    return config;
  },
};

module.exports = nextConfig;
```

#### Both options didn't work. 
Both failed when compiling Client Component. I was expecting client component to use custom fetch for client with the conditions above. However, I think because the Client component is also built in server first, the condition actually refers it as a server environment. For example, `window` is not available during compilation.

After this, I tried disabling SSR on the components that needs the custom fetch. I did this with these approaches. You can read more here; https://medium.com/@eric.burel/how-to-get-rid-of-window-is-not-defined-and-hydration-mismatch-errors-in-next-js-567cc51b4a17 

1. Using dynamic import API from `next/dynamic` and ssr flag to false
2. Using custom useMounted hook.

#### Both didn't work.
Thus, we cannot create a shared module for custom fetch. If we want to use "service" module, we need one for client and one for server. 

## Deployment
Amplify detects new changes from a connected branch and run the pipeline to deploy new instance. `amplify.yml` contains commands and steps to be performed during build and deployment.


### Supported Environments and their variables:
Note that these environment variables are injected in built time and then hardcoded by default. Runtime variable in Server Component has built-in support in Next.js App Router. For anywhere else like in the Client Component, it is recommended that the data to be pulled from an API instead.

### Limitation with built-in Next.js environment support
Supported environments are development, test, and production. When building and running the project, Next.js detects enviornment variables from files like `.env.development`, `.env.test`, `.env.production`, `.env`, and `.env.local`. It can't read from a custom environment file like `.env.staging`. Moreover, when we run `next build`, `NODE_ENV` will always be `production`. However, we want to use staging and other environments.

## Our Development Process:
1. staging is our default branch.
2. Branch off from staging for your feature.
3. Once the change is ready, create a PR to merge into staging.
4. Once PR is reviewed, merge it, and wait for the Amplify CI/CD to finish. Check Amplify console to see the progress.
5. If the pipeline succeeds, test your change using the Staging environment.

Later when we want to deploy Prod version, create a PR to merge into prod.
