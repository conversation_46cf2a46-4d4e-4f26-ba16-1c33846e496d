import { NextResponse } from 'next/server';
import { handler } from '@/lib/middleware/handler';
import { csrfProtected } from '@/lib/middleware/csrfProtected';
import { revokeToken } from '@/lib/auth/cognito'; // Import the revokeToken function
import AwsError from '@/lib/error/AwsError';
import { NETWORK, ORGANIZATION, MACHINE_REFRESH_TOKEN } from '@/lib/constant';
import { getToken } from 'next-auth/jwt';

const postEndpoint = async (req) => {
  try {
    const token = await getToken({ req });
    const revokeTokensParams = [
      {
        refreshToken: token.refreshToken,
        clientId: process.env.COGNITO_ORG_APP_CLIENT_ID,
        clientSecret: process.env.COGNITO_ORG_APP_CLIENT_SECRET,
      },
      {
        refreshToken: token[MACHINE_REFRESH_TOKEN(NETWORK)],
        clientId: process.env.COGNITO_NETWORK_MACHINE_APP_CLIENT_ID,
        clientSecret: process.env.COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET,
      },
      {
        refreshToken: token[MACHINE_REFRESH_TOKEN(ORGANIZATION)],
        clientId: process.env.COGNITO_ORG_MACHINE_APP_CLIENT_ID,
        clientSecret: process.env.COGNITO_ORG_MACHINE_APP_CLIENT_SECRET,
      },
    ];

    console.log({ revokeTokensParams });

    const tokenResults = await Promise.all(revokeTokensParams.map(revokeToken));

    for (const { error } of tokenResults) {
      if (error) {
        console.log('revokeToken()', { error });
        return error.toNextResponse();
      }
    }
    const response = NextResponse.json({}, { status: 200 });

    return response;
  } catch (error) {
    console.error('Error revoking token:', error);
    const awsError = new AwsError(error);
    return awsError.toNextResponse();
  }
};

export const POST = handler(csrfProtected(['POST']), postEndpoint);
