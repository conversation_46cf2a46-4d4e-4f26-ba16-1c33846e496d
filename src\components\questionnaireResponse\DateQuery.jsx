import { <PERSON>, Container, Grid, Di<PERSON>r, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from '@mui/material';
import TextField from '@mui/material/TextField';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import Autocomplete from '@mui/material/Autocomplete';
import React from 'react';
import Button from '@mui/material/Button';
import { format } from 'date-fns';
import { ResultsTable } from './ResultsTable/ResultsTable';
import { ResultsPerPage } from './ResultsPerPage/ResultsPerPage';
import { DoublePanelBorder } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';
import CircularProgress from '@mui/material/CircularProgress';
import { useTranslation } from 'react-i18next';
import { usePathname } from 'next/navigation';
import dayjs from 'dayjs';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID } from '@/lib/constant';

const DateQuery = ({ dateRangeData, resultsPerPageData, headCells, showResultsPerPage }) => {
  const today = dayjs();
  const [startDate, setStartDate] = React.useState(today.subtract(7, 'day'));
  const [endDate, setEndDate] = React.useState(today);
  const [dateRange, setDateRange] = React.useState('Last 7 Days');
  const [error, setError] = React.useState('');
  const [resultsPerPage, setResultsPerPage] = React.useState(50);
  const [queriedTableRecords, setQueriedTableRecords] = React.useState([]);
  const [patientInfo, setPatientInfo] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [noQRfound, setNoQRFound] = React.useState(false);
  const [errorSnackbarOpen, setErrorSnackbarOpen] = React.useState(false);
  const [errorSnackbarMessage, setErrorSnackbarMessage] = React.useState('');
  const { t } = useTranslation();
  const [totalRecords, setTotalRecords] = React.useState(0);
  const [nextPage, setNextPage] = React.useState('');
  const [previousPage, setPreviousPage] = React.useState('');
  const [selfPage, setSelfPage] = React.useState('');
  const [page, setPage] = React.useState(0);
  const [selectedResultsPerPage, setSelectedResultsPerPage] = React.useState(50);
  const pathname = usePathname();

  React.useEffect(() => {
    if (pathname && pathname.includes('/questionnaire-responses')) {
      const storedSearchParams = sessionStorage.getItem('searchParams');
      if (storedSearchParams) {
        const { dateRange, startDate, endDate, resultsPerPage } = JSON.parse(storedSearchParams);
        setDateRange(dateRange);
        setStartDate(startDate);
        setEndDate(endDate);
        setResultsPerPage(resultsPerPage);
      }
    }
  }, [pathname]);

  React.useEffect(() => {
    if (startDate !== null && endDate !== null && resultsPerPage !== null) {
      onSubmit();
    }
  }, [startDate, endDate, resultsPerPage]);

  function displayErrorMessage(message) {
    setErrorSnackbarOpen(true);
    setErrorSnackbarMessage(message);
  }

  const handleDateRangeSelect = (event, newValue) => {
    if (newValue) {
      let dates = newValue.getValue();
      setDateRange(newValue.label);
      setStartDate(dates[0]);
      setEndDate(dates[1]);
    } else {
      setDateRange(null);
      setStartDate(null);
      setEndDate(null);
      setQueriedTableRecords([]);
    }
  };

  const handleStartDateChange = (newCollectedData) => {
    try {
      setStartDate(newCollectedData);
      setDateRange('Custom');
      if (error.startDate) {
        setError('Error in setting start date');
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleEndDateChange = (newCollectedData) => {
    try {
      setEndDate(newCollectedData);
      setDateRange('Custom');
      if (error.endDate) {
        setError('Error in setting end date');
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleResultsPerPageSelect = (event, newValue) => {
    if (newValue) {
      setResultsPerPage(newValue);
    } else {
      setResultsPerPage(null);
    }
  };

  const onClear = () => {
    setPage(0);
    setDateRange(null);
    setStartDate(null);
    setEndDate(null);
    setTotalRecords(0);
    setResultsPerPage(null);
    setQueriedTableRecords([]);
    setSelectedResultsPerPage('');
    setNextPage('');
    setPreviousPage('');
  };

  const onSubmit = async () => {
    var start = format(new Date(startDate), 'yyyy-MM-dd');
    var end = format(new Date(endDate), 'yyyy-MM-dd');
    var patientInfoRecords = [];
    if (endDate < startDate) {
      setError('End date must be greater than start date');
      return;
    }

    if (endDate === null || startDate === null) {
      setError('Please fill in both date fields');
      return;
    }

    if (!resultsPerPage) {
      setError('Please select results per page');
      return;
    }

    setError('');
    setLoading(true);
    try {
      var sorting = '-authored';
      let resultsPerPageCount;

      if (resultsPerPage.label && resultsPerPage.label === 'All') {
        resultsPerPageCount = 1000;
      } else {
        resultsPerPageCount = resultsPerPage;
      }

      let queryString =
        '' +
        'authored=ge' +
        start +
        '&' +
        'authored=le' +
        end +
        '&status=completed&_include=QuestionnaireResponse:subject&_include=QuestionnaireResponse:author&_include=QuestionnaireResponse:source&_count=' +
        resultsPerPageCount +
        '&_total=accurate';
      if (sorting) {
        queryString = queryString + '&_sort=' + sorting;
      }

      const response = await fetchNextRoute(
        'organizationCDR',
        `/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse?${queryString}`,
      );
      const dataResponse = await response.json();
      const data = dataResponse;

      if (!data || !data.entry || data.entry.length === 0) {
        setNoQRFound(true);
        setLoading(false);
        return;
      }

      var filteredTableRecords = data.entry.filter((record) => {
        if (record.resource.resourceType === 'QuestionnaireResponse') {
          return true;
        } else {
          patientInfoRecords.push(record);
        }
      });
      for (const link of data.link) {
        let url = link.url;
        const [baseUrl, queryString] = url.split('?');
        if (baseUrl.includes('/fhir/') && queryString && queryString.includes('_getpages')) {
          url = `/fhir?${queryString}`;
        }
        if (link.relation === 'next') {
          setNextPage(url);
        } else if (link.relation === 'previous') {
          setPreviousPage(url);
        } else if (link.relation === 'self') {
          setSelfPage(url);
        }
      }
      setPage(0);
      setSelectedResultsPerPage(resultsPerPage);
      setTotalRecords(data.total);
      setQueriedTableRecords(filteredTableRecords);
      setNoQRFound(false);
      setPatientInfo(patientInfoRecords);
      setLoading(false);
      const searchParams = {
        dateRange,
        startDate,
        endDate,
        resultsPerPage,
      };
      sessionStorage.setItem('searchParams', JSON.stringify(searchParams));
    } catch (err) {
      console.log(err);
      setLoading(false);
      setNoQRFound(true);
      displayErrorMessage(t('Error : Could not fetch Questionnaire Responses'));
    }
  };

  return (
    <>
      <Grid container direction="row" justifyContent="center">
        <Box
          component={Grid}
          item
          xs={0}
          sm={0}
          md={0}
          lg={1}
          xl={1}
          display={{ xs: 'none', sm: 'none', md: 'none', lg: 'none' }}
        />

        <Box
          component={Grid}
          item
          xs={12}
          sm={12}
          md={3}
          lg={3}
          xl={3}
          display={{ xs: 'block' }}
          sx={{
            paddingRight: { md: 1 },
            marginBottom: { xs: 0.5, sm: 1, md: 0 },
          }}
        >
          <PanelBorder>
            <Container maxWidth={false} disableGutters={true}>
              <Grid container spacing={4} sx={{ p: 2 }}>
                <Grid item xs={12} md={12} lg={12}>
                  <Autocomplete
                    disablePortal
                    id="combo-box-demo"
                    options={dateRangeData}
                    onChange={(event, newValue) => {
                      handleDateRangeSelect(event, newValue);
                    }}
                    value={dateRange ? { label: dateRange } : null}
                    isOptionEqualToValue={(option, value) => option.label === value.label}
                    renderInput={(params) => <TextField {...params} sx={{ mt: 1 }} label="Date Range" />}
                  />
                </Grid>
                <Grid item xs={12} md={12} lg={12} xl={6}>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      size="small"
                      label="Start Date"
                      format="YYYY-MM-DD"
                      fullWidth={true}
                      value={dayjs(startDate)}
                      onChange={(newValue) => {
                        handleStartDateChange(newValue);
                      }}
                      slotProps={{
                        textField: {
                          sx: {
                            mt: 1,
                            '& .MuiInputBase-root': {
                              paddingRight: '10px',
                            },
                          },
                          error: false,
                          placeholder: 'Test',
                          helperText: '',
                          autoComplete: 'off',
                        },
                      }}
                      slots={{
                        textField: TextField,
                      }}
                    />
                  </LocalizationProvider>
                </Grid>
                <Grid item xs={12} md={12} lg={12} xl={6}>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      size="small"
                      label="End Date"
                      format="YYYY-MM-DD"
                      fullWidth={true}
                      value={dayjs(endDate)}
                      onChange={(newValue) => {
                        handleEndDateChange(newValue);
                      }}
                      slotProps={{
                        textField: {
                          sx: {
                            mt: 1,
                            '& .MuiInputBase-root': {
                              paddingRight: '10px',
                            },
                          },
                          error: false,
                          placeholder: 'Test',
                          helperText: '',
                          autoComplete: 'off',
                        },
                      }}
                      slots={{
                        textField: TextField,
                      }}
                    />
                  </LocalizationProvider>
                </Grid>
                {showResultsPerPage && (
                  <Grid item xs={12} md={12} lg={12}>
                    <ResultsPerPage
                      resultsPerPage={resultsPerPage}
                      handleResultsPerPageSelect={handleResultsPerPageSelect}
                      resultsPerPageData={resultsPerPageData}
                    />
                  </Grid>
                )}
                {error ? (
                  <Grid item xs={12} md={12} lg={12} sx={{ paddingTop: 4 }}>
                    <Typography color="red">{error}</Typography>
                  </Grid>
                ) : (
                  ''
                )}
              </Grid>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  p: 1,
                  m: 1,
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                }}
              >
                <Stack direction={'row'} spacing={2}>
                  <Button variant="outlined" autoFocus onClick={onClear}>
                    Clear
                  </Button>
                  <Button variant="contained" autoFocus onClick={onSubmit}>
                    Submit
                  </Button>
                </Stack>
              </Box>
            </Container>
          </PanelBorder>
        </Box>

        <Box
          component={Grid}
          item
          xs={12}
          sm={12}
          md={9}
          lg={9}
          xl={9}
          display={{ xs: 'block' }}
          sx={{
            paddingLeft: { md: 1 },
            marginTop: { xs: 0.5, sm: 1, md: 0 },
          }}
        >
          {loading && (
            <DoublePanelBorder>
              <div>
                <Box
                  sx={{ display: 'flex' }}
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  minHeight="100vh"
                >
                  <CircularProgress />
                </Box>
              </div>
            </DoublePanelBorder>
          )}
          {noQRfound && (
            <DoublePanelBorder>
              <Typography>{t('No questionnaire response data')}</Typography>
            </DoublePanelBorder>
          )}
          {!loading && !noQRfound && queriedTableRecords.length > 0 && (
            <DoublePanelBorder>
              <Container maxWidth={false} disableGutters={true}>
                <ResultsTable
                  queriedTableRecords={queriedTableRecords}
                  patientInfo={patientInfo}
                  headCells={headCells}
                  totalRecords={totalRecords}
                  nextPage={nextPage}
                  setNextPage={setNextPage}
                  previousPage={previousPage}
                  setPreviousPage={setPreviousPage}
                  selfPage={selfPage}
                  setSelfPage={setSelfPage}
                  page={page}
                  setPage={setPage}
                  setQueriedTableRecords={setQueriedTableRecords}
                  setPatientInfo={setPatientInfo}
                  selectedResultsPerPage={selectedResultsPerPage}
                  startDate={startDate}
                  endDate={endDate}
                />
              </Container>
            </DoublePanelBorder>
          )}
        </Box>

        <Box
          component={Grid}
          item
          xs={0}
          sm={0}
          md={0}
          lg={1}
          xl={1}
          display={{ xs: 'none', sm: 'none', md: 'none', lg: 'block' }}
        />
      </Grid>
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        open={errorSnackbarOpen}
        autoHideDuration={6000}
        onClose={() => setErrorSnackbarOpen(false)}
      >
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => setErrorSnackbarOpen(false)}>
              {t('Dismiss')}
            </Button>
          }
        >
          {errorSnackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export { DateQuery };
