import { NO_STORE, ORGANIZATION_ID } from '../constant';
import { fetchNextRoute } from './services/clientFetchRoutes';

export const loadOrganization = async () => {
  const response = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}`);
  const data = await response.json();
  return data;
};

export const getAllFeaturesList = async () => {
  const response = await fetchNextRoute('organizationData', `/features`);
  const data = await response.json();
  return data;
};

export const getAllIdTypes = async () => {
  const response = await fetchNextRoute('organizationData', `/id-types`);
  const data = await response.json();
  return data;
};

export const getOrgSettings = async () => {
  const response = await fetchNextRoute(
    'organizationData',
    `/organizations/${ORGANIZATION_ID}/organization-configuration`,
  );
  const data = await response.json();
  return data;
};

export const getOrgViews = async () => {
  const response = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/view-settings`);
  const data = await response.json();
  return data;
};
