import React, { useState } from 'react';
import ClientInformationPanel from './ClientInformationPanel';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';
import { CambianTooltip } from '@cambianrepo/ui';
import { ShowHelperTextFile } from './ShowHelperTextFile';
import { Box, Modal, IconButton, Stack } from '@mui/material';
import { Help } from '@mui/icons-material';

export function ClientSettingsEditor({ orgMetaData, allIdTypes, handleClientInformationFormSaveCallback }) {
  const [orgIdTypes, setOrgIdTypes] = React.useState([]);
  const [openHelperTextFile, setOpenHelperTextFile] = useState(false);

  const onClose = () => {
    setOpenHelperTextFile(false);
  };

  const ModalStyle = {
    margin: 'auto',
    marginTop: '20px',
    maxWidth: '80%',
    borderRadius: '10px',
    maxHeight: '80vh',
    overflowY: 'scroll',
  };

  React.useEffect(() => {
    if (allIdTypes && orgMetaData?.idTypes && allIdTypes.length > 0) {
      const newIdTypes = [];
      allIdTypes.map((idType) => {
        const currentOrgIdType = orgMetaData?.idTypes.find((orgIdType) => orgIdType.idType === idType.idType);
        newIdTypes.push({
          ...idType,
          allowed: currentOrgIdType?.allowed || false,
          required: currentOrgIdType?.required || false,
          match: currentOrgIdType?.match || false,
          allowedIssuers: currentOrgIdType?.issuers?.reduce((allowedIssuers, issuer) => {
            if (issuer.allowed) allowedIssuers.push(issuer);
            return allowedIssuers;
          }, []),
          requiredIssuers: currentOrgIdType?.issuers?.reduce((requiredIssuers, issuer) => {
            if (issuer.required) requiredIssuers.push(issuer);
            return requiredIssuers;
          }, []),
        });
      });
      setOrgIdTypes(newIdTypes);
    }
  }, [allIdTypes, orgMetaData]);

  return (
    <>
      <Stack direction="flex-start" alignItems="center">
        <HeaderStyle>Client Settings</HeaderStyle>
        <CambianTooltip title="Get more information">
          <IconButton color="primary" onClick={() => setOpenHelperTextFile(true)}>
            <Help
              fontSize="large"
              sx={{
                marginTop: -2,
                fontSize: '20px',
                fontWeight: 'normal',
                color: '#0000008A',
              }}
            />
          </IconButton>
        </CambianTooltip>
      </Stack>
      <PanelBorder sx={{ padding: 2 }}>
        <ClientInformationPanel
          orgClientInformation={orgMetaData?.clientInformation}
          allIdTypes={allIdTypes}
          orgIdTypes={orgIdTypes}
          handleFormSaveCallback={handleClientInformationFormSaveCallback}
        />
      </PanelBorder>
      <Modal open={openHelperTextFile} onClose={() => setOpenHelperTextFile(false)}>
        <Box sx={ModalStyle}>
          <ShowHelperTextFile onClose={onClose} />
        </Box>
      </Modal>
    </>
  );
}
