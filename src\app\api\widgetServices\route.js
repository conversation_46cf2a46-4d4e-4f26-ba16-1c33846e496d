import { NextResponse } from 'next/server';
import { ORGANIZATION } from '@/lib/constant';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import AwsError from '@/lib/error/AwsError';
import { WIDGET_LAMBDA_BASE_URL } from '@/lib/widget-editor/utils/constants/awsApiEndpoints';

export async function POST(request) {
  try {
    // Get request body
    const { appUrl, htmlString } = await request.json();
    // Build the API URL with the actual organization ID
    const apiUrl = `${WIDGET_LAMBDA_BASE_URL}/download/downloadHtmlAsPdf`;

    // Use the middleware pattern with proper token handling
    const response = await fetchWithMiddleware(addMachineAccessToken(request, ORGANIZATION))(apiUrl, {
      method: 'POST',
      body: JSON.stringify({
        appUrl,
        htmlString,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const responseData = await response.json();
    return NextResponse.json(responseData.base64Pdf || '');
  } catch (error) {
    console.error('Error in requests API route:', error);
    const apiError = new AwsError(error);
    return apiError.toNextResponse();
  }
}
