'use client';

import { ArticlesEditor } from '@cambianrepo/articles-editor';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useParams } from 'next/navigation';
import useNotification from '@/lib/hooks/useNotification';
import { deleteArtifact, getArtifact, getArtifactListByVisibility, putArtifact } from '@/lib/api/artifactRepository';
import { CREATE, PUBLIC, PRIVATE, ARTICLES, NO, BOTH } from '@/lib/constant';
import { useRouter, useSearchParams } from 'next/navigation';
import { isValidV4UUID } from '@/lib/utility';
import { useSession } from 'next-auth/react';
import { transformArticleData, uploadThumbnailToS3 } from '../articleUtility';
import { downloadFileInJsonFormat } from '@/lib/utility';

export default function ClientPage() {
  const openSnackbar = useNotification();
  const queryClient = useQueryClient();
  const router = useRouter();
  const searchParams = useSearchParams();
  const visibility = searchParams.get('visibility');
  const { artifactId } = useParams();
  const { data: session } = useSession();
  const user = session?.user;

  const { data: articleList } = useQuery({
    queryKey: [ARTICLES, PRIVATE],
    queryFn: () =>
      getArtifactListByVisibility({
        artifactType: ARTICLES,
        visibility: PRIVATE,
      }),
  });

  const getArticleWithReactQuery = async (articleId) => {
    const response = await queryClient.fetchQuery({
      queryKey: [ARTICLES, articleId],
      queryFn: () =>
        getArtifact({
          visibility: PRIVATE,
          artifactId: articleId,
          artifactType: ARTICLES,
          includeMetadata: true,
        }),
    });

    return await transformArticleData(response);
  };

  const { data, isError, isLoading, error } = useQuery({
    queryKey: [ARTICLES, artifactId, visibility],
    queryFn: async () => {
      const response = await getArtifact({
        artifactType: ARTICLES,
        artifactId,
        includeMetadata: true,
        visibility,
      });

      if (!response) {
        throw new Error('Article not found');
      }

      return await transformArticleData(response);
    },
    enabled: artifactId !== CREATE && isValidV4UUID(artifactId) && (visibility === PUBLIC || visibility === PRIVATE),
  });

  useEffect(() => {
    if (
      (artifactId !== CREATE && (!isValidV4UUID(artifactId) || (visibility !== PRIVATE && visibility !== PUBLIC))) ||
      isError
    ) {
      router.replace('/article-editor');
      openSnackbar({
        variant: 'error',
        msg: 'Failed to retrieve article',
      });
    }
  }, [artifactId, isError]);

  const article = data || {};
  const publishStatus = article.publishStatus;

  const handleSave = async (article) => {
    const newArticle = structuredClone(article);
    newArticle.modifiedDate = new Date().toISOString();

    try {
      const thumbnailData = newArticle.thumbnail?.data
        ? {
            contentType: newArticle.thumbnail.contentType || 'image/png',
            fileName: newArticle.thumbnail.fileName || 'thumbnail.png',
            data: newArticle.thumbnail.data,
          }
        : null;

      const articleToSave = {
        ...newArticle,
        publishStatus: artifactId === CREATE ? NO : publishStatus,
        thumbnail: newArticle.thumbnail
          ? {
              contentType: newArticle.thumbnail.contentType,
              fileName: newArticle.thumbnail.fileName,
            }
          : null,
      };
      if (articleToSave.thumbnail?.data) {
        delete articleToSave.thumbnail.data;
      }

      const privateRes = await putArtifact({
        artifactType: ARTICLES,
        visibility: PRIVATE,
        requestBody: articleToSave,
        artifactId: artifactId === CREATE ? undefined : artifactId,
      });

      if (!privateRes.ok) throw new Error('Failed to update private article');
      const privateResponse = await privateRes.json();

      let publicResponse;
      const shouldUpdatePublic = publishStatus === PUBLIC || publishStatus === BOTH;
      if (shouldUpdatePublic && artifactId !== CREATE) {
        const publicRes = await putArtifact({
          artifactType: ARTICLES,
          visibility: PUBLIC,
          requestBody: articleToSave,
          artifactId: artifactId,
        });

        if (!publicRes.ok) throw new Error('Failed to update public article');
        publicResponse = await publicRes.json();
      }

      if (thumbnailData) {
        await Promise.all([
          privateResponse.responseBody?.thumbnailPresignedUrl &&
            uploadThumbnailToS3(privateResponse.responseBody.thumbnailPresignedUrl, thumbnailData),
          shouldUpdatePublic &&
            publicResponse?.responseBody?.thumbnailPresignedUrl &&
            uploadThumbnailToS3(publicResponse.responseBody.thumbnailPresignedUrl, thumbnailData),
        ]);
      }

      if (artifactId === CREATE) {
        const { artifactId: newArtifactId } = privateResponse.responseBody;
        router.push(`/article-editor/${newArtifactId}?visibility=private`);
        return { success: true, article: { ...newArticle, artifactId: newArtifactId } };
      } else {
        await Promise.all([
          queryClient.invalidateQueries([ARTICLES, artifactId]),
          queryClient.invalidateQueries([ARTICLES, PRIVATE]),
          shouldUpdatePublic && queryClient.invalidateQueries([ARTICLES, PUBLIC]),
        ]);
        return { success: true, article: newArticle };
      }
    } catch (err) {
      console.error('Save failed:', err);
      return { success: false, error: err.message || 'Failed to save article' };
    }
  };

  const handlePublish = async (articleId, newPublishStatus, options = {}) => {
    const { triggeredBySave = false, newArticle = null } = options;
    let articleDataToPublish = newArticle || article;

    try {
      if (articleId === CREATE || !articleDataToPublish || Object.keys(articleDataToPublish).length === 0) {
        console.log('Please save the article before publishing');
        return { success: false, message: 'Please save the article before publishing' };
      }

      openSnackbar({
        msg: `Publishing ${articleDataToPublish.title} to ${newPublishStatus}`,
      });

      const article = await getArticleWithReactQuery(articleId);
      const { publishStatus: currentPublishStatus } = articleDataToPublish;

      if (currentPublishStatus === newPublishStatus) {
        openSnackbar({
          variant: 'warning',
          msg: 'There is nothing to publish. Aborting',
        });
        return;
      }

      const thumbnailData = article.thumbnail?.data
        ? {
            data: article.thumbnail.data,
            contentType: article.thumbnail.contentType || 'image/png',
            fileName: article.thumbnail.fileName || 'thumbnail.png',
          }
        : null;

      const privateUpdateResponse = await putArtifact({
        artifactType: ARTICLES,
        visibility: PRIVATE,
        requestBody: {
          ...article,
          publishStatus: newPublishStatus,
          thumbnail: thumbnailData
            ? {
                contentType: thumbnailData.contentType,
                fileName: thumbnailData.fileName,
              }
            : null,
        },
        artifactId: articleId,
      });

      if (!privateUpdateResponse.ok) {
        throw new Error('Failed to update private article');
      }

      const privateResponse = await privateUpdateResponse.json();
      const privateUpdate = privateResponse.responseBody;

      if (thumbnailData && privateUpdate?.thumbnailPresignedUrl) {
        await uploadThumbnailToS3(privateUpdate.thumbnailPresignedUrl, thumbnailData);
      }

      if (newPublishStatus === BOTH || newPublishStatus === PUBLIC) {
        const publicUpdateResponse = await putArtifact({
          artifactType: ARTICLES,
          visibility: PUBLIC,
          requestBody: {
            ...article,
            publishStatus: newPublishStatus,
            thumbnail: thumbnailData
              ? {
                  contentType: thumbnailData.contentType,
                  fileName: thumbnailData.fileName,
                }
              : null,
          },
          artifactId: articleId,
        });

        if (!publicUpdateResponse.ok) {
          throw new Error('Failed to update public article');
        }

        const publicResponse = await publicUpdateResponse.json();
        const publicUpdate = publicResponse.responseBody;

        if (thumbnailData && publicUpdate?.thumbnailPresignedUrl) {
          await uploadThumbnailToS3(publicUpdate.thumbnailPresignedUrl, thumbnailData);
        }
      } else if (currentPublishStatus === BOTH || currentPublishStatus === PUBLIC) {
        await deleteArtifact({
          artifactType: ARTICLES,
          visibility: PUBLIC,
          artifactId: articleId,
        });
      }
      await Promise.all([
        queryClient.invalidateQueries([ARTICLES, PRIVATE]),
        queryClient.invalidateQueries([ARTICLES, PUBLIC]),
        queryClient.invalidateQueries([ARTICLES, articleId]),
      ]);
      return { success: true };
    } catch (err) {
      console.error(err);
      return { success: false };
    }
  };

  const handleDelete = async (articleId) => {
    const selectedArticle = articleList.find((article) => article.artifactId === articleId);
    try {
      if (artifactId === CREATE) {
        router.push('/article-editor');
        return { success: true };
      }
      openSnackbar({
        msg: `Deleting ${selectedArticle.title}`,
      });

      const deletePromises = [
        deleteArtifact({
          artifactId,
          artifactType: ARTICLES,
          visibility: PRIVATE,
        }),
      ];

      if (publishStatus === PUBLIC || publishStatus === BOTH) {
        deletePromises.push(
          deleteArtifact({
            artifactId,
            artifactType: ARTICLES,
            visibility: PUBLIC,
          }),
        );
      }

      const results = await Promise.all(deletePromises);

      const allSuccess = results.every((res) => res.status === 200);

      if (allSuccess) {
        queryClient.setQueryData([ARTICLES, PRIVATE], (oldData = []) =>
          oldData?.filter((article) => article.artifactId !== artifactId),
        );
        queryClient.removeQueries({
          queryKey: [ARTICLES, artifactId],
          exact: true,
        });
        router.push('/article-editor');
        return { success: true };
      } else {
        throw new Error('One or more deletions failed');
      }
    } catch (err) {
      console.error(err);
      return { success: false };
    }
  };

  const handleExport = async (articleId) => {
    try {
      const article = await getArticleWithReactQuery(articleId);
      if (!article) {
        throw Error('Something went wrong');
      }
      openSnackbar({
        msg: `Exporting ${article.title}`,
      });

      let thumbnailBase64 = null;
      if (article.thumbnail?.data) {
        thumbnailBase64 = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.readAsDataURL(article.thumbnail.data);
        });
      }

      const exportData = {
        ...article,
        thumbnail: article.thumbnail
          ? {
              ...article.thumbnail,
              data: thumbnailBase64,
            }
          : null,
      };

      downloadFileInJsonFormat(JSON.stringify(exportData), article.title);
      return { success: true };
    } catch (err) {
      console.error('Export failed:', err);
      openSnackbar({
        variant: 'error',
        msg: 'Error in exporting article.',
      });
      return { success: false };
    }
  };

  const handleDuplicate = async (articleId) => {
    if (articleId === CREATE) {
      openSnackbar({
        variant: 'error',
        msg: 'Save article before duplicating.',
      });
      return { success: false };
    }
    try {
      const originalArticle = await getArticleWithReactQuery(articleId);
      if (!originalArticle) throw new Error('Original article not found');

      openSnackbar({
        msg: `Duplicating ${originalArticle.title} article`,
      });

      let thumbnailData = null;
      if (originalArticle.thumbnail?.data) {
        thumbnailData = {
          contentType: originalArticle.thumbnail.contentType || 'image/png',
          fileName: `copy_${originalArticle.thumbnail.fileName || 'thumbnail.png'}`,
          data: new Blob([originalArticle.thumbnail.data], { type: originalArticle.thumbnail.contentType }),
        };
      }

      const res = await putArtifact({
        artifactType: ARTICLES,
        visibility: PRIVATE,
        requestBody: {
          title: `${originalArticle.title} COPY`,
          description: originalArticle.description,
          name: `${originalArticle.name} COPY`,
          body: originalArticle.body,
          thumbnail: thumbnailData
            ? {
                contentType: thumbnailData.contentType,
                fileName: thumbnailData.fileName,
              }
            : null,
          publishStatus: 'no',
        },
      });

      if (!res.ok) {
        throw new Error('Failed to duplicate article');
      }

      const response = await res.json();
      const responseBody = response.responseBody;

      if (thumbnailData && responseBody?.thumbnailPresignedUrl) {
        try {
          await uploadThumbnailToS3(responseBody.thumbnailPresignedUrl, thumbnailData);
        } catch (error) {
          console.error('Failed to upload thumbnail for duplicate:', error);
          openSnackbar({
            variant: 'warning',
            msg: 'Article duplicated but thumbnail upload failed',
          });
        }
      }

      queryClient.invalidateQueries({ queryKey: [ARTICLES, PRIVATE] });
      return { success: true };
    } catch (err) {
      console.error('Duplicate failed:', err);
      return { success: false };
    }
  };

  if (isLoading) {
    return <>LOADING...</>;
  }
  return (
    <>
      <ArticlesEditor
        publishedRepository={publishStatus}
        handleNavigation={() => {
          router.push('/article-editor');
        }}
        onSaveDraftCallback={handleSave}
        onPublishCallback={handlePublish}
        onDuplicateCallback={handleDuplicate}
        onDeleteCallback={handleDelete}
        onExportCallback={handleExport}
        existingArticleData={article}
        setExistingArticleData={() => {}}
      />
    </>
  );
}
