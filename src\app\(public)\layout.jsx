import Image from 'next/image';
import backgroundImage from '@/public/cloud_background.jpeg';
import PublicFooter from '@/components/footer/PublicFooter';
import PublicToolbar from '@/components/header/PublicToolbar';
import PublicRoute from './PublicRoute';
import { Notification } from './Notification';
import './style.css';

export default function PublicLayout({ children }) {
  const message = process.env.NEXT_PUBLIC_NOTIFICATION_MESSAGE;
  return (
    <PublicRoute>
      <div style={{ position: 'relative', minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
        <PublicToolbar />
        {message && <Notification message={message} />}
        <Image
          src={backgroundImage}
          alt="background image with clouds and blue sky"
          fill
          style={{ objectFit: 'cover', objectPosition: 'center', zIndex: -1, top: 0, left: 0 }}
        />
        <div style={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: 15 }}>
          <div className="componentBorder">{children}</div>
        </div>
        <PublicFooter />
      </div>
    </PublicRoute>
  );
}
