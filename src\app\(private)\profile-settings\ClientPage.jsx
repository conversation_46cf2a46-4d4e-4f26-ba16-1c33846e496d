'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getOrgMetaData, getOrganizationConsentAgreement, getOrganizationIcon } from '@/lib/api/common';
import { OrgProfileEditor } from './Editors';
import useNotification from '@/lib/hooks/useNotification';
import { ORGANIZATION_ID, ORGANIZATION_METADATA, ORGANIZATION_SETTINGS } from '@/lib/constant';
import { getOrgSettings } from '@/lib/api/orgData';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { clientFetch } from '@/lib/fetch/client';
import { useSession } from 'next-auth/react';

function ClientPage() {
  const openSnackbar = useNotification();
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: getOrgMetaData,
  });

  const orgConsentAgreementQuery = useQuery({
    queryKey: ['organizationConsentAgreement'],
    queryFn: getOrganizationConsentAgreement,
  });

  const orgIconQuery = useQuery({
    queryKey: ['organizationIcon'],
    queryFn: getOrganizationIcon,
  });

  const orgDataWithConsentAgreementAndIcon = {
    ...orgMetaDataQuery.data,
    iconUrl: orgIconQuery.data?.iconUrl,
    consentAgreementUrl: orgConsentAgreementQuery.data?.consentAgreementUrl,
  };

  const { data: publishOrgProfile } = useQuery({
    queryKey: [ORGANIZATION_SETTINGS],
    queryFn: getOrgSettings,
  });

  const autoPublishOrgProfile = publishOrgProfile?.autoPublishOrgProfile;

  const handleProfileSaveCallback = async ({ allValues, dirtyValues }) => {
    console.log('allValues from form to API:', allValues);
    console.log('dirtyValues from form to API:', dirtyValues);
    try {
      const orgDataResponse = await updateOrgData(allValues, dirtyValues);

      if (!orgDataResponse.ok) {
        const errorData = await orgDataResponse.json();
        throw new Error(`Failed to update organization data: ${errorData.message}`);
      }

      if (autoPublishOrgProfile) {
        const orgRegistryResponse = await updateOrgRegistry(allValues, dirtyValues);
        if (!orgRegistryResponse.ok) {
          const errorData = await orgRegistryResponse.json();
          throw new Error(`Failed to update organization registry: ${errorData.message}`);
        }
      }

      if (Object.keys(dirtyValues).length > 0) {
        queryClient.setQueryData([ORGANIZATION_METADATA], allValues);
        queryClient.invalidateQueries([ORGANIZATION_METADATA]);
      }
      if ('logoSrcImg' in dirtyValues) {
        queryClient.setQueryData(['organizationIcon'], { iconUrl: allValues.logoSrcImg });
      }
      if ('consentAgreement' in dirtyValues) {
        queryClient.setQueryData(['organizationConsentAgreement'], { consentAgreementUrl: allValues.consentAgreement });
      }
      console.log('Saving profile data succeeded');
    } catch (error) {
      console.error('Error:', error);
      openSnackbar({
        variant: 'error',
        msg: error.message || 'Something went wrong while saving profile data. Please contact technical support.',
      });
    }
  };

  const updateOrgData = async (allValues, dirtyValues) => {
    const { logoSrcImg, consentAgreement, ...profileData } = dirtyValues;

    try {
      if (Object.keys(profileData).length > 0) {
        await updateOrgDataProfile(allValues);
      }

      if ('logoSrcImg' in dirtyValues) {
        const response = await updateOrgDataIcon(logoSrcImg);
        if (!response.ok) {
          return response;
        }
      }

      if ('consentAgreement' in dirtyValues) {
        const response = await updateOrgDataConsentAgreement(consentAgreement);
        if (!response.ok) {
          return response;
        }
      }

      return { ok: true };
    } catch (error) {
      console.error('Error in updateOrgData:', error);
      return { ok: false, status: 500, message: 'Internal server error' };
    }
  };

  const updateOrgRegistry = async (allValues, dirtyValues) => {
    const { logoSrcImg, consentAgreement, ...profileData } = dirtyValues;

    try {
      if (Object.keys(profileData).length > 0) {
        const response = await updateOrgRegistryProfile(allValues);
        if (!response.ok) {
          return response;
        }
      }

      if ('logoSrcImg' in dirtyValues) {
        if (logoSrcImg) {
          const response = await updateOrgRegistryIcon(logoSrcImg);
          if (!response.ok) {
            return response;
          }
        } else {
          const response = await deleteOrgRegistryIcon();
          if (!response.ok) {
            return response;
          }
        }
      }

      if ('consentAgreement' in dirtyValues) {
        if (consentAgreement) {
          const response = await updateOrgRegistryConsentAgreement(consentAgreement);
          if (!response.ok) {
            return response;
          }
        } else {
          const response = await deleteOrgRegistryConsentAgreement();
          if (!response.ok) {
            return response;
          }
        }
      }
      return { ok: true };
    } catch (error) {
      console.error('Error in updateOrgRegistry:', error);
      return { ok: false, status: 500, message: 'Internal server error' };
    }
  };

  const updateOrgDataProfile = (allValues) => {
    const orgDataValues = { ...allValues };
    const oldOrgDataValues = { ...orgMetaDataQuery.data };

    orgDataValues.serviceConfiguration = oldOrgDataValues.serviceConfiguration;
    orgDataValues.requestedAttributes = oldOrgDataValues.requestedAttributes;
    orgDataValues.features = oldOrgDataValues.features;
    orgDataValues.clientInformation = oldOrgDataValues.clientInformation;
    orgDataValues.idTypes = oldOrgDataValues.idTypes;

    delete orgDataValues.consentAgreement;
    delete orgDataValues.logoSrcImg;

    return fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}`, {
      method: 'PUT',
      body: JSON.stringify({ ...orgDataValues }),
    });
  };

  const updateOrgDataIcon = async (logoSrcImg) => {
    if (!logoSrcImg) {
      const response = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/icon`, {
        method: 'DELETE',
      });
      return { ok: true, status: response.status };
    }
    const iconUrlResponse = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/icon`, {
      method: 'PUT',
      body: JSON.stringify({ filename: logoSrcImg.name }),
    });

    const { iconUrl } = await iconUrlResponse.json();
    return fetch(iconUrl, {
      method: 'PUT',
      body: logoSrcImg,
      headers: { 'Content-Type': logoSrcImg.type },
    });
  };

  const updateOrgDataConsentAgreement = async (consentAgreementFile) => {
    if (!consentAgreementFile) {
      const response = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/consent-agreement`, {
        method: 'DELETE',
      });
      return { ok: true, status: response.status };
    }

    const consentAgreementResponse = await fetchNextRoute(
      'organizationData',
      `/organizations/${ORGANIZATION_ID}/consent-agreement`,
      {
        method: 'PUT',
        body: JSON.stringify({ filename: consentAgreementFile.name }),
      },
    );

    const { consentAgreementUrl } = await consentAgreementResponse.json();
    return fetch(consentAgreementUrl, {
      method: 'PUT',
      body: consentAgreementFile,
      headers: { 'Content-Type': consentAgreementFile.type },
    });
  };

  const updateOrgRegistryProfile = async (allValues) => {
    try {
      const { logoSrcImg, consentAgreement, ...orgRegistryProfileValues } = allValues;

      const payload = Object.fromEntries(
        Object.entries(orgRegistryProfileValues).map(([key, value]) => [key, value === null ? '' : value]),
      );

      const checkResponse = await fetchNextRoute('organizationRegistry', `/${ORGANIZATION_ID}`);

      if (checkResponse.status === 404) {
        console.log('Organization does not exist in the registry. Creating a new organization...');

        const createResponse = await fetchNextRoute('organizationRegistry', ``, {
          method: 'POST',
          body: JSON.stringify({ ...payload, organizationId: session.user.orgId }),
        });

        if (!createResponse.ok) {
          console.error('Failed to create organization profile in registry:', createResponse.statusText);
          throw new Error(`Failed to create organization profile in registry: ${createResponse.statusText}`);
        }

        console.log('Organization created successfully in the registry.');
        return createResponse;
      } else if (!checkResponse.ok) {
        console.error('Failed to check organization profile in registry:', checkResponse.statusText);
        throw new Error(`Failed to check organization profile in registry: ${checkResponse.statusText}`);
      }

      console.log('Organization exists in the registry. Updating the organization...');
      const updateResponse = await fetchNextRoute('organizationRegistry', `/${ORGANIZATION_ID}`, {
        method: 'PUT',
        body: JSON.stringify({ ...payload }),
      });

      if (!updateResponse.ok) {
        console.error('Failed to update organization profile in registry:', updateResponse.statusText);
        throw new Error(`Failed to update organization profile in registry: ${updateResponse.statusText}`);
      }
      return updateResponse;
    } catch (error) {
      console.error('Error updating organization registry profile:', error);
      throw error;
    }
  };

  const updateOrgRegistryIcon = async (logoSrcImg) => {
    const presignedUrlResponse = await fetchNextRoute('organizationRegistry', `/${ORGANIZATION_ID}/document`, {
      method: 'POST',
      body: JSON.stringify({
        contentType: logoSrcImg.type,
        documentName: 'img',
        documentType: 'imgSrcLogo',
        fileName: logoSrcImg.name,
      }),
    });

    if (presignedUrlResponse.ok) {
      const { presignedUrl } = await presignedUrlResponse.json();
      return fetch(presignedUrl, {
        method: 'PUT',
        body: logoSrcImg,
        headers: { 'Content-Type': logoSrcImg.type },
      });
    }
  };

  const updateOrgRegistryConsentAgreement = async (consentAgreementFile) => {
    const presignedUrlResponse = await fetchNextRoute('organizationRegistry', `/${ORGANIZATION_ID}/document`, {
      method: 'POST',
      body: JSON.stringify({
        contentType: consentAgreementFile.type,
        documentName: 'consentAgreement',
        documentType: 'consentAgreementFile',
        fileName: consentAgreementFile.name,
      }),
    });

    if (presignedUrlResponse.ok) {
      const { presignedUrl } = await presignedUrlResponse.json();
      return fetch(presignedUrl, {
        method: 'PUT',
        body: consentAgreementFile,
        headers: { 'Content-Type': consentAgreementFile.type },
      });
    }
  };

  const deleteOrgRegistryIcon = () => {
    return fetchNextRoute('organizationRegistry', `/${ORGANIZATION_ID}/document?type=imgSrcLogo&name=img`, {
      method: 'DELETE',
    });
  };

  const deleteOrgRegistryConsentAgreement = () => {
    return fetchNextRoute(
      'organizationRegistry',
      `/${ORGANIZATION_ID}/document?type=consentAgreementFile&name=consentAgreement`,
      { method: 'DELETE' },
    );
  };

  const handleResults = (results, messages) => {
    let hasError = false;
    results.forEach((result, index) => {
      if (!result.ok) {
        hasError = true;
        openSnackbar({ variant: 'error', msg: `${messages[index]} failed` });
      }
    });

    if (!hasError) {
      console.log('Saving profile data succeeded');
    }
  };

  return (
    <OrgProfileEditor
      orgMetaData={orgDataWithConsentAgreementAndIcon}
      handleProfileFormSaveCallback={handleProfileSaveCallback}
    />
  );
}

export default ClientPage;
