import React from 'react';
import ViewTabPanel from './ViewTabPanel';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';

export function ViewEditor({ orgMetaData, handleViewFormSaveCallback }) {
  return (
    <>
      <HeaderStyle>View Settings</HeaderStyle>
      <PanelBorder sx={{ padding: 2 }}>
        <ViewTabPanel orgViews={orgMetaData} handleFormSaveCallback={handleViewFormSaveCallback} />
      </PanelBorder>
    </>
  );
}
