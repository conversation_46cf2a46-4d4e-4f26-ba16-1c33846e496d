import 'server-only';
import { MACHINE_ACCESS_TOKEN, NETWORK, ORGANIZATION_ID, ORGANIZATION_USER_ID, ORGANIZATION } from '@/lib/constant';
import { headers } from 'next/headers';
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';

// All our Next.js Route handlers expects jwt token from request's Cookie header.
// Sometimes, the API request is made from Server Component. For some reason, the cookie is not automatically passed on.
// Thus, in each Server Component, manually pass the cookie if it exist.
// When the API request is made from Client Component, cookie is automatically passed on, so let the application handles that.
// Similar problem existed for page router and calling API from getServerSideProps
// https://stackoverflow.com/questions/76279526/next-auth-gettoken-dont-working-on-serverside
export async function addCookie(_url, options, next) {
  try {
    if (typeof window !== 'undefined') {
      throw new Error('Use fetch/client instead.');
    }

    const cookie = headers().get('cookie');

    options.headers ??= {};
    options.headers.Cookie = cookie;
    next();
  } catch (error) {
    console.log('addCookie/server failed');
    throw error;
  }
}

export function addCsrfToken(_url, options, next) {
  // not implemented and used at the moment
  next();
}

export const addMachineAccessToken = (getSessionInput, env = ORGANIZATION) => {
  if (!(getSessionInput instanceof Request) && !getSessionInput?.callbacks) {
    throw new Error(
      'addUserToken/server failed: Either pass NextRequest from Route Handler or pass AuthOptions from React Server Component.',
    );
  }
  return async (_url, options, next) => {
    try {
      if (env !== NETWORK && env !== ORGANIZATION) {
        throw new Error('env should be network or organization');
      }
      if (typeof window !== 'undefined') {
        throw new Error('Use fetch/client instead.');
      }
      if (!getSessionInput) {
        throw new Error('Did not pass getSessionInput');
      }
      const session = await getServerSession(authOptions);
      let accessToken = session?.user[MACHINE_ACCESS_TOKEN(env)];
      options.headers ??= {};
      options.headers['Authorization'] = `Bearer ${accessToken}`;
      next();
    } catch (error) {
      console.log('addMachineAccessToken/server failed');
      throw error;
    }
  };
};

export const addUserToken = (getSessionInput, { replaceOrgId, replaceOrgUserId } = {}) => {
  if (!(getSessionInput instanceof Request) && !getSessionInput?.callbacks) {
    throw new Error(
      'addUserToken/server failed: Either pass NextRequest from Route Handler or pass AuthOptions from React Server Component.',
    );
  }
  return async (url, options, next) => {
    try {
      if (typeof window !== 'undefined') {
        throw new Error('Use fetch/client instead.');
      }
      if (!getSessionInput) {
        throw new Error('Did not pass getSessionInput');
      }
      const session = await getServerSession(authOptions);
      const { orgId, orgUserId, idToken } = session?.user || {};

      if (!idToken) {
        throw new Error('An idToken is not provided in the session');
      }

      options.headers ??= {};
      options.headers['Cambian-User-Token'] = `Bearer ${idToken}`;

      if (replaceOrgId) {
        if (!orgId) {
          throw new Error('An orgId is not provided in the session');
        }
        url = url.replace(ORGANIZATION_ID, orgId);
      }

      if (replaceOrgUserId) {
        if (!orgUserId) {
          return NextResponse.json({ message: 'An org user id is not provided' }, { status: 399 });
        }
        url = url.replace(ORGANIZATION_USER_ID, orgUserId);
      }
      next();
      return url;
    } catch (error) {
      console.log('addUserToken/server failed');
      throw error;
    }
  };
};

export const fetchWithMiddleware =
  (...middleware) =>
  async (url, options = {}) => {
    try {
      for (let i = 0; i < middleware.length; i++) {
        let nextInvoked = false;

        /**
         * Move to the next middleware in the chain.
         * @function
         */
        const next = async () => {
          nextInvoked = true;
        };

        const changedUrl = await middleware[i](url, options, next);
        if (typeof changedUrl === 'function') {
          throw new Error('One of your closure middlewares was not properly initialized.');
        }
        if (changedUrl && typeof changedUrl == 'string') {
          url = changedUrl;
        }

        if (!nextInvoked) {
          throw new Error('Middleware failed');
        }
      }
    } catch (error) {
      console.log(
        'fetchWithMiddleware/server FAILED:',
        options.method?.toUpperCase() || 'GET',
        url,
        'options',
        options,
      );
      console.log(error);
      throw error;
    }
    console.log('fetchWithMiddleware/server:', options.method?.toUpperCase() || 'GET', url);
    console.log({ options });
    return fetch(url, options);
  };
