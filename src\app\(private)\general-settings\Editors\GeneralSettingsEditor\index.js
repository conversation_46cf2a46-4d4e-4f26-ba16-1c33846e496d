import React from 'react';
import GeneralSettingsTabPanel from './GeneralSettingsTabPanel';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';

export function GeneralSettingsEditor({ orgMetaData, handleProfileFormSaveCallback }) {
  return (
    <>
      <HeaderStyle>General Settings</HeaderStyle>
      <PanelBorder sx={{ padding: 2 }}>
        <GeneralSettingsTabPanel orgMetaData={orgMetaData} handleFormSaveCallback={handleProfileFormSaveCallback} />
      </PanelBorder>
    </>
  );
}
