'use client';

import {
  QuestionnaireList,
  generateCodebookData,
  headerTemplate,
  questionnaireDetailsTemplate,
  htmlDefaultTemplate as htmlReportDefaultTemplate,
} from '@cambianrepo/questionnaire-editor-v2';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import useNotification from '@/lib/hooks/useNotification';
import {
  deleteArtifact,
  getArtifact,
  getArtifactListByVisibility,
  putQuestionnaire,
} from '@/lib/api/artifactRepository';
import { CREATE, PUBLIC, PRIVATE, QUESTIONNAIRES, BOTH, DRAFT, FINAL } from '@/lib/constant';
import { downloadFileInJsonFormat } from '@/lib/utility';
import { downloadPDF, removeURLParams } from './codebookUtility';

import { convertV1toV2Format, extractExtension } from './questionnaireUtility';

const codebookDefaultTemplate = headerTemplate + questionnaireDetailsTemplate;
export default function ClientPage() {
  const queryClient = useQueryClient();
  const openSnackbar = useNotification();
  const { data: session } = useSession();
  const user = session?.user;
  const router = useRouter();
  const {
    data: questionnaireList,
    isError,
    isLoading,
  } = useQuery({
    queryKey: [QUESTIONNAIRES, PRIVATE],
    queryFn: () => getArtifactListByVisibility({ artifactType: QUESTIONNAIRES, visibility: PRIVATE }),
  });

  useEffect(() => {
    if (isError) {
      openSnackbar({
        variant: 'error',
        msg: 'Retrieving questionnaire list failed.',
      });
    }
  }, [isError]);

  if (isLoading) {
    return <>LOADING...</>;
  }
  const getQuestionnaireWithReactQuery = async (questionnaireId) => {
    // Even the 'Public' questionnaires are saved in private AR, so the visibilty defaults to PRIVATE here.
    // However, this may change.
    const data = await queryClient.fetchQuery({
      queryKey: [QUESTIONNAIRES, questionnaireId],
      queryFn: () => getArtifact({ visibility: PRIVATE, artifactId: questionnaireId, artifactType: QUESTIONNAIRES }),
    });
    return data;
  };

  const handleDelete = async (questionnaireId) => {
    console.log('qid ', questionnaireId);
    const selectedQuestionnaire = questionnaireList.find(
      (questionnaire) => questionnaire.artifactId === questionnaireId,
    );
    try {
      console.log(selectedQuestionnaire);
      openSnackbar({
        msg: `Deleting ${selectedQuestionnaire.shortName}...`,
      });
      const { artifactId, publishStatus } = selectedQuestionnaire;
      const deletePromises = [];
      if (publishStatus === PUBLIC || publishStatus === BOTH) {
        deletePromises.push(deleteArtifact({ artifactId, artifactType: QUESTIONNAIRES, visibility: PUBLIC }));
      }
      deletePromises.push(deleteArtifact({ artifactId, artifactType: QUESTIONNAIRES, visibility: PRIVATE }));

      const results = await Promise.all(deletePromises);

      // TODO: What if one is deleted but another fails..? We really need a better error handling.
      // Right now, just check status for one
      if (results[0].status === 200) {
        queryClient.setQueryData([QUESTIONNAIRES, PRIVATE], (oldData = []) =>
          oldData.filter((questionnaire) => questionnaire.artifactId !== artifactId),
        );
        console.log(`Successfully deleted questionnaire, ${selectedQuestionnaire.shortName}`);
      } else {
        throw Error(JSON.stringify(results[0]));
      }
    } catch (err) {
      console.error(err);
      openSnackbar({
        variant: 'error',
        msg: `Failed to delete questionnaire, ${selectedQuestionnaire.shortName}`,
      });
    }
  };

  const handleDuplicate = async (questionnaireId) => {
    try {
      const questionnaireMetadata = questionnaireList.find(
        (questionnaire) => questionnaire.artifactId === questionnaireId,
      );
      openSnackbar({ msg: `Duplicating ${questionnaireMetadata.shortName} questionnaire...` });
      const { questionnaire, pdfTemplate } = await getQuestionnaireWithReactQuery(questionnaireId);

      if (!questionnaire) {
        throw new Error('Failed to get artifact');
      }
      // TODO: This causes the copy versions to have the same questionnaire name
      // Perhaps the put artifact endpoint in Artifact Repository, perhaps it should append a number if the same name questionnaire is created.
      questionnaire.name = `${questionnaire.name} COPY`;
      questionnaire.title = `${questionnaire.title} COPY`;
      questionnaire.status = DRAFT;
      const res = await putQuestionnaire({
        artifactType: QUESTIONNAIRES,
        visibility: PRIVATE,
        requestBody: {
          contentStatus: 'draft',
          publishStatus: 'no',
          questionnaire: questionnaire,
          pdfTemplate,
        },
      });
      if (res.status === 200) {
        queryClient.invalidateQueries({ queryKey: [QUESTIONNAIRES, PRIVATE] });
        console.log(`Duplicated "${questionnaire.shortName} as draft"`);
      } else {
        throw new Error('putQuestionnaire failed.');
      }
    } catch (err) {
      console.log(err);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to duplicate the questionnaire',
      });
    }
  };

  const handleImport = async (fhirQuestionnaire) => {
    try {
      openSnackbar({
        msg: 'Importing questionnaire...',
      });
      // TODO: The api endpoint extract content status from fhirQuestionnaire and not request body...
      fhirQuestionnaire.status = DRAFT;
      const updatedQuestionnaire = convertV1toV2Format(fhirQuestionnaire);
      const pdfTemplate = extractExtension(updatedQuestionnaire?.extension, 'pdftemplate-base64')?.valueString;

      const res = await putQuestionnaire({
        artifactType: QUESTIONNAIRES,
        visibility: 'private',
        requestBody: {
          contentStatus: 'draft',
          publishStatus: 'no',
          questionnaire: updatedQuestionnaire,
          pdfTemplate,
        },
      });

      if (res.status === 200) {
        queryClient.invalidateQueries([QUESTIONNAIRES, PRIVATE]);
        console.log(`Imported ${updatedQuestionnaire.name}`);
      } else {
        openSnackbar({
          variant: 'error',
          msg: 'Failed to import a questionnaire',
        });
      }
    } catch (err) {
      console.log(err);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to import a questionnaire',
      });
    }
  };

  const handleExport = async (questionnaireId) => {
    try {
      openSnackbar({
        msg: 'Exporting a questionnaire...',
      });

      const { questionnaire, pdfTemplate } = await getQuestionnaireWithReactQuery(questionnaireId);
      if (pdfTemplate) {
        const hasPdfTemplate = questionnaire.extension.some((ext) => ext.url === 'pdftemplate-base64');
        if (!hasPdfTemplate) {
          questionnaire.extension.push({
            url: 'pdftemplate-base64',
            valueString: pdfTemplate,
          });
        }
      }

      if (!questionnaire) {
        throw Error('Something went wrong');
      }

      downloadFileInJsonFormat(JSON.stringify(questionnaire), questionnaire.name);

      console.log(`Successfully Exported ${questionnaire.name}. Check your downloads!`);
    } catch (err) {
      console.log(err);
      openSnackbar({
        variant: 'error',
        msg: 'Error in exporting questionnaire.',
      });
    }
  };

  const handleEdit = (questionnaireId) => {
    const questionnaireMetadata = questionnaireList.find(
      (questionnaire) => questionnaire.artifactId === questionnaireId,
    );
    const { publishStatus } = questionnaireMetadata;
    let visiblity;
    if (publishStatus === PUBLIC) {
      visiblity = PUBLIC;
    } else {
      visiblity = PRIVATE;
    }
    router.push(`/questionnaire-editor/${questionnaireId}?visibility=${visiblity}`);
  };

  const handleCreateQuestionnaire = () => {
    router.push(`/questionnaire-editor/${CREATE}`);
  };

  const handlePreview = (questionnaireId, publishStatus) => {
    const orgId = user?.orgId;
    if (!orgId) {
      openSnackbar({
        variant: 'error',
        msg: 'Technical error. Please contact the administrator.',
      });
      console.log('org id does not exists in user session');
      return;
    }
    const publishStatusParam = `repository=${publishStatus}`;
    const WIDGET_URL = `${process.env.NEXT_PUBLIC_WIDGET_BASE_URL}/widget/organizations/${orgId}/questionnaireWidget/1?qid=${questionnaireId}&${publishStatusParam}`;
    window.open(WIDGET_URL, '_blank');
  };

  const handlePublish = async (questionnaireId, newPublishStatus) => {
    const selectedQuestionnaireMetadata = questionnaireList.find(
      (questionnaire) => questionnaire.artifactId === questionnaireId,
    );
    try {
      // Assume that all questionnaires here are final
      openSnackbar({
        msg: `Publishing ${selectedQuestionnaireMetadata.shortName} to ${newPublishStatus}...`,
      });
      const { questionnaire, pdfTemplate } = await getQuestionnaireWithReactQuery(questionnaireId);
      const { publishStatus: currentPublishStatus } = selectedQuestionnaireMetadata;

      if (currentPublishStatus === newPublishStatus) {
        openSnackbar({
          variant: 'warning',
          msg: 'There is nothing to publish. Aborting...',
        });
        return;
      }

      const questionnaireActionPromises = [];
      if (newPublishStatus === BOTH || newPublishStatus === PUBLIC) {
        questionnaireActionPromises.push(
          putQuestionnaire({
            artifactType: QUESTIONNAIRES,
            visibility: PUBLIC,
            requestBody: {
              contentStatus: FINAL,
              publishStatus: newPublishStatus,
              questionnaire,
              pdfTemplate,
            },
            artifactId: questionnaireId,
          }),
        );
      } else if (currentPublishStatus === BOTH || currentPublishStatus === PUBLIC) {
        questionnaireActionPromises.push(
          deleteArtifact({
            artifactType: QUESTIONNAIRES,
            visibility: PUBLIC,
            artifactId: questionnaireId,
          }),
        );
      }

      questionnaireActionPromises.push(
        putQuestionnaire({
          artifactType: QUESTIONNAIRES,
          visibility: PRIVATE,
          requestBody: {
            contentStatus: FINAL,
            publishStatus: newPublishStatus,
            questionnaire,
            pdfTemplate,
          },
          artifactId: questionnaireId,
        }),
      );
      const results = await Promise.all(questionnaireActionPromises);

      // TODO: What if one is deleted but another fails..? We really need a better error handling.
      // Right now, just check status for one
      if (results[0].status === 200) {
        queryClient.invalidateQueries({ queryKey: [QUESTIONNAIRES, PRIVATE] });
        queryClient.invalidateQueries({ queryKey: [QUESTIONNAIRES, questionnaireId] });
        console.log(`Successfully published questionnaire, ${selectedQuestionnaireMetadata.shortName} `);
      } else {
        throw Error(JSON.stringify(results[0]));
      }
    } catch (err) {
      console.error(err);
      openSnackbar({
        variant: 'error',
        msg: `Failed to publish questionnaire, ${selectedQuestionnaireMetadata.shortName}`,
      });
    }
  };

  const handleCodebookDownload = async (questionnaireId, publishStatus, _codebookData) => {
    try {
      openSnackbar({
        msg: 'Downloading codebook...',
      });

      const { questionnaire, _pdfTemplate } = await getQuestionnaireWithReactQuery(questionnaireId);

      if (!questionnaire) {
        throw Error('Cound not fetch questionnaire');
      }
      const codebookHtml = generateCodebookData(questionnaire);
      if (!codebookHtml) {
        openSnackbar({
          variant: 'error',
          msg: 'CodeBook data not found for questionnaire with ID- ' + questionnaireId,
        });
      }

      const appUrl =
        process.env.NODE_ENV === 'development' ? 'https://www.google.com' : removeURLParams(window.location.href);

      const widgetServicesDownloadResponse = await fetch(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/widgetServices`,
        {
          method: 'POST',
          body: JSON.stringify({
            appUrl,
            htmlString: codebookHtml,
          }),
        },
      );
      if (!widgetServicesDownloadResponse.ok) {
        openSnackbar({
          variant: 'error',
          msg: `Error while downloading code book for ${questionnaire.name}`,
        });
        return;
      }
      const pdfData = await widgetServicesDownloadResponse.json();
      downloadPDF(pdfData, `codebook_${questionnaireId}`);
      console.log(`Successfully downloaded code book for ${questionnaire.name}. Check your downloads!`);
    } catch (err) {
      console.log(err);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to download questionnaire codebook.',
      });
    }
  };
  return (
    <>
      {/* 
       AppScoop uses QuestionnaireEditorKit that handles navigation between List and Editor page.
       
       <QuestionnaireEditorKit
      questionnaireList={questionnaireList}
      onEditQuestionnaireCallback={handleEditQuestionnaire}
      onSaveDraftCallback={handleSaveDraft}
      onPublishCallback={handlePublish}
      onDeleteCallback={handleDelete}
      onPreviewCallback={handlePreview}
      onDuplicateCallback={handleDuplicate}
      onImportCallback={handleImport}
      onExportCallback={handleExport}
    />  */}
      <QuestionnaireList
        handleNavigation={handleCreateQuestionnaire}
        questionnaireList={questionnaireList ?? []}
        onImportCallback={handleImport}
        handleEditQuestionnaire={handleEdit}
        handleDuplicateQuestionnaire={handleDuplicate}
        handleDeleteQuestionnaire={handleDelete}
        handleExportQuestionnaire={handleExport}
        handlePublishQuestionnaire={handlePublish}
        handlePreviewQuestionnaire={handlePreview}
        handleViewQuestionnaire={handleEdit} // Reuse handleEdit function
        handleDownloadCodebook={handleCodebookDownload}
        htmlReportDefaultTemplate={htmlReportDefaultTemplate}
        codebookDefaultTemplate={codebookDefaultTemplate}
      />
    </>
  );
}
