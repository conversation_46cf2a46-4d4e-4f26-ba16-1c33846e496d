import React from 'react';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import PropTypes from 'prop-types';

function ResultsPerPage(props) {
  const { resultsPerPage, handleResultsPerPageSelect, resultsPerPageData } = props;

  return (
    <Autocomplete
      disablePortal
      id="combo-box-demo"
      options={resultsPerPageData}
      onChange={(event, newValue) => {
        handleResultsPerPageSelect(event, newValue);
      }}
      getOptionLabel={(value) => {
        if (value.label) {
          return value.label;
        }
        return value.toString();
      }}
      value={resultsPerPage}
      isOptionEqualToValue={(option, value) => {
        return option === value;
      }}
      renderInput={(params) => <TextField {...params} sx={{ mt: 1 }} label="Results Per Page" />}
    />
  );
}

export { ResultsPerPage };

ResultsPerPage.propTypes = {
  resultsPerPage: PropTypes.any,
  handleResultsPerPageSelect: PropTypes.func,
  resultsPerPageData: PropTypes.array,
};
