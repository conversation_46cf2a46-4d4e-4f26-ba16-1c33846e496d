import { Link, Box } from '@mui/material';
import Image from 'next/image';

function Branding(props) {
  return (
    <Link href="/home" sx={{ cursor: 'pointer', display: 'block' }}>
      <Box
        sx={{
          height: 75,
          width: 'auto',
          position: 'relative',
          '& img': {
            height: 65,
            width: 'auto',
            paddingTop: '8px',
          },
        }}
      >
        <Image
          src={`/${process.env.NEXT_PUBLIC_HEADER_LOGO}`}
          alt={process.env.NEXT_PUBLIC_HEADER_LOGO_ALT}
          width={300}
          height={65}
        />
      </Box>
    </Link>
  );
}

export { Branding };
