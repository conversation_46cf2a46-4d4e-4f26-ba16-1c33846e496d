'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getOrgMetaData } from '@/lib/api/common';
import { getAllIdTypes } from '@/lib/api/orgData';
import { ClientSettingsEditor } from './Editors';
import useNotification from '@/lib/hooks/useNotification';
import { ORGANIZATION_ID, ORGANIZATION_METADATA } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

// TODO: Org Data is the source of truth and all data should be retrieved from there.
function ClientPage() {
  const openSnackbar = useNotification();

  const allIdTypesQuery = useQuery({
    queryKey: ['allIdTypes'],
    queryFn: () => getAllIdTypes(),
  });

  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => getOrgMetaData(),
  });

  const queryClient = useQueryClient();

  const handleClientInformationSaveCallback = async (data) => {
    try {
      const orgDataValues = { ...orgMetaDataQuery.data };

      orgDataValues.clientInformation = data.clientInformation;
      orgDataValues.idTypes = data.idTypes;
      const updateOrgResponse = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}`, {
        method: 'PUT',
        body: JSON.stringify({
          ...orgDataValues,
        }),
      });

      queryClient.setQueryData([ORGANIZATION_METADATA], orgDataValues);
      queryClient.invalidateQueries([ORGANIZATION_METADATA]);
      return updateOrgResponse.ok;
    } catch (error) {
      console.log(error);
      openSnackbar({
        variant: 'error',
        msg: 'Something went wrong while saving client settings data. Please contact technical support.',
      });
      return false;
    }
  };

  return (
    <ClientSettingsEditor
      allIdTypes={allIdTypesQuery.data?.idTypes}
      orgMetaData={queryClient.getQueryData([ORGANIZATION_METADATA])}
      handleClientInformationFormSaveCallback={handleClientInformationSaveCallback}
    />
  );
}

export default ClientPage;
