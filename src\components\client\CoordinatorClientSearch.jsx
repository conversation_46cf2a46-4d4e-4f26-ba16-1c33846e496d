import {
  Grid,
  Typography,
  MenuItem,
  TextField,
  Button,
  Box,
  Select,
  InputLabel,
  OutlinedInput,
  FormControl,
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import * as Constants from '@/app/globalConstants';
import useNotification from '@/lib/hooks/useNotification';

export function CoordinatorClientSearch(props) {
  const {
    title,
    idTypes,
    issuers,
    genderList,
    clientSearchHandler,
    clientSearchParams,
    handleParamsValueChange,
    firstNameAllowed,
    lastNameAllowed,
    dobAllowed,
    emailAllowed,
    phoneAllowed,
    genderAllowed,
    idAllowed,
  } = props;
  const openSnackbar = useNotification();

  const isSearchButtonDisabled = () => {
    const {
      firstName = '',
      lastName = '',
      emailAddress = '',
      primaryPhoneNumber = '',
      gender = '',
      dateOfBirth = '',
      healthCareIdType = '',
      healthCareIdIssuer = '',
      healthCareIdValue = '',
    } = clientSearchParams || {};

    // Check if all relevant fields are empty or null
    return (
      !firstName &&
      !lastName &&
      !emailAddress &&
      !primaryPhoneNumber &&
      !gender &&
      !dateOfBirth &&
      !healthCareIdType &&
      !healthCareIdIssuer &&
      !healthCareIdValue
    );
  };

  const handleClientSearch = () => {
    const { dateOfBirth } = clientSearchParams;

    if (dateOfBirth && dayjs(dateOfBirth).isAfter(dayjs(), 'day')) {
      openSnackbar({
        message: 'Please enter a date of birth in the past.',
        variant: 'error',
      });
      return;
    }
    clientSearchHandler();
  };

  const handleKeyDown = (e) => {
    if (e.keyCode == 13) {
      clientSearchHandler();
    }
  };

  const getIssuerDisplayName = (code) => {
    const issuer = issuers.find((issuer) => issuer.code === code);
    return issuer ? issuer.code : code;
  };

  return (
    <div>
      <Typography variant="h4" gutterBottom>
        {title}
      </Typography>
      <Grid container spacing={Constants.formFieldSpacing}>
        {firstNameAllowed && (
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField
              label="First Name"
              name="firstName"
              value={clientSearchParams?.firstName}
              onChange={handleParamsValueChange}
              onKeyDown={handleKeyDown}
            />
          </Grid>
        )}
        {lastNameAllowed && (
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField
              label="Last Name"
              name="lastName"
              value={clientSearchParams?.lastName}
              onChange={handleParamsValueChange}
              onKeyDown={handleKeyDown}
            />
          </Grid>
        )}
        {idAllowed && (
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <Box display="flex" flexDirection={{ xs: 'column', sm: 'row' }} width="100%" maxWidth="500px" gap={1}>
              <Box display="flex" width="100%" gap={1} flexDirection={{ xs: 'row', sm: 'row' }}>
                <FormControl size="small" sx={{ flex: 1 }}>
                  <InputLabel>Type</InputLabel>
                  <Select
                    select
                    name="healthCareIdType"
                    value={clientSearchParams?.healthCareIdType}
                    onChange={handleParamsValueChange}
                    onKeyDown={handleKeyDown}
                    input={<OutlinedInput label="Type" />}
                  >
                    {clientSearchParams?.healthCareIdType && (
                      <MenuItem value="">
                        <em>None</em>
                      </MenuItem>
                    )}
                    {idTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl size="small" sx={{ flex: 1 }}>
                  <InputLabel>Issuer</InputLabel>
                  <Select
                    select
                    name="healthCareIdIssuer"
                    value={clientSearchParams?.healthCareIdIssuer}
                    onChange={handleParamsValueChange}
                    onKeyDown={handleKeyDown}
                    input={<OutlinedInput label="Issuer" />}
                    renderValue={(selected) => {
                      return getIssuerDisplayName(selected);
                    }}
                  >
                    {clientSearchParams?.healthCareIdIssuer && (
                      <MenuItem value="">
                        <em>None</em>
                      </MenuItem>
                    )}
                    {issuers.map((issuer) => (
                      <MenuItem key={issuer.code} value={issuer.code}>
                        {issuer.displayName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              <Box sx={{ width: '100%' }}>
                <TextField
                  label="Value"
                  name="healthCareIdValue"
                  value={clientSearchParams?.healthCareIdValue}
                  onChange={handleParamsValueChange}
                  onKeyDown={handleKeyDown}
                  fullWidth
                />
              </Box>
            </Box>
          </Grid>
        )}

        {emailAllowed && (
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField
              label="Email"
              name="emailAddress"
              value={clientSearchParams?.emailAddress}
              onChange={handleParamsValueChange}
              onKeyDown={handleKeyDown}
            />
          </Grid>
        )}
        {phoneAllowed && (
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField
              label="Phone Number"
              name="primaryPhoneNumber"
              value={clientSearchParams?.primaryPhoneNumber}
              onChange={handleParamsValueChange}
              onKeyDown={handleKeyDown}
            />
          </Grid>
        )}
        {genderAllowed && (
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField
              label="Gender"
              select
              name="gender"
              value={clientSearchParams?.gender}
              onChange={handleParamsValueChange}
              onKeyDown={handleKeyDown}
            >
              {clientSearchParams?.gender && (
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
              )}
              {genderList.map((type) => (
                <MenuItem key={type} value={type}>
                  {type}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
        )}
        {dobAllowed && (
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <LocalizationProvider required dateAdapter={AdapterDayjs}>
              <DatePicker
                label="Date of Birth"
                maxDate={dayjs()}
                value={clientSearchParams?.dateOfBirth ? dayjs(clientSearchParams?.dateOfBirth) : null}
                onChange={(e) => {
                  handleParamsValueChange({
                    target: {
                      name: 'dateOfBirth',
                      value: e === null ? null : dayjs(e).format('YYYY-MM-DD'),
                    },
                  });
                }}
                format={'YYYY-MM-DD'}
                slots={{
                  textField: TextField,
                }}
                slotProps={{
                  textField: {
                    required: false,
                    error: false,
                    onKeyDown: handleKeyDown,
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>
        )}
        <Grid container spacing={Constants.formFieldSpacing} pt={'16px'} pb={'8px'} justifyContent="flex-end">
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              disabled={isSearchButtonDisabled()}
              onClick={handleClientSearch}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
}
