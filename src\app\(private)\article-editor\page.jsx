import { dehydrate, HydrationBoundary, QueryClient } from '@tanstack/react-query';
import ClientPage from './ClientPage';
import { PRIVATE, ARTICLES } from '@/lib/constant';
import { getQueryClient } from '@/lib/reactQueryClient';
import { server_getArtifactListByVisibility } from '@/actions/artifactRepository';

export default async function Page() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: [ARTICLES, PRIVATE],
    queryFn: () => server_getArtifactListByVisibility({ artifactType: ARTICLES, visibility: PRIVATE }),
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ClientPage />
    </HydrationBoundary>
  );
}
