import { getServerSession } from 'next-auth';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import ThemeRegistry from '@/components/providers/ThemeRegistry.jsx';
import SessionProvider from '@/components/providers/SessionProvider';
import ReactQueryClientProvider from '@/components/providers/ReactQueryClientProvider';
import CustomSnackbarProvider from '@/components/providers/CustomSnackbarProvider';
import './global.css';
import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';
import { Roboto } from 'next/font/google';

export const metadata = {
  title: process.env.NEXT_PUBLIC_APP_NAME || 'Cambian',
  description: process.env.NEXT_PUBLIC_APP_DESCRIPTION,
  icons: {
    icon: `/${process.env.NEXT_PUBLIC_ICON_NAME}`,
    apple: `/${process.env.NEXT_PUBLIC_APPLE_ICON_NAME}`,
  },
};

const roboto = Roboto({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
});

export default async function RootLayout({ children }) {
  const session = await getServerSession(authOptions);

  return (
    <html lang="en">
      <head>
        <base href="/" />
      </head>
      <body className={roboto.className}>
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <SessionProvider session={session}>
          <ReactQueryClientProvider>
            <ThemeRegistry options={{ key: 'mui' }}>
              <CustomSnackbarProvider>
                <main id="root">{children}</main>
              </CustomSnackbarProvider>
            </ThemeRegistry>
            <ReactQueryDevtools initialIsOpen={false} />
          </ReactQueryClientProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
