import React, { useEffect, useState } from 'react';
import { Checkbox, Autocomplete, TextField, Box } from '@mui/material';
import { Controller } from 'react-hook-form';
import { createFilterOptions } from '@mui/material/Autocomplete';

export const MultiSelect = ({
  sx,
  items,
  selectedValues,
  setSelectedValues,
  label,
  getOptionLabel,
  getOptionSelected,
  isOptionEqualToValue,
  limitTags,
  control,
  controllerName,
  disabled,
  multipleIdentification,
}) => {
  const allSelected = items.length && items.length <= selectedValues.length;
  const [disabledOptions, setDisabledOptions] = useState([]);

  useEffect(() => {
    if (!multipleIdentification && selectedValues.length > 0) {
      setDisabledOptions(items.filter((item) => !selectedValues.includes(item)));
    } else {
      setDisabledOptions([]);
    }
  }, [selectedValues, items, multipleIdentification]);

  const handleChange = (event, selectedOptions, reason, field) => {
    if (reason === 'selectOption' || reason === 'removeOption') {
      if (selectedOptions.find((option) => option === 'Select All')) {
        if (!allSelected) {
          field.onChange(items);
          setSelectedValues && setSelectedValues(items);
        } else {
          field.onChange([]);
          setSelectedValues && setSelectedValues([]);
        }
      } else {
        setSelectedValues && setSelectedValues(selectedOptions);
        field.onChange(selectedOptions);
      }
    } else if (reason === 'clear') {
      setSelectedValues && setSelectedValues([]);
      field.onChange([]);
    }
  };

  const optionRenderer = (props, option, { selected }) => {
    const selectAllProps = option === 'Select All' && !multipleIdentification ? { checked: allSelected } : {};

    if (option === 'Select All' && multipleIdentification) {
      return (
        <li name={option} {...props} checked={!selected}>
          <Checkbox size="small" name={option} checked={allSelected} {...selectAllProps} />
          {option}
        </li>
      );
    }
    const isDisabled = multipleIdentification === false && !selected && disabledOptions.includes(option);

    return (
      <li
        name={getOptionLabel(option)}
        {...props}
        disabled={isDisabled}
        style={{
          pointerEvents: isDisabled ? 'none' : 'auto',
          opacity: isDisabled ? 0.5 : 1,
        }}
      >
        <Checkbox
          size="small"
          name={getOptionLabel(option)}
          checked={selected}
          {...selectAllProps}
          disabled={isDisabled}
        />
        {getOptionLabel(option)}
      </li>
    );
  };

  const inputRenderer = (params) => <TextField {...params} label={label} />;

  const filter = createFilterOptions();

  return (
    <Controller
      control={control}
      name={controllerName}
      defaultValue={[]}
      render={({ field: { ref, ...field } }) => (
        <Autocomplete
          sx={sx}
          {...field}
          multiple
          size="small"
          limitTags={limitTags}
          disabled={disabled}
          options={items}
          value={selectedValues}
          disableCloseOnSelect
          disableClearable
          getOptionLabel={getOptionLabel}
          getOptionSelected={getOptionSelected}
          isOptionEqualToValue={isOptionEqualToValue}
          filterOptions={(options, params) => {
            const filtered = filter(options, params);
            return controllerName.includes('allowedIssuers')
              ? ['Select All', ...filtered]
              : multipleIdentification
                ? ['Select All', ...filtered]
                : filtered;
          }}
          onChange={(event, selectedOptions, reason) => handleChange(event, selectedOptions, reason, field)}
          renderOption={optionRenderer}
          renderInput={inputRenderer}
        />
      )}
    />
  );
};

MultiSelect.defaultProps = {
  limitTags: 5,
  selectedValues: [],
  getOptionLabel: (option) => option,
  getOptionSelected: (option, anotherOption) => option === anotherOption,
  isOptionEqualToValue: (option, value) => option === value,
  items: [],
};
