import { <PERSON>, Divider, <PERSON>u<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useTheme, TextField } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { PanelBorder } from '@cambianrepo/ui';
import { useTranslation } from 'react-i18next';

export default function SelectableMenuList({
  headerButtonText,
  handleButtonClickCallback,
  handleItemClickCallback,
  listData,
  getRoleName,
  getRoleId,
  selectedRole,
  setSelectedRole,
}) {
  const theme = useTheme();
  const { t } = useTranslation();

  const [searchTerm, setSearchTerm] = useState('');
  const [filteredData, setFilteredData] = useState(listData);

  useEffect(() => {
    const sortedAndFiltered = listData
      .filter((item) => getRoleName(item).toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => getRoleName(a).localeCompare(getRoleName(b)));

    setFilteredData(sortedAndFiltered);
  }, [searchTerm, listData, getRoleName]);

  const handleMenuItemClick = (role) => {
    setSelectedRole(role);
    handleItemClickCallback(role.id);
  };

  const handleButtonClick = () => {
    setSelectedRole(null);
    handleButtonClickCallback();
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const selectedMenuItemStyle = {
    backgroundColor: `${theme.palette.primary.main} !important`,
    color: 'white',
  };

  return (
    <>
      <PanelBorder>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <TextField
            label={t('Search')}
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ marginTop: 1, marginLeft: 1, width: '300px' }}
          />
          <Button variant="text" onClick={handleButtonClick} sx={{ fontSize: 17, paddingRight: 1, marginTop: 1 }}>
            {headerButtonText}
          </Button>
        </Box>
        <Divider sx={{ color: `${theme.palette.primary.main} !important`, mt: 1 }} />
        <MenuList>
          {filteredData.map((role) => (
            <MenuItem
              key={getRoleId(role)}
              selected={selectedRole && selectedRole.id === getRoleId(role)}
              onClick={() => handleMenuItemClick(role)}
              sx={{ ...(selectedRole && selectedRole.id === getRoleId(role) && selectedMenuItemStyle) }}
            >
              {getRoleName(role)}
            </MenuItem>
          ))}
        </MenuList>
      </PanelBorder>
    </>
  );
}
