import NextAuth from 'next-auth';
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials';
import { signInWithMfa, refreshTokenAuth, getMachineAccessTokenForEnv } from '@/lib/auth/cognito';
import { SESSION_TIME } from '@/lib/constant';
import { HUMAN, NETWORK, ORGANIZATION, MACHINE_ACCESS_TOKEN, MACHINE_REFRESH_TOKEN } from '@/lib/constant';

export const authOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    CredentialsProvider({
      id: 'cognito-mfa',
      name: 'CognitoLogin',
      credentials: {
        verificationCode: { label: 'verificationCode', type: 'text' },
        session: { label: 'session', type: 'text' },
        challengeName: { label: 'challengeName', type: 'text' },
        username: { label: 'username', type: 'text' },
      },
      async authorize(credentials) {
        const { verificationCode, session, username, challengeName } = credentials ?? {};

        // Check to ensure all required credentials were passed in
        if (!verificationCode || !session || !username || !challengeName) return null;

        const [signInResult, signInError] = await signInWithMfa(credentials);

        if (signInError) {
          throw signInError.toNodeError();
        }

        if (signInResult) {
          const promises = [];
          promises.push(getMachineAccessTokenForEnv(ORGANIZATION));
          promises.push(getMachineAccessTokenForEnv(NETWORK));
          const results = await Promise.all(promises);
          results.forEach((result, index) => {
            const [res, error] = result;
            if (error) {
              console.log(`Failed to get machine token for ${res.env}`);
            } else {
              signInResult[MACHINE_ACCESS_TOKEN(res.env)] = res.accessToken;
              signInResult[MACHINE_REFRESH_TOKEN(res.env)] = res.refreshToken;
            }
          });
        }

        return signInResult;
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    // Seconds - How long until an idle session expires and is no longer valid.
    maxAge: SESSION_TIME,
  },
  debug: process.env.NODE_ENV !== 'production',
  callbacks: {
    async session({ session, user, token }) {
      session.user = {
        email: token.email ?? null,
        orgId: token.orgId ?? null,
        orgUserId: token.orgUserId ?? null,
        idToken: token.idToken ?? null,
        accessToken: token.accessToken ?? null,
      };
      session.user[MACHINE_ACCESS_TOKEN(ORGANIZATION)] = token[MACHINE_ACCESS_TOKEN(ORGANIZATION)];
      session.user[MACHINE_ACCESS_TOKEN(NETWORK)] = token[MACHINE_ACCESS_TOKEN(NETWORK)];
      // console.log('Session Callback');
      // console.log("Token", token)
      // console.log("Session", session)
      return session;
    },
    async jwt({ token, user, account, profile, isNewUser }) {
      if (user) {
        token.email = user.email;
        token.orgId = user.orgId;
        token.orgUserId = user.orgUserId;
        token.idToken = user.idToken;
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
        token[MACHINE_ACCESS_TOKEN(ORGANIZATION)] = user[MACHINE_ACCESS_TOKEN(ORGANIZATION)];
        token[MACHINE_REFRESH_TOKEN(ORGANIZATION)] = user[MACHINE_REFRESH_TOKEN(ORGANIZATION)];
        token[MACHINE_ACCESS_TOKEN(NETWORK)] = user[MACHINE_ACCESS_TOKEN(NETWORK)];
        token[MACHINE_REFRESH_TOKEN(NETWORK)] = user[MACHINE_REFRESH_TOKEN(NETWORK)];
        token.cognitoTokensExpiry = Date.now() + (user.expires_in - 10) * 1000;
      }
      //console.log('jwt callback');
      // console.log("User", user)
      // console.log('Token', token);

      console.log('Token time left in miliseconds: ', token.cognitoTokensExpiry - Date.now());
      if (Date.now() >= token.cognitoTokensExpiry) {
        token.cognitoTokensExpiry = Date.now() + SESSION_TIME;
        const promises = [];
        promises.push(
          refreshTokenAuth({
            cognitoPool: HUMAN,
            refreshToken: token.refreshToken,
            userId: token.orgUserId,
          }),
        );
        promises.push(
          refreshTokenAuth({
            cognitoPool: ORGANIZATION,
            refreshToken: token[MACHINE_REFRESH_TOKEN(ORGANIZATION)],
            userId: JSON.parse(process.env.COGNITO_ORG_MACHINE_CREDENTIALS || '{}').username,
          }),
        );
        promises.push(
          refreshTokenAuth({
            cognitoPool: NETWORK,
            refreshToken: token[MACHINE_REFRESH_TOKEN(NETWORK)],
            userId: JSON.parse(process.env.COGNITO_NETWORK_MACHINE_CREDENTIALS).username,
          }),
        );
        const results = await Promise.all(promises);
        results.forEach((result, index) => {
          const [res, error, cognitoPool] = result;
          if (error) {
            console.error('jwt callback refreshTokenAuth error:', error);
            if (cognitoPool === HUMAN) {
              return { ...token, refreshTokenError: 'Failed to refresh token' };
            }
          } else {
            if (cognitoPool === HUMAN) {
              token.accessToken = res.AuthenticationResult.AccessToken;
              token.idToken = res.AuthenticationResult.IdToken;
              token.cognitoTokensExpiry = Date.now() + (res.AuthenticationResult.ExpiresIn - 10) * 1000;
            } else {
              token[MACHINE_ACCESS_TOKEN(cognitoPool)] = res.AuthenticationResult.AccessToken;
            }
          }
        });
      }
      return token;
    },
  },
  pages: {
    signIn: '/',
    // // signOut: '/signout',
    // error: '/error',
    // verifyRequest: '/verify-request',
    // newUser: '/signup'
  },
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
