import { getArtifact } from '@/lib/api/artifactRepository';
import { PRIVATE, ARTICLES } from '@/lib/constant';

export async function transformArticleData(articleData) {
  const transformedData = {
    ...articleData,
    body: articleData.body || '',
  };

  try {
    if (articleData.bodyPresignedUrl) {
      const bodyResponse = await fetch(articleData.bodyPresignedUrl);
      if (bodyResponse.ok) {
        transformedData.body = await bodyResponse.text();
      }
    }

    if (articleData.thumbnail?.data) {
      transformedData.thumbnail = {
        ...articleData.thumbnail,
        data: articleData.thumbnail.data,
      };
    } else if (articleData.thumbnailPresignedUrl) {
      try {
        const thumbnailResponse = await fetch(articleData.thumbnailPresignedUrl);
        if (thumbnailResponse.ok) {
          const contentType = thumbnailResponse.headers.get('Content-Type') || 'image/png';
          const fileName = thumbnailResponse.headers.get('x-amz-meta-filename') || 'thumbnail.png';
          const blob = await thumbnailResponse.blob();

          transformedData.thumbnail = {
            contentType,
            fileName,
            data: blob,
          };
        }
      } catch (error) {
        console.error('Error fetching thumbnail:', error);
      }
    }
  } catch (error) {
    console.error('Error transforming article data:', error);
  }

  return transformedData;
}

export const uploadThumbnailToS3 = async (thumbnailPresignedUrl, thumbnailData) => {
  if (!thumbnailPresignedUrl || !thumbnailData) {
    console.warn('Missing thumbnail data or presigned URL');
    return null;
  }

  try {
    const response = await fetch(thumbnailPresignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': thumbnailData.contentType || 'image/png',
      },
      body: thumbnailData.data,
    });

    if (!response.ok) {
      throw new Error(`Thumbnail upload failed: ${response.statusText}`);
    }
    return response;
  } catch (error) {
    console.error('Thumbnail upload error:', error);
    throw error;
  }
};
