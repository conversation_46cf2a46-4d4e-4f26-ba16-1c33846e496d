import { TableCell, TableHead, TableRow } from '@mui/material';
import { visuallyHidden } from '@mui/utils';
import TableSortLabel from '@mui/material/TableSortLabel';
import PropTypes from 'prop-types';
import { Box } from '@mui/material';

function EnhancedTableHead(props) {
  const { order, orderBy, onRequestSort, headCells, emptyHeadCellCount } = props;
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };
  var elements = [];
  if (emptyHeadCellCount && emptyHeadCellCount > 0) {
    for (var i = 0; i < emptyHeadCellCount; i++) {
      elements.push(<TableCell key={i} padding="normal"></TableCell>);
    }
  }

  return (
    <TableHead>
      <TableRow>
        {headCells.map((headCell) => (
          <TableCell
            key={headCell.id}
            align="left"
            padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? order : false}
          >
            {headCell.skipSorting ? (
              headCell.label
            ) : (
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : 'asc'}
                onClick={createSortHandler(headCell.id)}
              >
                {headCell.label}
                {orderBy === headCell.id ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            )}
          </TableCell>
        ))}
        {elements}
      </TableRow>
    </TableHead>
  );
}

export { EnhancedTableHead };

EnhancedTableHead.propTypes = {
  onRequestSort: PropTypes.func.isRequired,
  order: PropTypes.oneOf(['asc', 'desc']).isRequired,
  orderBy: PropTypes.string.isRequired,
  headCells: PropTypes.array,
  emptyHeadCellCount: PropTypes.string,
};
