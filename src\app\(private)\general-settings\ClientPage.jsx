'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getOrgSettings } from '@/lib/api/orgData';
import { GeneralSettingsEditor } from './Editors';
import useNotification from '@/lib/hooks/useNotification';
import { ORGANIZATION_ID, ORGANIZATION_SETTINGS } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

// TODO: Org Data is the source of truth and all data should be retrieved from there.
function ClientPage() {
  const openSnackbar = useNotification();
  const queryClient = useQueryClient();

  const orgSettingsQuery = useQuery({ queryKey: [ORGANIZATION_SETTINGS], queryFn: getOrgSettings });

  const orgData = {
    ...orgSettingsQuery.data,
  };

  const handleProfileSaveCallback = async ({ allValues, dirtyValues }) => {
    console.log('allValues from form to API:', allValues);
    console.log('dirtyValues from form to API:', dirtyValues);
    try {
      await updateOrgData(allValues, dirtyValues);

      queryClient.setQueryData([ORGANIZATION_SETTINGS], allValues);
      queryClient.invalidateQueries([ORGANIZATION_SETTINGS]);
    } catch (error) {
      console.log(error);
      openSnackbar({
        variant: 'error',
        msg: 'Something went wrong while saving settings. Please contact technical support.',
      });
    }
  };

  const updateOrgData = async (allValues, dirtyValues) => {
    const { ...profileData } = dirtyValues;

    if (Object.keys(profileData).length > 0) {
      await updateOrgDataProfile(allValues);
    }
  };

  const updateOrgDataProfile = (allValues) => {
    const orgDataValues = { ...allValues };

    return fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/organization-configuration`, {
      method: 'PUT',
      body: JSON.stringify({ ...orgDataValues }),
    });
  };

  return <GeneralSettingsEditor orgMetaData={orgData} handleProfileFormSaveCallback={handleProfileSaveCallback} />;
}

export default ClientPage;
