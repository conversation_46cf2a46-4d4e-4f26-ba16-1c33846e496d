import React from 'react';
import { AppBar, Toolbar, Typography, Box, Link } from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import { Branding } from './header/Branding';
import { HeaderStyle } from '@cambianrepo/ui';
import { useTheme } from '@mui/styles';

function AccessDenied() {
  const theme = useTheme();

  return (
    <>
      <AppBar position="fixed" color="default" sx={{ height: '75px' }}>
        <Toolbar>
          <Branding />
        </Toolbar>
      </AppBar>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: 'calc(100vh - 75px)',
          textAlign: 'center',
        }}
      >
        <LockOutlinedIcon sx={{ fontSize: 100, opacity: 0.1 }} />
        <HeaderStyle>Access Denied</HeaderStyle>
        <Typography variant="subtitle1">You don&apos;t have permissions to access this page.</Typography>
        <Link href="/" style={{ marginTop: 20 }}>
          Back to Home
        </Link>
      </Box>
    </>
  );
}

export default AccessDenied;
