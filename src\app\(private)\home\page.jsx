'use client';
import { useEffect } from 'react';
import { HeaderStyle } from '@cambianrepo/ui';
import { NETWORK, ORGANIZATION, MACHINE_ACCESS_TOKEN } from '@/lib/constant';
import useNotification from '@/lib/hooks/useNotification';
import { useSession } from 'next-auth/react';

export default function HomePage() {
  let { data: session, status } = useSession();
  const openSnackbar = useNotification();

  useEffect(() => {
    if (status !== 'unauthenticated') {
      if (!session?.user[MACHINE_ACCESS_TOKEN(ORGANIZATION)] && !session?.user[MACHINE_ACCESS_TOKEN(NETWORK)]) {
        console.error('Failed to authenticate organization machine user');
        openSnackbar({
          variant: 'error',
          msg: 'Failed to authenticate machine users',
        });
      } else if (!session?.user[MACHINE_ACCESS_TOKEN(ORGANIZATION)]) {
        console.error('Failed to authenticate organization machine user');
        openSnackbar({
          variant: 'error',
          msg: 'Failed to authenticate organization machine user',
        });
      } else if (!session?.user[MACHINE_ACCESS_TOKEN(NETWORK)]) {
        console.error('Failed to authenticate network machine user');
        openSnackbar({
          variant: 'error',
          msg: 'Failed to authenticate network machine user',
        });
      }
    }
  }, [session]);

  return (
    <>
      <HeaderStyle>Home</HeaderStyle>
    </>
  );
}
