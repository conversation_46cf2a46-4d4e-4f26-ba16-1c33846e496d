import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import ClientPage from './ClientPage';
import { CREATE, QUESTIONNAIRES } from '@/lib/constant';
import { isValidV4UUID } from '@/lib/utility';
import { getQueryClient } from '@/lib/reactQueryClient';
import { server_getArtifact } from '@/actions/artifactRepository';

export default async function Page({ params: { artifactId }, searchParams: { visibility } }) {
  const queryClient = getQueryClient();

  if (artifactId !== CREATE && isValidV4UUID(artifactId)) {
    await queryClient.prefetchQuery({
      queryKey: [QUESTIONNAIRES, artifactId],
      queryFn: () =>
        server_getArtifact({ artifactType: QUESTIONNAIRES, artifactId, visibility, includeMetadata: true }),
    });
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ClientPage />
    </HydrationBoundary>
  );
}
