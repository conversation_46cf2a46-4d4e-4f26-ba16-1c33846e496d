import React from 'react';
import { TwoColumnPage } from '@cambianrepo/ui';
import RoleEditor from './RoleEditor';
import SelectableMenuList from './SelectableMenuList';
import { HeaderStyle } from '@cambianrepo/ui';

export function OrgRoleEditor({
  listData,
  roleNameData,
  selectedRole,
  setSelectedRole,
  fetchFeaturesData,
  fetchRoleFeaturesData,
  handleItemClickCallback,
  handleButtonClickCallback,
  handleFormSaveCallback,
  proceedWithDelete,
  isRightColumnVisible,
  setIsRightColumnVisible,
}) {
  return (
    <>
      <HeaderStyle>Role Settings</HeaderStyle>
      <TwoColumnPage
        leftColumn={
          <SelectableMenuList
            headerButtonText="New Role"
            listData={listData}
            getRoleName={(item) => item.name}
            getRoleId={(item) => item.id}
            handleItemClickCallback={handleItemClickCallback}
            handleButtonClickCallback={handleButtonClickCallback}
            selectedRole={selectedRole}
            setSelectedRole={setSelectedRole}
          />
        }
        rightColumn={
          isRightColumnVisible && (
            <RoleEditor
              selectedRole={selectedRole}
              handleFormSaveCallback={handleFormSaveCallback}
              listData={listData}
              roleNameData={roleNameData}
              proceedWithDelete={proceedWithDelete}
              setSelectedRole={setSelectedRole}
              setIsRightColumnVisible={setIsRightColumnVisible}
              fetchFeaturesData={fetchFeaturesData}
              fetchRoleFeaturesData={fetchRoleFeaturesData}
            />
          )
        }
      />
    </>
  );
}

export default OrgRoleEditor;
