// https://tanstack.com/query/latest/docs/framework/react/guides/advanced-ssr#initial-setup
import { QueryCache, QueryClient } from '@tanstack/react-query';

export default function makeServerQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
        retry: 1, // At a development phase, our endpoints are faulty. Only retry once. Default is 3.
      },
      // dehydrate: {
      //   // This is to streaming with server component https://tanstack.com/query/latest/docs/framework/react/guides/advanced-ssr#streaming-with-server-components
      //   // per default, only successful Queries are included,
      //   // this includes pending Queries as well
      //   shouldDehydrateQuery: (query) => defaultShouldDehydrateQuery(query) || query.state.status === 'pending',
      // },
    },
    queryCache: new QueryCache({
      onError: (error) => {
        console.log('Server/React Query Error: Something went wrong', error);
      },
    }),
  });
}
