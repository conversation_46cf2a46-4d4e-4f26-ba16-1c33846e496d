import React from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { Tooltip, Box } from '@mui/material';
import * as uuid from 'uuid';
import { calcAge, getAddress } from '@/lib/api/services/utility';

export function CoordinatorClientList(props) {
  const {
    clientsData,
    clientSelectedHandler,
    handleChangePage,
    handleSortParamsChange,
    totalElements,
    paginationMode,
    sortingMode,
    firstNameAllowed,
    middleNameAllowed,
    lastNameAllowed,
    dobAllowed,
    emailAllowed,
    phoneAllowed,
    genderAllowed,
    idAllowed,
    addressAllowed,
  } = props;

  const rows = clientsData.map((client) => {
    const primaryHealthCareId = client.healthCareIds.find((id) => id.primary);
    const healthCareIdTypeAndValue = primaryHealthCareId
      ? `${primaryHealthCareId.type} ${primaryHealthCareId.issuer} ${primaryHealthCareId.value}`
      : '';
    const primaryEmailAddress = client.emailAddresses.find((email) => email.primary)?.emailAddress || '';
    const primaryPhoneNumber = client.phoneNumbers.find((phone) => phone.primary)?.phoneNumber || '';

    return {
      id: client.clientId || uuid.v4(),
      firstName: client.firstName || '',
      middleName: client.middleName || '',
      lastName: client.lastName || '',
      healthCareIdTypeAndValue,
      dateOfBirth: client.dateOfBirth || '',
      age: `${calcAge(client.dateOfBirth)} years`,
      gender: client.gender || '',
      primaryPhoneNumber,
      emailAddress: primaryEmailAddress,
      address: getAddress(client.addresses),
    };
  });

  const allowedColumns = [
    { field: 'firstName', headerName: 'First Name', flex: 1, allowed: firstNameAllowed },
    { field: 'middleName', headerName: 'Middle Name', flex: 1, allowed: middleNameAllowed },
    { field: 'lastName', headerName: 'Last Name', flex: 1, allowed: lastNameAllowed },
    { field: 'healthCareIdTypeAndValue', headerName: 'Healthcare ID', flex: 1, allowed: idAllowed },
    {
      field: 'dateOfBirth',
      headerName: 'Date of Birth (Age)',
      flex: 1,
      allowed: dobAllowed,
      renderCell: (params) => `${params.value} (${params.row.age})`,
    },
    { field: 'gender', headerName: 'Gender', flex: 1, allowed: genderAllowed },
    { field: 'primaryPhoneNumber', headerName: 'Phone Number', flex: 1, allowed: phoneAllowed },
    { field: 'emailAddress', headerName: 'Email', flex: 1, allowed: emailAllowed },
    { field: 'address', headerName: 'Address', flex: 2, allowed: addressAllowed },
  ];

  const columns = allowedColumns
    .filter((col) => col.allowed)
    .map((col) => ({
      ...col,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    }));

  return (
    <Box>
      <DataGrid
        autoHeight
        pagination
        paginationMode={paginationMode}
        sortingMode={sortingMode}
        rows={rows}
        columns={columns}
        rowCount={+totalElements}
        onPaginationModelChange={handleChangePage}
        onRowClick={clientSelectedHandler}
        onSortModelChange={handleSortParamsChange}
        initialState={{ pagination: { paginationModel: { pageSize: 5 } } }}
        pageSizeOptions={[5, 10, 15, 20, 100]}
      />
    </Box>
  );
}

export default CoordinatorClientList;
