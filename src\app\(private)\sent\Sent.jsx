'use client';

import { Box } from '@mui/material';
import { PanelBorder } from '@cambianrepo/ui';
import { MessagingContainer, MessageFilters, useMessagingFilters } from '@cambianrepo/messaging';
import { useMessages } from '@/hooks/useMessages';

export default function Sent() {
  // Use our shared filters hook to manage filter state
  const {
    searchText,
    dateRange,
    startDate,
    endDate,
    sortModel,
    dateRangePresets,
    selectedDateRangeOption,

    // Handlers
    handleSearchChange,
    handleSearchClear,
    handleDateRangeChange,
    handleStartDateChange,
    handleEndDateChange,
    handleSortModelChange,
  } = useMessagingFilters({
    type: 'sent',
    initialFilters: {
      dateRange: 'Last 7 Days',
      sortModel: [{ field: 'sent', sort: 'desc' }],
    },
  });

  // Use our custom hook to handle API integration
  const { messages, isLoading, error, selectedMessageId, handleMessageClick } = useMessages('sent', {
    startDate,
    endDate,
    sortModel,
    searchText,
  });

  return (
    <PanelBorder>
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Shared Filters Component */}
        <MessageFilters
          searchText={searchText}
          dateRange={dateRange}
          startDate={startDate}
          endDate={endDate}
          dateRangePresets={dateRangePresets}
          selectedDateRangeOption={selectedDateRangeOption}
          onSearchChange={handleSearchChange}
          onSearchClear={handleSearchClear}
          onDateRangeChange={handleDateRangeChange}
          onStartDateChange={handleStartDateChange}
          onEndDateChange={handleEndDateChange}
        />

        {/* Shared Messaging Container */}
        <Box sx={{ p: { xs: 1, sm: 2, md: 2 }, flexGrow: 1 }}>
          <MessagingContainer
            type="sent"
            messages={messages}
            onMessageClick={handleMessageClick}
            selectedMessageId={selectedMessageId}
            isLoading={isLoading}
            error={error}
            sortModel={sortModel}
            onSortModelChange={handleSortModelChange}
            searchText={searchText}
            paginationOptions={{
              defaultPageSize: 10,
              pageSizeOptions: [10, 25, 50],
            }}
          />
        </Box>
      </Box>
    </PanelBorder>
  );
}
