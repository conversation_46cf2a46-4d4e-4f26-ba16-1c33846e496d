'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getOrgReports, getOrgMetaData, getOrganizationConsentAgreement, getOrganizationIcon } from '@/lib/api/common';
import { ReportEditor } from './Editors';
import useNotification from '@/lib/hooks/useNotification';
import { ORGANIZATION_ID, ORGANIZATION_REPORTS, ORGANIZATION_METADATA } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

// TODO: Org Data is the source of truth and all data should be retrieved from there.
function ClientPage() {
  const openSnackbar = useNotification();
  const queryClient = useQueryClient();

  const orgReportsQuery = useQuery({ queryKey: [ORGANIZATION_REPORTS], queryFn: getOrgReports });
  const orgMetaDataQuery = useQuery({ queryKey: [ORGANIZATION_METADATA], queryFn: getOrgMetaData });
  const orgConsentAgreementQuery = useQuery({
    queryKey: ['organizationConsentAgreement'],
    queryFn: getOrganizationConsentAgreement,
  });
  const orgIconQuery = useQuery({ queryKey: ['organizationIcon'], queryFn: getOrganizationIcon });

  const orgData = {
    ...orgReportsQuery.data,
    ...orgMetaDataQuery.data,
    iconUrl: orgIconQuery.data?.iconUrl,
    consentAgreementUrl: orgConsentAgreementQuery.data?.consentAgreementUrl,
  };

  const handleReportSaveCallback = async ({ allValues, dirtyValues }) => {
    console.log('allValues from form to API:', allValues);
    console.log('dirtyValues from form to API:', dirtyValues);
    try {
      // Create a promise array for parallel execution
      const promises = [];
      const messages = [];

      await updateOrgReports(allValues, promises, messages);

      const results = await Promise.all(promises);
      handleResults(results, messages);
      queryClient.setQueryData([ORGANIZATION_REPORTS], allValues);
    } catch (error) {
      console.log(error);
      openSnackbar({
        variant: 'error',
        msg: 'Something went wrong while saving report settings. Please contact technical support.',
      });
    }
  };

  const updateOrgReports = async (allValues, promises, messages) => {
    promises.push(updateOrgDataReports(allValues));
    messages.push('Save Reports Data to Org Data');
  };

  const updateOrgDataReports = (allValues) => {
    return fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/report-settings`, {
      method: 'PUT',
      body: JSON.stringify({ ...allValues }),
    });
  };

  const handleResults = (results, messages) => {
    let hasError = false;
    results.forEach((result, index) => {
      if (!result.ok) {
        hasError = true;
        openSnackbar({ variant: 'error', msg: `${messages[index]} failed` });
      }
    });

    if (!hasError) {
      console.log('Saving report data succeeded');
    }
  };

  return <ReportEditor orgMetaData={orgData} handleReportFormSaveCallback={handleReportSaveCallback} />;
}

export default ClientPage;
