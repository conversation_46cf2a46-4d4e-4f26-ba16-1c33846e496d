'use client';

import React from 'react';
import { AppBar, Toolbar } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Branding } from './Branding';

const useStyles = makeStyles((theme) => ({
  appbar: {
    paddingLeft: '24px',
    paddingRight: '24px',
  },
  appbarBranding: {
    flex: 1,
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'right',
    justifyContent: 'left',
  },
}));

export default function PublicToolbar() {
  const classes = useStyles();

  return (
    <AppBar position="sticky" color="default" sx={{ height: '75px' }}>
      <Toolbar className={classes.appbar}>
        <div className={classes.appbarBranding}>
          <Branding />
        </div>
      </Toolbar>
    </AppBar>
  );
}
