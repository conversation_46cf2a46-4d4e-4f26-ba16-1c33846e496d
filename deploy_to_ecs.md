# Deployment Guide: Deploying a Next.js Application to ECS with ECR

## Overview

This document outlines the steps to deploy a Next.js application on Amazon ECS Fargate using an ECR image. The Docker image will be built and stored in the host account’s ECR, then deployed in the customer account's ECS environment using AWS CloudFormation.

## Prerequisites

### 1.1 Resources

The Core Setup includes the following resources:

1. AWS CLI: Ensure AWS CLI is installed and configured with credentials that have the necessary permissions.

2. **IAM Permissions**:

- Host account:
  `ecr:GetAuthorizationToken`, `ecr:BatchCheckLayerAvailability`, `ecr:PutImage`, `ecr:InitiateLayerUpload`, `ecr:UploadLayerPart`, `ecr:CompleteLayerUpload`
- Customer account: Permissions for ECS deployment (`ecs:RunTask`, `ecs:Describe*`, `ecs:Update*`, etc.).

3. Docker: Installed on the host account for containerization.
4. **npm Token:** Ensure a `.npmrc` file exists at the root of the project with an npm token configured to access any private packages.
5. Ensure to have a VPC setup already done and atleast have two public subnets already created. The Id for VPC and Public subnets should be stored in SSM with the following format: `/${Prefix}/CS/CF/${VisibilityEnv}/VPCID}}`. Where VisibilityEnv can be one `Individual|Network|Organization`.

### 2. Host Actions

### 2.1.1 Next.js Configuration

To make the Next.js app work efficiently in a Docker container, configure the app to output a standalone build.

1. Open your Next.js configuration file, usually next.config.js.
2. Add the following configuration:

```js
const nextConfig = {
  // other configs
  output: 'standalone',
};
module.exports = nextConfig;
```

### 2.1.2 Health Check Route

Add a health check route to verify that the app is running correctly. This will also help ECS determine if the service is healthy.

1. Create a new file at /src/app/api/health/route.js.

2. Add the following code:

```js
export async function GET() {
  return new Response(JSON.stringify({ status: 'healthy' }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}
```

3. This route will respond with `{ "status": "healthy" }` and a `200 OK` status, which ECS can use for health checks.

#### 2.2 Upload App image to ECR by containerizing it using Docker

1. Create a Docker image with the following Dockerfile:
2. Build and push the Docker image to ECR:

```bash
# Authenticate Docker with ECR
aws ecr get-login-password --region <your-region> | docker login --username AWS --password-stdin <your-account-id>.dkr.ecr.<your-region>.amazonaws.com

# Build the Docker image
docker build -t nextjs-app .

# Tag and push to ECR
docker tag nextjs-app:latest <your-account-id>.dkr.ecr.<your-region>.amazonaws.com/nextjs-app:latest
docker push <your-account-id>.dkr.ecr.<your-region>.amazonaws.com/nextjs-app:latest

```

#### 2.3 Set Up Cross-Account Access for ECR Image Sharing

1. Create an ECR Repository Policy in the host account that permits the customer account to pull images:

```json
{
  "Version": "2008-10-17",
  "Statement": [
    {
      "Sid": "AllowCrossAccountECRAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::<customer-account-id>:root"
      },
      "Action": ["ecr:BatchCheckLayerAvailability", "ecr:GetDownloadUrlForLayer", "ecr:BatchGetImage"]
    }
  ]
}
```

NOTE: Remember to replace <customer-account-id> with the actual Id.

2. Attach IAM Role Permissions: In the customer account, ensure that the ECS task execution role has the `ecr:GetAuthorizationToken` permission for the host account’s ECR. This allows the ECS task to authenticate and pull images.

### 2. Host Actions

Using the provided base-template.yaml template file, configure and deploy the ECS resources in the customer account.

1. Modify Parameter Values in samconfig.toml file:

- Set `ApplicationName` to a unique identifier for the app.
- Update the `Prefix` parameter with your chosen prefix.
- Set `EcrImageUri` to the URI of the ECR image from the host account.

2. Deploy teh Stack

```bash
  sam build
  sam deploy
```

3. Verify the Outputs: After successful deployment, check the following output values:

- LoadBalancerDNS: The DNS endpoint for accessing the application.
- ServiceURL: The full URL of the deployed service.

## Troubleshooting Tips

- Permission Errors: Verify IAM permissions in both host and customer accounts.
- Image Pulling Issues: Ensure cross-account access is correctly set on the ECR repository policy.
- Environment Variables: Confirm that environment variables required for the Next.js application are properly configured.
- Ensure all the requried SSM variables already present in correct format and consistent `Prefix` and `VisibilityEnv` is used.
- Containerization Errors:
  - Make sure Docker desktop or daemon is running.
  - If you get Authorization issue while pushing image to ECR try running
    ```bash
      docker logout <account_id>.dkr.ecr.<region>.com
    ```
