import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import ClientPage from './ClientPage';
import { headers } from 'next/headers';
import {
  server_fetchBookingWidgetsList,
  server_fetchQuestionnaireWidgetsList,
  server_fetchRegistrationWidgetsList,
} from '../../../actions/widgetConfig';
import { getOrgSettings } from '@/lib/api/orgData';
import {
  QUESTIONNAIRE_WIDGET_QUERY_KEY,
  REGISTRATION_WIDGET_QUERY_KEY,
  BOOKING_WIDGET_QUERY_KEY,
} from '@/lib/widget-editor/utils/constants';
import { ORGANIZATION_SETTINGS } from '@/lib/constant';
import { getQueryClient } from '@/lib/reactQueryClient';

export default async function Page() {
  //make this page dynamic. This is technically not needed as all our apis have auth headers and that enforce us to opt out of Data Caching https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating#opting-out-of-data-caching
  headers();

  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: [ORGANIZATION_SETTINGS],
    queryFn: getOrgSettings,
  });

  // prefetchQuery does not throw an error when it fails. If this fails, we simply let Client Component retry.
  const promises = [];
  promises.push(
    queryClient.prefetchQuery({
      queryKey: [QUESTIONNAIRE_WIDGET_QUERY_KEY],
      queryFn: server_fetchQuestionnaireWidgetsList,
    }),
  );
  promises.push(
    queryClient.prefetchQuery({
      queryKey: [REGISTRATION_WIDGET_QUERY_KEY],
      queryFn: server_fetchRegistrationWidgetsList,
    }),
  );
  promises.push(
    queryClient.prefetchQuery({
      queryKey: [BOOKING_WIDGET_QUERY_KEY],
      queryFn: server_fetchBookingWidgetsList,
    }),
  );

  await Promise.all(promises);
  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ClientPage />
    </HydrationBoundary>
  );
}
