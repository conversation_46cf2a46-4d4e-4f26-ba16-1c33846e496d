export const ORGANIZATION_ID = 'ORGANIZATION_ID';
export const ORGANIZATION_USER_ID = 'organization-user-id';

export const NO_STORE = 'no-store';
export const ONE_DAY = 24 * 60 * 60;
export const SIX_HOURS = 6 * 60 * 60;

export const ORG_REGISTRY_API_BASE_URL = `${process.env.NEXT_PUBLIC_ORG_REGISTRY_BASE_URL}/organization/registry`;

export const ORG_DATA_API_BASE_URL = `${process.env.NEXT_PUBLIC_ORGANISATION_DATA_BASE_URL}`;
export const CLIENT_INDEX_BASE_URL = `${process.env.NEXT_PUBLIC_CLIENT_INDEX_BASE_URL}`;
export const NEXT_PUBLIC_APPLICATION_TITLE = process.env.NEXT_PUBLIC_APP_SHORT_NAME;
export const ORG_CDR_API_BASE_URL = `${process.env.NEXT_PUBLIC_ORGANISATION_CDR_BASE_URL}`;
export const DOC_GEN_URL = `${process.env.NEXT_PUBLIC_DOC_GEN_BASE_URL}/generate`;
export const ARTIFACT_REPOSITORY_BASE_URL = `${process.env.NEXT_PUBLIC_PRIVATE_ARTIFACT_REPOSITORY_BASE_URL}`;
export const ORG_MESSAGING_API_BASE_URL = `${process.env.NEXT_PUBLIC_ORGANISATION_MESSAGING_BASE_URL}`;
export const ORG_REQUESTS_API_BASE_URL = `${process.env.NEXT_PUBLIC_ORGANISATION_REQUESTS_BASE_URL}`;

export const ORGANIZATION_METADATA = 'ORGANIZATION_METADATA';
export const ORGANIZATION_SETTINGS = 'ORGANIZATION_SETTINGS';
export const ORGANIZATION_VIEWS = 'ORGANIZATION_VIEWS';
export const ORGANIZATION_REPORTS = 'ORGANIZATION_REPORTS';
export const ALL_ID_TYPES_DATA = 'ALL_ID_TYPES_DATA';

export const QUESTIONNAIRES = 'questionnaires';
export const ARTICLES = 'articles';
export const NEWS_ITEMS = 'news-items';
export const PLANS = 'plans';
export const ADS = 'ads';
export const CONSENT_AGREEMENTS = 'consent-agreements';

export const PRIVATE = 'private';
export const PUBLIC = 'public';
export const BOTH = 'both';
export const NO = 'no';

export const FINAL = 'final';
export const DRAFT = 'draft';

export const TYPE_QUESTIONNAIRE_RESPONSE = 'QUESTIONNAIRE_RESPONSE';
export const REQUEST_SOURCE_COORDINATOR = 'COORDINATOR';

export const HUMAN = 'human';
export const NETWORK = 'network';
export const ORGANIZATION = 'organization';
export const MACHINE_ACCESS_TOKEN = (env) => `${env}AccessToken`;
export const MACHINE_REFRESH_TOKEN = (env) => `${env}RefreshToken`;

export const CREATE = 'create';

export const SERVICES = 'services';
export const LOCATIONS = 'locations';

export const idTypesList = ['PHN', 'IFH', 'ULI'];

export const genderList = ['MALE', 'FEMALE', 'OTHER'];

export const countriesAndProvinces = {
  USA: [
    'Alabama',
    'Alaska',
    'Arizona',
    'Arkansas',
    'California',
    'Colorado',
    'Connecticut',
    'Delaware',
    'Florida',
    'Georgia',
    'Hawaii',
    'Idaho',
    'Illinois',
    'Indiana',
    'Iowa',
    'Kansas',
    'Kentucky',
    'Louisiana',
    'Maine',
    'Maryland',
    'Massachusetts',
    'Michigan',
    'Minnesota',
    'Mississippi',
    'Missouri',
    'Montana',
    'Nebraska',
    'Nevada',
    'New Hampshire',
    'New Jersey',
    'New Mexico',
    'New York',
    'North Carolina',
    'North Dakota',
    'Ohio',
    'Oklahoma',
    'Oregon',
    'Pennsylvania',
    'Rhode Island',
    'South Carolina',
    'South Dakota',
    'Tennessee',
    'Texas',
    'Utah',
    'Vermont',
    'Virginia',
    'Washington',
    'West Virginia',
    'Wisconsin',
    'Wyoming',
  ],
  Canada: [
    'Alberta',
    'British Columbia',
    'Manitoba',
    'New Brunswick',
    'Newfoundland and Labrador',
    'Northwest Territories',
    'Nova Scotia',
    'Nunavut',
    'Ontario',
    'Prince Edward Island',
    'Quebec',
    'Saskatchewan',
    'Yukon',
  ],
};

export const healthCareTypes = [
  { id: 'type1', name: 'PHN' },
  { id: 'type2', name: 'IFH' },
  { id: 'type3', name: 'ULI' },
];

export const APP_CODE_CAMBIAN = 'CAMBIAN';

export const toolbarConfig = [
  {
    id: 'editors',
    title: 'Editors',
    icon: 'EditNoteIcon',
    permissions: ['EDITOR_QUESTIONNAIRE', 'EDITOR_NEWS', 'EDITOR_ARTICLE', 'EDITOR_ADS'],
    children: [
      {
        id: 'questionnaires',
        title: 'Questionnaires',
        path: '/questionnaire-editor',
        permission: 'EDITOR_QUESTIONNAIRE',
        icon: 'ListAltIcon',
      },
      { id: 'news', title: 'News', path: '/news-editor', permission: 'EDITOR_NEWS', icon: 'NewspaperIcon' },
      { id: 'articles', title: 'Articles', path: '/article-editor', permission: 'EDITOR_ARTICLE', icon: 'ArticleIcon' },
      { id: 'ads', title: 'Ads', path: '/ads-editor', permission: 'EDITOR_ADS', icon: 'CampaignIcon' },
      {
        id: 'plans',
        title: 'Plans',
        path: '/plan-editor',
        permission: 'EDITOR_ADS', // TODO: update permission
        icon: 'EditCalendarIcon',
      },
      {
        id: 'templates',
        title: 'Templates',
        path: '/templates',
        permission: 'EDITOR_ADS', // TODO: update permission
        icon: 'WebIcon', // TODO: update when we find the icon similar to the one in mockup
      },
      {
        id: 'widgets',
        title: 'Widgets',
        path: '/widget-editor',
        permission: 'ORGANIZATION_WIDGET',
        icon: 'WidgetsIcon',
      },
    ],
  },
  {
    id: 'settings',
    title: 'Settings',
    icon: 'SettingsIcon',
    permissions: ['ORGANIZATION_USER', 'ORGANIZATION_ROLE', 'ORGANIZATION_PROFILE', 'ORGANIZATION_WIDGET'],
    children: [
      {
        id: 'profileSetting',
        title: 'Profile',
        path: '/profile-settings',
        permission: 'ORGANIZATION_PROFILE',
        icon: 'CorporateFareIcon',
      },
      {
        id: 'clientSetting',
        title: 'Clients',
        path: '/client-settings',
        permission: 'CLIENT_INFORMATION',
        icon: 'PeopleAltIcon',
      },
      {
        id: 'userSetting',
        title: 'Users',
        path: '/user-settings',
        permission: 'ORGANIZATION_USER',
        icon: 'PersonIcon',
      },
      { id: 'roleSetting', title: 'Roles', path: '/role-settings', permission: 'ORGANIZATION_ROLE', icon: 'LockIcon' },
      ...(process.env.NEXT_PUBLIC_BUILD_ENV === 'local'
        ? [
            {
              id: 'permissions',
              title: 'Permissions',
              path: '/permission-settings',
              permission: 'CLIENT_INFORMATION',
              icon: 'LockPersonIcon',
            },
          ]
        : []),
      {
        id: 'viewSetting',
        title: 'Views',
        path: '/view-settings',
        permission: 'ORGANIZATION_USER',
        icon: 'GridViewIcon',
      },
      {
        id: 'reportSetting',
        title: 'Reports',
        path: '/report-settings',
        permission: 'ORGANIZATION_USER',
        icon: 'SummarizeIcon',
      },
      {
        id: 'generalSetting',
        title: 'General',
        path: '/general-settings',
        permission: 'ORGANIZATION_USER',
        icon: 'ManageAccountsIcon',
      },
    ],
  },
];

export const menuConfig = [
  {
    id: 'home',
    title: 'Home',
    icon: 'HomeIcon',
    children: [],
    path: '/home',
    alwaysShow: true,
  },
  {
    id: 'network',
    title: 'Network',
    icon: 'PeopleAltIcon',
    permissions: ['CLIENT_INFORMATION', 'CLIENT_GROUP'],
    children: [
      { id: 'clients', title: 'Clients', path: '/clients', permission: 'CLIENT_INFORMATION' },
      { id: 'groups', title: 'Groups', path: '/client-groups', permission: 'CLIENT_GROUP' },
    ],
  },
  {
    id: 'messages',
    title: 'Messages',
    icon: 'MessageIcon',
    permissions: ['COLLABORATION_MESSAGE'],
    children: [
      { id: 'inbox', title: 'Inbox', path: '/inbox', permission: 'COLLABORATION_MESSAGE' },
      { id: 'sent', title: 'Sent', path: '/sent', permission: 'COLLABORATION_MESSAGE' },
      { id: 'messages', title: 'Messages', path: '/messages', permission: 'COLLABORATION_MESSAGE' },
    ],
  },
  {
    id: 'healthServices',
    title: 'Health Services',
    icon: 'VolunteerActivismIcon',
    children: [],
    path: '/health-services',
    alwaysShow: true, // TODO: set up permissions for this
  },
  {
    id: 'healthData',
    title: 'Health Data',
    icon: 'DatabaseIcon',
    permissions: ['CDR_PATIENT', 'CDR_QUESTIONNAIRERESPONSE'],
    children: [
      { id: 'patientResource', title: 'Patient Resource', path: '/patient-resource', permission: 'CDR_PATIENT' },
      {
        id: 'questionnaireResponses',
        title: 'Questionnaire Responses',
        path: '/questionnaire-responses',
        permission: 'CDR_QUESTIONNAIRERESPONSE',
      },
    ],
  },
];

export const breadcrumbConfig = [
  {
    path: '/clients',
    title: 'Clients',
  },
  {
    path: `/clients?client=:uuidClient`,
    title: 'Client',
    parent: '/clients',
  },
  {
    path: '/clients/add-client',
    title: 'Add Client',
    parent: '/clients',
  },
  {
    path: '/episodeOfCare/:uuidClient/create',
    title: 'Create Episode of Care',
    parent: '/clients?client=:uuidClient',
  },
  {
    path: '/episodeOfCare/:uuidClient/:uuid/individualEncounter/create',
    title: 'Create Individual Encounter',
    parent: '/clients?client=:uuidClient',
  },
  {
    path: '/episodeOfCare/:uuidClient/:uuidEpisode/individualEncounter/:uuid',
    title: 'Individual Encounter',
    parent: '/episodeOfCare/:uuidClient/:uuidEpisode/edit',
  },
  {
    path: '/episodeOfCare/:uuidClient/:uuidEpisode/edit',
    title: 'Episode Of Care',
    parent: '/clients?client=:uuidClient',
  },
  {
    path: '/questionnaire-editor',
    title: 'Questionnaires',
  },
  {
    path: '/questionnaire-editor/create',
    title: 'Create Questionnaire',
    parent: '/questionnaire-editor',
  },
  {
    path: '/questionnaire-editor/:uuid?visibility=private',
    title: 'Edit Questionnaire',
    parent: '/questionnaire-editor',
  },
  {
    path: '/article-editor',
    title: 'Articles',
  },
  {
    path: '/article-editor/create',
    title: 'Create Article',
    parent: '/article-editor',
  },
  {
    path: '/article-editor/:uuid',
    title: 'Edit Article',
    parent: '/article-editor',
  },
  {
    path: '/questionnaire-responses',
    title: 'Questionnaire Responses',
  },
  {
    path: '/questionnaire-responses/questionnaire-report',
    title: 'Questionnaire Report',
    parent: '/questionnaire-responses',
  },
  {
    path: '/clients/questionnaire/questionnaire-report?identifier=:uuidClient',
    title: 'Questionnaire Report',
    parent: '/clients?client=:uuidClient',
  },
  {
    path: '/widget-editor',
    title: 'Widgets',
  },
  {
    path: '/widget-editor/booking/create',
    title: 'Add Booking',
    parent: '/widget-editor',
  },
  {
    path: '/widget-editor/questionnaire/create',
    title: 'Add Questionnaire',
    parent: '/widget-editor',
  },
  {
    path: '/widget-editor/registration/create',
    title: 'Add Registration',
    parent: '/widget-editor',
  },
  {
    path: '/widget-editor/questionnaire/:uuid',
    title: 'Edit Questionnaire',
    parent: '/widget-editor',
  },
  {
    path: '/widget-editor/registration/:uuid',
    title: 'Edit Registration',
    parent: '/widget-editor',
  },
  {
    path: '/widget-editor/booking/:uuid',
    title: 'Edit Booking',
    parent: '/widget-editor',
  },
  {
    path: '/workload-tracker',
    title: 'Workload Tracker',
  },
  {
    path: '/aggregate-encounter',
    title: 'Create Aggregate Encounter',
    parent: '/workload-tracker',
  },
  {
    path: '/aggregate-encounter/:uuid',
    title: 'Aggregate Encounter',
    parent: '/workload-tracker',
  },
  {
    path: '/aggregate-encounter/:uuid',
    title: 'Aggregate Encounter',
    parent: '/workload-tracker',
  },
  {
    path: '/class-workshop-session/create',
    title: 'Create Class/Workshop Session',
    parent: '/workload-tracker',
  },
  {
    path: '/class-workshop-session/:uuid',
    title: 'Class/Workshop Session',
    parent: '/workload-tracker',
  },
  {
    path: '/mdt',
    title: 'MDT/Team Members',
  },
  {
    path: '/mdt/create',
    title: 'Create MDT/Team Member',
    parent: '/mdt',
  },
  {
    path: '/mdt/:uuid',
    title: 'Profile',
    parent: '/mdt',
  },
  {
    path: '/mdt/:uuid/createMdtSite',
    title: 'Create MDT/Team Member Site Association',
    parent: '/mdt/:uuid',
  },
  {
    path: '/programs',
    title: 'Programs',
  },
  {
    path: '/program',
    title: 'Create Program',
    parent: '/programs',
  },
  {
    path: '/program/:uuid',
    title: 'Details',
    parent: '/programs',
  },
  {
    path: '/physicians',
    title: 'Physicians',
  },
  {
    path: '/physicians/:uuid',
    title: 'Profile',
    parent: '/physicians',
  },
  {
    path: '/physicians/create',
    title: 'Create Physician',
    parent: '/physicians',
  },
  {
    path: '/site',
    title: 'Sites',
  },
  {
    path: '/site/:uuid',
    title: 'Details',
    parent: '/site',
  },
  {
    path: '/site/create',
    title: 'Create Site',
    parent: '/site',
  },
  {
    path: '/non-clinical-encounters',
    title: 'Encounters',
  },
  {
    path: '/non-clinical-encounters/create',
    title: 'Create Encounter',
    parent: '/non-clinical-encounters',
  },
  {
    path: '/non-clinical-encounters/:uuid',
    title: 'Details',
    parent: '/non-clinical-encounters',
  },
  {
    path: '/physician-activity',
    title: 'Physician Activity',
  },
  {
    path: '/physician-activity/:uuid',
    title: 'Details',
    parent: '/physician-activity',
  },
  {
    path: '/admin-class-workshop',
    title: 'Class/Workshops',
  },
  {
    path: '/admin-class-workshop/:uuid',
    title: 'Series',
    parent: '/admin-class-workshop',
  },
  {
    path: '/admin-class-workshop/:uuid/:uuid',
    title: 'Registrations',
    parent: '/admin-class-workshop/:uuid',
  },
];

export const SESSION_TIME_IN_HOURS = 2;
export const HOUR_TO_SECONDS = 60 * 60;
export const SESSION_TIME = SESSION_TIME_IN_HOURS * HOUR_TO_SECONDS;
export const SESSION_WARNING_TIME_IN_SECONDS = 3 * 60; // 3 minutes
export const MFA_TIME_OUT_IN_SECONDS = 3 * 60; // 3 minutes

export const UNKNOWN_ERROR = 'UNKNOWN_ERROR';

export const clientSettingsHelperText = `
<h1 style="color: #4D76A9;">Display</h1>
<p style="font-weight: 400;">Determines whether this attribute is visible on forms for users to view or input data. If unchecked, the attribute will be hidden.</p>
<p style="font-weight: 400;">When you uncheck the <b>Display</b> option for an attribute and subsequently update the client's information, any data previously entered in that attribute will be deleted. However, if you do not update the client information after unchecking the attribute, the existing data in the attribute will remain unchanged.</p>
<p style="font-weight: 400;"><b>Warning: </b>Before unchecking the <b>Display</b> option for an attribute, ensure that removing the attribute's data is acceptable. Unintended data loss can occur if hidden attributes containing important information are inadvertently updated.</p>

<h1 style="color: #4D76A9;">Require</h1>
<p style="font-weight: 400;">Indicates that at least one value must be provided for this attribute. If the multiple attribute is enabled, the primary value must be provided.</p>
<p style="font-weight: 400;"><b>Warning: </b>Updating the <b>Require</b> setting directly affects form validation. Ensure this setting aligns with your organization's needs to maintain data integrity and user experience.</p>

<h1 style="color: #4D76A9;">Multiple</h1>
<p style="font-weight: 400;">Enables users to provide multiple entries for this attribute. For example, if enabled for email, users can add more than one email address.</p>
<p style="font-weight: 400;">When you uncheck the <b>Multiple</b> option for an attribute and subsequently update the client's information, any non-primary values previously entered in that attribute will be deleted. However, if you do not update the client information after unchecking the attribute, the existing data in the attribute will remain unchanged.</p>
<p style="font-weight: 400;"><b>Warning: </b>Unchecking the <b>Multiple</b> option can lead to the removal of non-primary values upon client information updates. Ensure that any important non-primary data is backed up or no longer needed before making this change.</p>

<h1 style="color: #4D76A9;">Match</h1>
<p style="font-weight: 400;">Determines how the entered values are compared to the existing values stored in the client's information. If all the specified attributes for matching are identical to a record, a match is found.</p>
<p style="font-weight: 400;">Matching for a specific attribute is performed as follows:</p>
<ul>
<li style="font-weight: 400;">Single-attribute type (when the <b>Multiple</b> option is unchecked): The entered value is compared to all existing values for the attribute. If it matches any existing value, a match for that attribute is found.</li>
<li style="font-weight: 400;">Multiple-attribute type (when the <b>Multiple</b> option is checked): If at least one of the entered values matches any of the existing values, a match for that attribute is found.</li>
</ul>


<h1 style="color: #4D76A9;">Identification</h1>
<p style="font-weight: 400;">Refers to a unique credential or document used to verify an individual's identity or affiliation with a specific entity. It typically consists of a specific type, the issuing authority, and a unique value.</p>

<p style="font-weight: 400;"><b>Type</b></p>
<p style="font-weight: 400;">Refers to the classification or category of the identification document (e.g., PHN (Personal Health Number), passport, driver's license, national identification card, or employee badge).</p>

<p style="font-weight: 400;"><b>Issuer</b></p>
<p style="font-weight: 400;">Represents the organizations or authorities responsible for issuing the identification document.</b></p>

<p style="font-weight: 400;"><b>Value</b></p>
<p style="font-weight: 400;">Represents the unique identifier or alphanumeric code assigned to the identification document.</p>

<p style="font-weight: 400;"><b>Example:</b></p>
<ul>
<li style="font-weight: 400;"><b>Type:</b> PHN (Personal Health Number)</li>
<li style="font-weight: 400;"><b>Issuer:</b> Provincial Health Authority (e.g., British Columbia Ministry of Health)</li>
<li style="font-weight: 400;"><b>Value:</b> A unique identifier, such as 1234-567-890</li>
</ul>
<p style="font-weight: 400;">For instance, if you reside in British Columbia, you are issued a <b>BC Services Card</b>. On the back of the card, you will find your <b>PHN</b>, which serves as your unique health identification number.</p>
`;

export const reportSettingsHelperText = `
<h1 style="color: #4D76A9;">Report Header</h1>
<p style="font-weight: 400;">This feature allows you to create and manage your personalized Report Header with dynamic placeholders and HTML formatting for enhanced customization.</p>
<p style="font-weight: 400;">
<ul>
<li>In <b>Edit Mode</b>, you can type your own text and use placeholders like {Client.firstName}, {Client.lastName}, and {Org.tagline} to add personalized details.</li>
<li>You can also include HTML tags (e.g., &lt;b&gt;, &lt;i&gt;, &lt;u&gt;, &lt;p&gt;, etc.) for formatting purposes. For example, typing &lt;b&gt;{Client.firstName}&lt;/b&gt; will bold the client's first name in <b>Preview Mode</b>.</li>
<li>When switching to <b>Preview Mode</b>, these placeholders are replaced with example information, so you can see how it will look once saved.</li>
<li>Any HTML tags you've included will be rendered, allowing you to see the formatted output.</li>
</ul>
</p>
Available Client Placeholders:
<ul>
<li>{Client.firstName}</li>
<li>{Client.middleName}</li>
<li>{Client.lastName}</li>
<li>{Client.dateOfBirth}</li>
<li>{Client.gender}</li>
<li>{Client.email}</li>
<li>{Client.phone}</li>
<li>{Client.preferredContactMethod}</li>
<li>{Client.notifications}</li>
<li>{Client.addressLine}</li>
<li>{Client.city}</li>
<li>{Client.province}</li>
<li>{Client.country}</li>
<li>{Client.postalCode}</li>
<li>{Client.identification}</li>
</ul>
Available Org Placeholders:
<ul>
<li>{Org.name}</li>
<li>{Org.tagline}</li>
<li>{Org.aboutUs}</li>
<li>{Org.address}</li>
<li>{Org.email}</li>
<li>{Org.phone}</li>
<li>{Org.fax}</li>
<li>{Org.consentAgreement}</li>
<li>{Org.logo}</li>
</ul>
Example:
<p style="font-weight: 400;">You can type something like: <b>&lt;b&gt;{Client.lastName}&lt;/b&gt;, &lt;i&gt;{Client.firstName}&lt;/i&gt; {Client.middleName} | {Org.name}</b></p>
<p style="font-weight: 400;">After switching to preview mode, it will show: <b>Doe</b>, <i>John</i> A. | Cambian</p>
`;

export const viewSettingsHelperText = `
<h1 style="color: #4D76A9;">Client Profile Summary</h1>
<p style="font-weight: 400;">This feature allows you to create and manage your personalized Client Profile Summary with dynamic placeholders and HTML formatting for enhanced customization.</p>
<p style="font-weight: 400;">
<ul>
<li>In <b>Edit Mode</b>, you can type your own text and use placeholders like {Client.firstName}, {Client.lastName}, and {Client.identification} to add personalized details.</li>
<li>You can also include HTML tags (e.g., &lt;b&gt;, &lt;i&gt;, &lt;u&gt;, &lt;p&gt;, etc.) for formatting purposes. For example, typing &lt;b&gt;{Client.firstName}&lt;/b&gt; will bold the client's first name in <b>Preview Mode</b>.</li>
<li>When switching to <b>Preview Mode</b>, these placeholders are replaced with example information, so you can see how it will look once saved.</li>
<li>Any HTML tags you've included will be rendered, allowing you to see the formatted output.</li>
</ul>
</p>
Available Placeholders:
<ul>
<li>{Client.firstName}</li>
<li>{Client.middleName}</li>
<li>{Client.lastName}</li>
<li>{Client.dateOfBirth}</li>
<li>{Client.gender}</li>
<li>{Client.email}</li>
<li>{Client.phone}</li>
<li>{Client.preferredContactMethod}</li>
<li>{Client.notifications}</li>
<li>{Client.addressLine}</li>
<li>{Client.city}</li>
<li>{Client.province}</li>
<li>{Client.country}</li>
<li>{Client.postalCode}</li>
<li>{Client.identification}</li>
</ul>
Example:
<p style="font-weight: 400;">You can type something like: <b>&lt;b&gt;{Client.lastName}&lt;/b&gt;, &lt;i&gt;{Client.firstName}&lt;/i&gt; {Client.middleName} | {Client.identification}</b></p>
<p style="font-weight: 400;">After switching to preview mode, it will show: <b>Doe</b>, <i>John</i> A. | PHN BC 123456789</p>
`;
