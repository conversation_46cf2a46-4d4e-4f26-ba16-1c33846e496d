'use client';
import React, { useState, useEffect } from 'react';
import { Grid, Button, InputAdornment, IconButton, Typography, Box } from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';
import useNotification from '@/lib/hooks/useNotification';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { FormContainer, TextFieldElement } from 'react-hook-form-mui';
import { addCsrfToken, clientFetch } from '@/lib/fetch/client';
import { PASSWORD_REQUIREMENTS } from '@/app/globalConstants';

const validatePasswordStrength = (value, createError) => {
  if (!value) return createError({ message: 'Password is required.' });

  const rules = [
    { regex: /.{8,20}/, message: PASSWORD_REQUIREMENTS.STRONG_PASSWORD_MESSAGE },
    { regex: /[a-z]/, message: PASSWORD_REQUIREMENTS.LOWERCASE },
    { regex: /[A-Z]/, message: PASSWORD_REQUIREMENTS.UPPERCASE },
    { regex: /[0-9]/, message: PASSWORD_REQUIREMENTS.NUMERIC },
    { regex: /[!@#$%^&*]/, message: PASSWORD_REQUIREMENTS.SPECIAL },
  ];
  const errors = rules.filter((rule) => !rule.regex.test(value)).map((rule) => rule.message);
  return errors.length ? createError({ message: errors.join('\n') }) : true;
};

const schema = yup.object().shape({
  currentPassword: yup.string().required('Current password is required.'),
  newPassword: yup
    .string()
    .required('New password is required.')
    .test('password-strength', '', (value, context) => validatePasswordStrength(value, context.createError))
    .test('password-match', 'Passwords do not match.', function (value) {
      const { confirmPassword } = this.parent;
      return value === confirmPassword || !confirmPassword; // Match or no confirmPassword
    }),
  confirmPassword: yup
    .string()
    .required('Confirm password is required.')
    .test('password-strength', '', (value, context) => validatePasswordStrength(value, context.createError))
    .test('password-match', 'Passwords do not match.', function (value) {
      const { newPassword } = this.parent;
      return value === newPassword || !newPassword; // Match or no newPassword
    }),
});

const renderErrorMessages = (errorMessage) => (
  <Box sx={{ ml: 3.7, mb: 2, mt: -1 }}>
    {errorMessage?.split('\n').map((msg, index) => (
      <Typography key={index} sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
        {msg}
      </Typography>
    ))}
  </Box>
);

function ChangePasswordPage() {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const openSnackbar = useNotification();

  const formContext = useForm({
    mode: isSubmitted ? 'onChange' : 'onSubmit', // Start with onSubmit, switch to onChange after first submit
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const { formState, control, trigger, reset } = formContext;

  const newPassword = useWatch({ control, name: 'newPassword' });
  const confirmPassword = useWatch({ control, name: 'confirmPassword' });

  // Dynamically re-validate both fields when either changes after submission
  useEffect(() => {
    if (isSubmitted && (newPassword || confirmPassword)) {
      trigger(['newPassword', 'confirmPassword']);
    }
  }, [isSubmitted, newPassword, confirmPassword, trigger]);

  const handleToggleCurrentPasswordVisibility = () => {
    setShowCurrentPassword(!showCurrentPassword);
  };

  const handleToggleNewPasswordVisibility = () => {
    setShowNewPassword(!showNewPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleSubmitButtonClick = () => {
    setIsSubmitted(true); // Switch to onChange validation after first submit
  };

  const handleSubmit = async (data, event) => {
    event.preventDefault();
    const { currentPassword, newPassword, confirmPassword } = data;

    try {
      const result = await clientFetch(addCsrfToken)(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/changePassword`,
        {
          method: 'POST',
          body: JSON.stringify({
            previousPassword: currentPassword,
            newPassword: newPassword,
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      const responseData = await result.json();
      if (result.ok) {
        console.log('Password changed successfully');
        resetForm();
      } else {
        switch (responseData.errorName) {
          case 'NotAuthorizedException':
            openSnackbar({
              variant: 'error',
              msg: 'Incorrect current password. Please try again.',
            });
            break;
          case 'InvalidPasswordException':
            openSnackbar({
              variant: 'error',
              msg: 'New password does not meet security requirements. Please try again.',
            });
            break;
          case 'LimitExceededException':
            openSnackbar({
              variant: 'error',
              msg: 'Too many attempts. Please try again later.',
            });
            break;
          case 'UserNotFoundException':
            openSnackbar({
              variant: 'error',
              msg: 'User does not exist. Please check your account details.',
            });
            break;
          default:
            openSnackbar({
              variant: 'error',
              msg: responseData.message || 'Failed to change password. Please try again.',
            });
        }
      }
    } catch (error) {
      console.error('Failed to change password:', error);
      openSnackbar({
        variant: 'error',
        msg: 'An unexpected error occurred. Please check your internet connection or try again later.',
      });
    }
  };

  const resetForm = () => {
    reset({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setShowCurrentPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  return (
    <>
      <HeaderStyle>Change Password</HeaderStyle>
      <PanelBorder>
        <FormContainer onSuccess={handleSubmit} formContext={formContext}>
          <Typography sx={{ m: 2 }}>Enter your new password from 8-20 characters in length</Typography>
          <Grid item>
            <TextFieldElement
              label="Current Password"
              sx={{ ml: 2, mb: 2 }}
              type={showCurrentPassword ? 'text' : 'password'}
              name="currentPassword"
              required
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={handleToggleCurrentPasswordVisibility} color="primary">
                      {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item>
            <TextFieldElement
              label="New Password"
              sx={{ ml: 2, mb: 2 }}
              type={showNewPassword ? 'text' : 'password'}
              name="newPassword"
              required
              error={!!formState.errors.newPassword}
              FormHelperTextProps={{
                sx: { display: 'none' }, // Hide helperText entirely
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={handleToggleNewPasswordVisibility} color="primary">
                      {showNewPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            {formState.errors.newPassword && renderErrorMessages(formState.errors.newPassword.message)}
          </Grid>
          <Grid item>
            <TextFieldElement
              label="Confirm New Password"
              sx={{ ml: 2, mb: 2 }}
              type={showConfirmPassword ? 'text' : 'password'}
              name="confirmPassword"
              required
              error={!!formState.errors.confirmPassword}
              FormHelperTextProps={{
                sx: { display: 'none' }, // Hide helperText entirely
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={handleToggleConfirmPasswordVisibility} color="primary">
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            {formState.errors.confirmPassword && renderErrorMessages(formState.errors.confirmPassword.message)}
          </Grid>
          <Grid item>
            <Button variant="contained" type="submit" sx={{ ml: 2, mb: 2 }} onClick={handleSubmitButtonClick}>
              Save
            </Button>
          </Grid>
        </FormContainer>
      </PanelBorder>
    </>
  );
}

export default ChangePasswordPage;
