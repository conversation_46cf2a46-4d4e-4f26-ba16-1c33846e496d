export const extractExtension = (extensionArray, url) => {
  let matchingExtension = null;
  if (extensionArray !== undefined && Array.isArray(extensionArray) && url !== undefined && url !== null) {
    matchingExtension =
      extensionArray.find((extension) => extension?.url?.toUpperCase() === url?.toUpperCase()) || null;
  }
  return matchingExtension;
};

export function convertV1toV2Format(questionnaire) {
  const oldUrl = 'http://cambian.com/Questionnaire/';

  const replacer = (key, value) => {
    if (key === 'url' && typeof value === 'string') {
      return value.replace(oldUrl, '');
    }
    if (typeof value === 'string' && value === 'cannot locate string') {
      return null;
    }
    return value;
  };

  const jsonString = JSON.stringify(questionnaire, replacer);
  const updatedJsonObject = JSON.parse(jsonString);
  return updatedJsonObject;
}
