'use client';

import React, { useEffect, useState } from 'react';
import { Grid, Box, Collapse, IconButton, TextField, Button, FormGroup, Paper, Snackbar, Alert } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useRouter, useSearchParams } from 'next/navigation.js';
import { genderList, ORGANIZATION_ID } from '@/lib/constant';
import { CoordinatorClientList } from '@/components/client/CoordinatorClientList';
import { camelToSnake } from '@/lib/api/services/utility';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';
import { makeStyles } from '@mui/styles';
import { styled } from '@mui/material/styles';
import SearchIcon from '@mui/icons-material/Search';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import CloseIcon from '@mui/icons-material/Close';
import { CoordinatorClientSearch } from '@/components/client/CoordinatorClientSearch';
import CreateAndEditClientView from './[id]/view/page';
import { useClientSearchResults } from '@/context/ClientSearchResults';
import Tabs, { tabsClasses } from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import PropTypes from 'prop-types';
import { useQuestionnaire } from '@/context/QuestionnaireContext';
import { usePermissions } from '@/context/UserPermissions';
import useNotification from '@/lib/hooks/useNotification';
import * as Constants from '@/app/globalConstants';
import ResponsesTable from '@/components/questionnaireResponse/ResponsesTable';
import { useTranslation } from 'react-i18next';
import { getOrgMetaData } from '@/lib/api/common';
import { ORGANIZATION_METADATA } from '@/lib/constant';
import { useQuery } from '@tanstack/react-query';
import AddQuestionnaireResponse from '@/components/questionnaireResponse/AddQuestionnaireResponse';
import RequestToRespondToQuestionnaire from '@/components/questionnaireResponse/RequestToRespondToQuestionnaire';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vertical-tabpanel-${index}`}
      aria-labelledby={`vertical-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `vertical-tab-${index}`,
    'aria-controls': `vertical-tabpanel-${index}`,
  };
}

const useStyles = makeStyles((theme) => ({
  input: {
    height: 40,
  },
  accordionClass: {
    //border: `0.5px solid ${theme.palette.divider}`,
  },
  accordionOpenClass: {
    border: `0.5px solid ${theme.palette.divider}`,
  },
}));

const searchParams = {
  firstName: '',
  lastName: '',
  email: '',
  primaryPhoneNumber: '',
  gender: '',
  dateOfBirth: null,
  healthCareIdType: '',
  healthCareIssuer: '',
  healthCareIdValue: '',
};

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& fieldset': {
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderTopLeftRadius: 0,
  borderBottomLeftRadius: 0,
  //textTransform: 'lowercase',
}));

function ClientPage() {
  const [error, setError] = useState(false);
  const { t } = useTranslation();
  const [errorMessageOpen, setErrorMessageOpen] = useState(false);
  const [pageSize, setPageSize] = useState(5);
  const [sortParam, setSortParam] = useState('id,asc');
  const [page, setPage] = useState(0);
  const [lastSearchIsQuickSearch, setLastSearchIsQuickSearch] = useState(false);
  const [lastSearchIsAdvancedSearch, setLastSearchIsAdvancedSearch] = useState(false);
  const [value, setValue] = React.useState(0);
  const { roles } = usePermissions();
  const openSnackbar = useNotification();
  const [hasPractitionerRole, setHasPractitionerRole] = useState(false);
  const [firstNameAllowed, setFirstNameAllowed] = useState(false);
  const [middleNameAllowed, setMiddleNameAllowed] = useState(false);
  const [lastNameAllowed, setLastNameAllowed] = useState(false);
  const [dobAllowed, setDobAllowed] = useState(false);
  const [genderAllowed, setGenderAllowed] = useState(false);
  const [emailAllowed, setEmailAllowed] = useState(false);
  const [phoneAllowed, setPhoneAllowed] = useState(false);
  const [idAllowed, setIdAllowed] = useState(false);
  const [addressAllowed, setAddressAllowed] = useState(false);
  const [showAddResponse, setShowAddResponse] = useState(false);
  const [showRequest, setShowRequest] = useState(false);
  const [clientData, setClientData] = useState({});
  const [loading, setLoading] = useState(true);
  const { setSelectedQuestionnaire, setSelectedRepository, setFilteredChannels, showAddDuplicateResponse } =
    useQuestionnaire();

  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => getOrgMetaData(),
  });

  const orgDataValues = { ...orgMetaDataQuery.data?.clientInformation };
  const healthcareValues = Array.isArray(orgMetaDataQuery.data?.idTypes) ? orgMetaDataQuery.data.idTypes : [];

  const idTypes = healthcareValues.filter((item) => item.allowed).map((item) => item.idType);

  const issuers = healthcareValues
    .flatMap((item) =>
      item.issuers
        .filter((issuer) => issuer.allowed)
        .map((issuer) => ({ code: issuer.issuer, displayName: issuer.displayName })),
    )
    .filter((value, index, self) => index === self.findIndex((i) => i.code === value.code));

  useEffect(() => {
    if (orgMetaDataQuery.isSuccess && orgDataValues) {
      Object.values(orgDataValues).forEach((field) => {
        switch (field.attribute) {
          case 'FIRST_NAME':
            if (field.allowed) {
              setFirstNameAllowed(field.allowed);
            }
            break;
          case 'MIDDLE_NAME':
            if (field.allowed) {
              setMiddleNameAllowed(field.allowed);
            }
            break;
          case 'LAST_NAME':
            if (field.allowed) {
              setLastNameAllowed(field.allowed);
            }
            break;
          case 'DATE_OF_BIRTH':
            if (field.allowed) {
              setDobAllowed(field.allowed);
            }
            break;
          case 'GENDER':
            if (field.allowed) {
              setGenderAllowed(field.allowed);
            }
            break;
          case 'EMAIL':
            if (field.allowed) {
              setEmailAllowed(field.allowed);
            }
            break;
          case 'PHONE':
            if (field.allowed) {
              setPhoneAllowed(field.allowed);
            }
            break;
          case 'IDENTIFICATION':
            if (field.allowed) {
              setIdAllowed(field.allowed);
            }
            break;
          case 'ADDRESS':
            if (field.allowed) {
              setAddressAllowed(field.allowed);
            }
            break;
          default:
            break;
        }
      });
    }
  }, [orgMetaDataQuery.isSuccess, orgDataValues, orgMetaDataQuery.data]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const toggleAcordion = () => {
    setExpand((prev) => {
      if (prev) {
        setSearchParams(searchParams);
      }
      return !prev;
    });
  };
  const classes = useStyles();
  const {
    viewMode,
    setViewMode,
    selectedClientId,
    setSelectedClientId,
    clientsData,
    setClientsData,
    clientSearchParams,
    setSearchParams,
    query,
    setQuery,
    expand,
    setExpand,
  } = useClientSearchResults();
  const [errorSnackbarOpen, setErrorSnackbarOpen] = React.useState(false);
  const [errorSnackbarMessage, setErrorSnackbarMessage] = React.useState('');
  function displayErrorMessage(message) {
    setErrorSnackbarOpen(true);
    setErrorSnackbarMessage(message);
  }

  const router = useRouter();
  const searchUrlParams = useSearchParams();

  useEffect(() => {
    const clientParam = searchUrlParams.get('client');
    if (clientParam) {
      setSelectedClientId(clientParam);
      setViewMode(true);
    } else {
      setSelectedClientId(null);
      setViewMode(false);
    }
  }, [searchUrlParams]);

  useEffect(() => {
    if (clientsData == null) {
      return;
    }
    if (lastSearchIsQuickSearch) {
      clientQuickSearchHandler();
    }
    if (lastSearchIsAdvancedSearch) {
      clientSearchHandler();
    }
  }, [page, pageSize, sortParam]);

  const handleErrorMessageClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setErrorMessageOpen(false);
  };

  const clientSearchHandler = async () => {
    router.push('/clients');
    setLastSearchIsAdvancedSearch(true);
    setViewMode(false);
    const searchParams = {
      ...clientSearchParams,
      page,
      size: pageSize,
      sort: sortParam,
    };
    setSearchParams(searchParams);
    try {
      let queryString = ``;
      for (const key in searchParams) {
        if (searchParams[key] !== null && searchParams[key] !== undefined && searchParams[key] !== '') {
          queryString += `${encodeURIComponent(key)}=${encodeURIComponent(searchParams[key])}&`;
        }
      }
      const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients?${queryString.slice(0, -1)}`);

      const dataResponse = await response.json();
      const res = dataResponse;

      if (!res.clients || res.clients.length === 0) {
        displayErrorMessage(t('No Client found with given information. Please try again.'));
      }
      setClientsData(res);
    } catch (err) {
      setError(true);
      setErrorMessageOpen(true);
    }
  };

  const clientQuickSearchHandler = async () => {
    router.push('/clients');
    setLastSearchIsQuickSearch(true);
    setViewMode(false);
    const matchAnyParam = {
      matchAny: query,
    };
    const searchParams = {
      ...matchAnyParam,
      page,
      size: pageSize,
      sort: sortParam,
    };
    setSearchParams(searchParams);
    try {
      let queryString = ``;
      for (const key in searchParams) {
        if (searchParams[key] !== null && searchParams[key] !== undefined && searchParams[key] !== '') {
          queryString += `${encodeURIComponent(key)}=${encodeURIComponent(searchParams[key])}&`;
        }
      }
      const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients?${queryString.slice(0, -1)}`);

      const dataResponse = await response.json();
      const res = dataResponse;
      if (!res.clients || res.clients.length === 0) {
        displayErrorMessage(t('No Client found with given information. Please try again.'));
      }
      setClientsData(res);
    } catch (err) {
      setError(true);
      setErrorMessageOpen(true);
    }
  };

  const searchResetHandler = () => {
    setSearchParams(null);
    setViewMode(false);
    setQuery('');
    setClientsData(null);
  };

  const handleParamsValueChange = (event) => {
    const { name, value } = event.target;

    const validation = {
      firstName: 100,
      lastName: 100,
      emailAddress: 100,
      primaryPhoneNumber: 100,
      healthCareIdValue: 100,
    };

    if (!validation[name] || value.length <= validation[name]) {
      setSearchParams((prevParams) => ({
        ...prevParams,
        [name]: value,
      }));
    }
    setPage(0);
  };

  const handlePageChange = ({ page, pageSize }, _) => {
    setPage(page);
    setPageSize(pageSize);
  };

  const handleRowsPerPageChange = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSortParamsChange = (paramInfo) => {
    if (!paramInfo.length) return;
    setSortParam(`${camelToSnake(paramInfo[0].field)},${paramInfo[0].sort}`);
  };

  const clientSelectedHandler = (data) => {
    router.push(`/clients?client=${data.id}`);
    setSelectedClientId(data.id);
    setViewMode(true);
    setShowAddResponse(false);
    setShowRequest(false);
  };

  const handleQuickSearch = (e) => {
    setQuery(e.target.value);
    setPage(0);
  };

  const handleQuickSearchClear = () => {
    searchResetHandler();
    router.push('/clients');
  };

  const showErrorSnackbar = () => {
    openSnackbar({
      variant: 'error',
      message: 'An Error Occurred. Please Try Again.',
    });
  };

  useEffect(() => {
    if (error) {
      showErrorSnackbar();
      setError(false);
    }
  }, [error]);

  const handleOpenAddResponse = () => {
    setShowAddResponse(true);
  };

  const handleCancelAddResponse = () => {
    setShowAddResponse(false);
  };

  const handleOpenRequest = () => {
    setShowRequest(true);
  };

  const handleCancelRequest = () => {
    setShowRequest(false);
    setSelectedQuestionnaire('');
    setSelectedRepository('');
    setFilteredChannels('');
  };

  useEffect(() => {
    const hasPractitionerRole = roles.some((role) => role.name === 'Practitioner');
    setHasPractitionerRole(hasPractitionerRole);
  }, [roles]);

  useEffect(() => {
    const fetchClientData = async () => {
      if (selectedClientId) {
        try {
          const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${selectedClientId}`);
          const dataResponse = await response.json();
          const clientData = dataResponse;
          setClientData(clientData);
          setLoading(false);
        } catch (err) {
          console.error('Error fetching data:', err.message);
          displayErrorMessage('Error fetching client data');
        }
      }
    };

    fetchClientData();
  }, [selectedClientId]);

  return (
    <>
      {!viewMode && <HeaderStyle>Clients</HeaderStyle>}
      <PanelBorder>
        <Grid container>
          {!viewMode && (
            <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2}>
              <Grid
                container
                direction="row"
                justifyContent="space-between"
                sx={{ paddingBottom: 0, paddingTop: 2 }}
                className={classes.accordionClass}
              >
                <Grid item>
                  <Box display="flex" justifyContent="flex-start">
                    <FormGroup row>
                      <StyledTextField
                        variant="outlined"
                        label="Search"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        InputProps={{
                          className: classes.input,
                          endAdornment: query && (
                            <IconButton color="primary" aria-label="close" onClick={handleQuickSearchClear} edge="end">
                              <CloseIcon />
                            </IconButton>
                          ),
                        }}
                        sx={{
                          mb: 1,
                          width: { lg: '250px', md: '250px', sm: '100px', xs: '100px' },
                          paddingRight: '0px',
                        }}
                        value={query}
                        onKeyDown={(e) => {
                          if (e.keyCode == 13) {
                            clientQuickSearchHandler();
                          }
                        }}
                        onChange={(e) => {
                          handleQuickSearch(e);
                        }}
                      />
                      <StyledButton
                        disableElevation
                        variant="contained"
                        onClick={clientQuickSearchHandler}
                        style={{ height: '40px', width: '40px', minWidth: 0 }}
                      >
                        <SearchIcon />
                      </StyledButton>
                    </FormGroup>
                    <IconButton
                      aria-label="expand row"
                      size="small"
                      onClick={(e) => toggleAcordion()}
                      sx={{ paddingTop: 0 }}
                    >
                      {expand ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                    </IconButton>
                  </Box>
                </Grid>
                <Grid item>
                  <Box display="flex" justifyContent="flex-end">
                    <Button
                      color="primary"
                      startIcon={<AddIcon width="1" />}
                      onClick={() => {
                        router.push('/clients/add-client');
                      }}
                    >
                      Add Client
                    </Button>
                  </Box>
                </Grid>
              </Grid>
              <Collapse
                in={expand}
                timeout="auto"
                unmountOnExit
                sx={{ paddingRight: 2, paddingLeft: 2, paddingTop: 2, mt: 1, mb: 1 }}
                className={classes.accordionOpenClass}
              >
                <div
                //sx={{ padding: expand ? 5 : 0 }}
                >
                  <CoordinatorClientSearch
                    idTypes={idTypes}
                    issuers={issuers}
                    genderList={genderList}
                    clientSearchHandler={clientSearchHandler}
                    clientSearchParams={clientSearchParams}
                    handleParamsValueChange={handleParamsValueChange}
                    firstNameAllowed={firstNameAllowed}
                    lastNameAllowed={lastNameAllowed}
                    dobAllowed={dobAllowed}
                    emailAllowed={emailAllowed}
                    genderAllowed={genderAllowed}
                    phoneAllowed={phoneAllowed}
                    idAllowed={idAllowed}
                  />
                </div>
              </Collapse>
            </Grid>
          )}
          {viewMode && <Grid item xs={12} sm={12} sx={{ mt: 1 }}></Grid>}
          {clientsData && !clientsData?.clients?.length && <Grid item xs={12} sm={12} sx={{ mt: 3 }}></Grid>}
          {clientsData && clientsData?.clients?.length > 0 && !viewMode && (
            <Grid item xs={12} sm={12} sx={{ mt: 1, paddingRight: 2, paddingLeft: 2 }}>
              <CoordinatorClientList
                clientsData={clientsData?.clients}
                pageSize={pageSize}
                page={page}
                paginationMode="server"
                sortingMode="server"
                handleChangePage={handlePageChange}
                handleChangeRowsPerPage={handleRowsPerPageChange}
                clientSelectedHandler={clientSelectedHandler}
                handleSortParamsChange={handleSortParamsChange}
                totalElements={clientsData?.page?.totalElements}
                firstNameAllowed={firstNameAllowed}
                middleNameAllowed={middleNameAllowed}
                lastNameAllowed={lastNameAllowed}
                dobAllowed={dobAllowed}
                emailAllowed={emailAllowed}
                genderAllowed={genderAllowed}
                phoneAllowed={phoneAllowed}
                idAllowed={idAllowed}
                addressAllowed={addressAllowed}
              />
            </Grid>
          )}
          {viewMode && <CreateAndEditClientView params={{ id: selectedClientId, clientPage: true }} />}
          {viewMode && (
            <Grid container>
              <Grid item xs={12} sm={12} sx={{ pr: 2, pl: 2, pt: 2 }}>
                <Paper>
                  <Box>
                    <Grid
                      container
                      alignItems="center"
                      justifyContent="space-between"
                      sx={{ borderBottom: 1, borderColor: 'divider' }}
                    >
                      <Grid>
                        <Tabs
                          value={value}
                          onChange={handleChange}
                          variant="scrollable"
                          scrollButtons="auto"
                          allowScrollButtonsMobile
                          aria-label="visible arrows tabs example"
                          sx={{
                            [`& .${tabsClasses.scrollButtons}`]: {
                              '&.Mui-disabled': { opacity: 0.3 },
                            },
                          }}
                        >
                          <Tab label="Questionnaires" {...a11yProps(0)} />
                        </Tabs>
                      </Grid>
                    </Grid>
                  </Box>
                  <TabPanel value={value} index={0}>
                    <Box
                      component="main"
                      sx={{
                        flexGrow: 1,
                      }}
                    >
                      {hasPractitionerRole && !showAddResponse && !showRequest && !showAddDuplicateResponse && (
                        <Grid container justifyContent="flex-end" sx={{ pr: 2 }}>
                          <Box>
                            <Button
                              color="primary"
                              size={Constants.buttonSize}
                              startIcon={<AddIcon width="1" />}
                              onClick={handleOpenRequest}
                            >
                              {t('Request')}
                            </Button>
                            <Button
                              color="primary"
                              size={Constants.buttonSize}
                              startIcon={<AddIcon width="1" />}
                              onClick={handleOpenAddResponse}
                            >
                              {t('Add')}
                            </Button>
                          </Box>
                        </Grid>
                      )}
                      {showAddResponse ? (
                        <AddQuestionnaireResponse
                          clientId={selectedClientId}
                          cdrIdentifier={clientData.identifiers?.find((i) => i.type == 'CDR_PATIENT_ID')}
                          targetSubjectType={'Patient'}
                          onCancel={handleCancelAddResponse}
                        />
                      ) : showRequest ? (
                        <RequestToRespondToQuestionnaire
                          clientId={selectedClientId}
                          targetSubjectType={'Patient'}
                          onCancel={handleCancelRequest}
                        />
                      ) : (
                        <ResponsesTable clientId={selectedClientId} />
                      )}
                    </Box>
                  </TabPanel>
                </Paper>
              </Grid>
            </Grid>
          )}
        </Grid>
      </PanelBorder>
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        open={errorSnackbarOpen}
        autoHideDuration={6000}
        onClose={() => setErrorSnackbarOpen(false)}
      >
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => setErrorSnackbarOpen(false)}>
              {t('Dismiss')}
            </Button>
          }
        >
          {errorSnackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
}

export default ClientPage;
