{"name": "Coordinator", "version": "0.2.0", "private": true, "scripts": {"prestart": "node -p \"'export const COMPONENT_UI_VERSION = ' + JSON.stringify(require('./node_modules/@cambianrepo/ui/package.json').version) + ';'\" > src/lib/version.js", "set:commit-id": "node set-commit-id.js", "dev": "cross-env BUILD_ENV=dev next dev -p 3010", "local": "cross-env BUILD_ENV=local next dev -p 3010", "build": "node set-commit-id.js && next build", "build:standalone": "NEXT_OUTPUT_STANDALONE=true next build", "start": "next start", "lint": "next lint --fix", "format": "prettier --write \"./src/**/*.{js,jsx,ts,tsx,css,}\" --config ./.prettierrc", "postinstall": "husky"}, "dependencies": {"@aws-sdk/client-cognito-identity": "^3.461.0", "@aws-sdk/client-cognito-identity-provider": "^3.461.0", "@cambianrepo/articles-editor": "0.0.17", "@cambianrepo/breadcrumbs": "0.0.1", "@cambianrepo/client-info": "0.0.2", "@cambianrepo/messaging": "0.0.7", "@cambianrepo/questionnaire": "0.0.18", "@cambianrepo/questionnaire-editor-v2": "0.0.119", "@cambianrepo/ui": "^0.0.64", "@cambianrepo/user-profile": "0.0.43", "@cambianrepo/widget-editor-v2": "0.0.69", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.15.15", "@mui/lab": "^5.0.0-alpha.160", "@mui/material": "^5.15.15", "@mui/styles": "^5.14.18", "@mui/x-data-grid": "^7.2.0", "@mui/x-date-pickers": "^7.2.0", "@tanstack/react-query": "^5.45.1", "@tanstack/react-query-devtools": "^5.45.1", "axios": "^1.6.7", "client-only": "^0.0.1", "compress.js": "^1.2.2", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "google-maps-react": "^2.0.6", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "moment": "^2.30.1", "next": "^14.2.4", "next-auth": "^4.24.5", "notistack": "^3.0.1", "react": "^18.2.0", "react-avatar-editor": "13.0.0", "react-beautiful-dnd": "^13.1.1", "react-copy-to-clipboard": "^5.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-hook-form": "^7.56.1", "react-hook-form-mui": "^7.6.0", "react-i18next": "^14.1.2", "react-localization": "^1.0.19", "react-places-autocomplete": "^7.3.0", "react-quill": "^2.0.0", "react-quilljs": "^2.0.4", "react-router-dom": "^6.0.2", "react-to-print": "^2.15.1", "server-only": "^0.0.1", "sharp": "0.32.6", "uuid": "^9.0.1", "yup": "^1.3.3"}, "devDependencies": {"cross-env": "^7.0.3", "dotenv-expand": "^11.0.6", "dotenv-flow": "^4.1.0", "eslint": "^8", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.1.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}