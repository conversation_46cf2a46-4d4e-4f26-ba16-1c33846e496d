import { <PERSON>, Divider, <PERSON>u<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useTheme, TextField } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { PanelBorder } from '@cambianrepo/ui';
import { useTranslation } from 'react-i18next';

function SelectableMenuList({
  headerButtonText,
  handleButtonClickCallback,
  handleItemClickCallback,
  usersData,
  getItemName,
  getItemId,
  selectedUserId,
}) {
  const theme = useTheme();
  const { t } = useTranslation();

  const [searchTerm, setSearchTerm] = useState('');
  const [filteredData, setFilteredData] = useState(usersData);
  const [selectedIndex, setSelectedIndex] = useState(null);

  // Get ID type of the list.
  const firstItem = usersData?.[0];
  const idType = firstItem ? typeof getItemId(firstItem) : null;

  useEffect(() => {
    if (idType != null && idType !== 'number' && idType !== 'string') {
      console.error("Item's id should be either number or string");
    }
  }, [idType]);

  useEffect(() => {
    if (searchTerm === '') {
      setFilteredData(usersData);
    } else {
      const lowercasedFilter = searchTerm.toLowerCase();
      const filteredList = usersData.filter((item) => {
        return `${getItemName(item)}`.toLowerCase().includes(lowercasedFilter);
      });
      setFilteredData(filteredList);
    }
  }, [searchTerm, usersData]);

  useEffect(() => {
    const newIndex = filteredData.findIndex((item) => getItemId(item) === selectedUserId);
    setSelectedIndex(newIndex);
  }, [filteredData, getItemId, selectedUserId]);

  const handleMenuItemClick = (event, index) => {
    const selectedValue = idType === 'number' ? Number(event.target.dataset.value) : event.target.dataset.value;
    setSelectedIndex(index);
    handleItemClickCallback(selectedValue);
  };

  const handleButtonClick = () => {
    setSelectedIndex(null);
    handleButtonClickCallback();
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const selectedMenuItemStyle = {
    backgroundColor: `${theme.palette.primary.main} !important`,
    color: 'white',
  };

  return (
    <>
      <PanelBorder>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <TextField
            label={t('Search')}
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ marginTop: 1, marginLeft: 1, width: '300px' }}
          />
          <Button variant="text" onClick={handleButtonClick} sx={{ fontSize: 17, paddingRight: 1, marginTop: 1 }}>
            {headerButtonText}
          </Button>
        </Box>
        <Divider sx={{ color: `${theme.palette.primary.main} !important`, mt: 1 }} />
        <MenuList>
          {filteredData.map((data, index) => (
            <MenuItem
              key={getItemId(data)}
              selected={selectedIndex === index}
              data-value={getItemId(data)}
              onClick={(event) => handleMenuItemClick(event, index)}
              sx={{ ...(selectedIndex === index && selectedMenuItemStyle) }}
            >
              {getItemName(data)}
            </MenuItem>
          ))}
        </MenuList>
      </PanelBorder>
    </>
  );
}

export default SelectableMenuList;
