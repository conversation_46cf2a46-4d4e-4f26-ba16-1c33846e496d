import { NextResponse } from 'next/server';
import AwsError from '@/lib/error/AwsError';
import { retrievePDFDocument } from '@/lib/api/server/docGenServiceForServer';

const postEndpoint = async (req) => {
  try {
    const { formData } = await req.json();
    try {
      //Post request to get docgen
      const dataResponse = await retrievePDFDocument(req, formData);
      const nextResponse = NextResponse.json({ dataResponse }, { status: dataResponse.status });
      return nextResponse;
    } catch (err) {
      const awsError = new AwsError(err);
      return awsError.toNextResponse();
    }
  } catch (err) {
    console.error('Error in postEndpoint:', err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};
export const POST = postEndpoint;
