import { NextResponse } from 'next/server';
import AwsError from '@/lib/error/AwsError';
import {
  ARTICLES,
  ADS,
  PLANS,
  NEWS_ITEMS,
  QUESTIONNAIRES,
  NO_STORE,
  CONSENT_AGREEMENTS,
  ORGANI<PERSON><PERSON>ION_ID,
  PRIVATE,
  ORGANIZATION,
  NETWORK,
} from '@/lib/constant';
import { getArtifactRepoUrlByVisibility } from '@/lib/utility';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';

const validArtifactTypes = [QUESTIONNAIRES, NEWS_ITEMS, ARTICLES, PLANS, ADS, CONSENT_AGREEMENTS];
/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const putArtifact = async (req, { params: { visibility, artifactType, artifactId } }) => {
  const BASE_URL = getArtifactRepoUrlByVisibility(visibility);
  if (!BASE_URL) {
    return NextResponse.json({ message: 'visibility should be private or public' }, { status: 400 });
  }

  if (!validArtifactTypes.includes(artifactType)) {
    return NextResponse.json({ message: 'Unsupported Artifact Type' }, { status: 400 });
  }

  const { publishStatus, contentStatus, ...restRequestBody } = await req.json();

  // TODO: Validate statuses
  if (!restRequestBody) {
    return NextResponse.json({ message: 'Artifact is empty' }, { status: 400 });
  }

  try {
    const env = visibility === PRIVATE ? ORGANIZATION : NETWORK;
    const res = await fetchWithMiddleware(addMachineAccessToken(req, env), addUserToken(req, { replaceOrgId: true }))(
      `${BASE_URL}/organizations/${ORGANIZATION_ID}/${artifactType}/${artifactId}`,
      {
        method: 'PUT',
        body: JSON.stringify({
          publishStatus,
          contentStatus,
          ...restRequestBody,
        }),
      },
    );
    const responseBody = await res.json();

    const response = NextResponse.json({ responseBody }, { status: res.status });
    return response;
  } catch (err) {
    console.log(err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

/**
 * @param {Request} req
 * @returns {Promise<>}
 */
const getArtifact = async (req, { params: { visibility, artifactType, artifactId } }) => {
  const BASE_URL = getArtifactRepoUrlByVisibility(visibility);
  if (!BASE_URL) {
    return NextResponse.json({ message: 'visibility should be private or public' }, { status: 400 });
  }

  if (!validArtifactTypes.includes(artifactType)) {
    return NextResponse.json({ message: 'Unsupported Artifact Type' }, { status: 400 });
  }

  try {
    const env = visibility === PRIVATE ? ORGANIZATION : NETWORK;
    const res = await fetchWithMiddleware(addMachineAccessToken(req, env), addUserToken(req, { replaceOrgId: true }))(
      `${BASE_URL}/organizations/${ORGANIZATION_ID}/${artifactType}/${artifactId}?includeMetadata=true`,
      {
        cache: NO_STORE,
      },
    );
    const responseBody = await res.json();

    const response = NextResponse.json({ responseBody }, { status: res.status });
    return response;
  } catch (err) {
    console.log('er', err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const deleteArtifact = async (req, { params: { visibility, artifactType, artifactId } }) => {
  const BASE_URL = getArtifactRepoUrlByVisibility(visibility);
  if (!BASE_URL) {
    return NextResponse.json({ message: 'visibility should be private or public' }, { status: 400 });
  }

  if (!validArtifactTypes.includes(artifactType)) {
    return NextResponse.json({ message: 'Unsupported Artifact Type' }, { status: 400 });
  }

  try {
    const env = visibility === PRIVATE ? ORGANIZATION : NETWORK;
    const res = await fetchWithMiddleware(addMachineAccessToken(req, env), addUserToken(req, { replaceOrgId: true }))(
      `${BASE_URL}/organizations/${ORGANIZATION_ID}/${artifactType}/${artifactId}`,
      {
        method: 'DELETE',
      },
    );

    console.log('DELETE', res);
    const response = NextResponse.json({}, { status: res.status });
    return response;
  } catch (err) {
    console.log('er', err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const GET = getArtifact;
export const PUT = putArtifact;
export const DELETE = deleteArtifact;
// TODO: Perhaps validate CSRF token here.
