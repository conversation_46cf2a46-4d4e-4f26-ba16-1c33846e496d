'use client';
import { HeaderStyle } from '@cambianrepo/ui';
import React from 'react';
import dayjs from 'dayjs';
import { access } from '@/components/questionnaireResponse/utility/tableSortingUtil';
import { format } from 'date-fns';
import { DateQuery } from '@/components/questionnaireResponse/DateQuery';

const questionnaireResponsesHeadCells = [
  {
    id: 'resource.name',
    disablePadding: false,
    label: 'Title',
    getRowValue: (id, object) => {
      for (const extension of object.resource.extension) {
        if (extension.url.includes('questionnaire-name')) {
          if (extension.valueString) {
            return extension.valueString;
          } else {
            return '-';
          }
        }
      }
      return '-';
    },
  },
  {
    id: 'resource.authored',
    disablePadding: false,
    label: 'Date',
    getRowValue: (id, object) => {
      let value = access(id, object);
      if (value) {
        return format(new Date(value), 'yyyy-MM-dd h:mm a');
      }
      return '--';
    },
  },
  {
    id: 'patient',
    disablePadding: false,
    label: 'Subject',
    skipSorting: true,
    getRowValue: (id, object, patientInfo) => {
      let value = access(id, object);
      if (value) {
        return value;
      }
      return '--';
    },
  },
  {
    id: 'source',
    disablePadding: false,
    label: 'Source',
    skipSorting: true,
    getRowValue: (id, object, patientInfo) => {
      if (object.resource.source && object.resource.source.reference) {
        const sourceId = object.resource.source.reference.split('/')[1];
        for (const info of patientInfo) {
          if (info.resource.id === sourceId) {
            if (info.resource.name && info.resource.name.length > 0) {
              if (info.resource.resourceType === 'Location') {
                return info.resource.name;
              }
              for (const pName of info.resource.name) {
                return pName.given[0] + ' ' + pName.family;
              }
            }
          }
        }
      }
      return '--';
    },
  },
  {
    id: 'author',
    disablePadding: false,
    label: 'Author',
    skipSorting: true,
    getRowValue: (id, object, patientInfo) => {
      if (object.resource.author && object.resource.author.reference) {
        const authorId = object.resource.author.reference.split('/')[1];
        for (const info of patientInfo) {
          if (info.resource.id === authorId) {
            if (info.resource.name && info.resource.name.length > 0) {
              if (info.resource.resourceType === 'Location') {
                return info.resource.name;
              }
              for (const pName of info.resource.name) {
                return pName.given[0] + ' ' + pName.family;
              }
            }
          }
        }
      }
      return '--';
    },
  },
  {
    id: 'score',
    disablePadding: false,
    label: 'Score',
    skipSorting: true,
    getRowValue: (id, object) => {
      let scores = [];
      for (const extension of object.resource.extension) {
        if (extension.url.includes('calculated-scores')) {
          if (extension.extension.length > 0) {
            extension.extension.forEach((scoreExtension) => {
              if (scoreExtension.valueString) {
                scores.push(scoreExtension.valueString);
              }
            });
          }
        }
      }
      return scores.length > 0 ? scores.join(', ') : '-';
    },
  },
];

const resultsPerPageData = [20, 50, 100, { value: -1, label: 'All' }];

const dateRangeData = [
  {
    label: 'Last 7 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(7, 'day'), today];
    },
  },
  {
    label: 'Last 14 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(14, 'day'), today];
    },
  },
  {
    label: 'Last 21 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(21, 'day'), today];
    },
  },
  {
    label: 'Last Calendar Month',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(1, 'month'), today];
    },
  },
  {
    label: 'Last Three Calendar Months',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(3, 'month'), today];
    },
  },
  {
    label: 'Last Twelve Calendar Months',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(1, 'year'), today];
    },
  },
  {
    label: 'Current Calendar Month',
    getValue: () => {
      const today = dayjs();
      return [today.startOf('month'), today];
    },
  },
  {
    label: 'Custom',
    getValue: () => {
      return [null, null];
    },
  },
];

export default function Page() {
  return (
    <>
      <HeaderStyle>Questionnaire Responses</HeaderStyle>
      <DateQuery
        dateRangeData={dateRangeData}
        resultsPerPageData={resultsPerPageData}
        headCells={questionnaireResponsesHeadCells}
        showResultsPerPage={true}
      />
    </>
  );
}
