'use server';

import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';
import { ORG_DATA_API_BASE_URL, NO_STORE, ORGANIZATION_ID } from '@/lib/constant';
import ApiError from '@/lib/error/ApiError';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';

export const server_loadOrganization = async () => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(`${ORG_DATA_API_BASE_URL}/organizations/${ORGANIZATION_ID}`, {
      cache: NO_STORE,
    });

    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data.message });
    }
    return data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const server_getAllIdTypes = async () => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: false }),
    )(`${ORG_DATA_API_BASE_URL}/id-types`, {
      cache: NO_STORE,
    });

    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data.message });
    }
    return data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};
