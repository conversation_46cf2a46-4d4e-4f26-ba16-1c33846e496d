import {
  Stack,
  Button,
  Box,
  Checkbox,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormHelperText,
} from '@mui/material';
import { FormContainer, TextFieldElement } from 'react-hook-form-mui';
import React, { useEffect, useState } from 'react';
import { dirtyValues } from '@/lib/utility';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { DoublePanelBorder } from '@cambianrepo/ui';
import { usePermissions } from '@/context/UserPermissions';

function DeleteConfirmationDialog({ open, onClose, onConfirm, selectedRole }) {
  return (
    <Dialog sx={{ '& .MuiDialog-paper': { width: 'auto' } }} maxWidth="xs" open={open}>
      <DialogTitle>Delete role &quot;{selectedRole?.name}&quot;?</DialogTitle>
      <DialogContent>
        <Typography>This action cannot be undone.</Typography>
      </DialogContent>
      <DialogActions>
        <Button autoFocus variant="outlined" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={onConfirm} variant="contained" color="error">
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function RoleEditor({
  selectedRole,
  handleFormSaveCallback,
  roleNameData,
  proceedWithDelete,
  fetchFeaturesData,
  fetchRoleFeaturesData,
}) {
  const schema = yup
    .object()
    .shape({
      name: yup
        .string()
        .required()
        .test({
          name: 'isUnique',
          message: 'This role already exists',
          params: { roleNameData, selectedRole },
          test: (value) => {
            const roleData = selectedRole || { id: undefined, name: '', features: [] };
            const isNameUnique = !roleNameData.some((role) => role.name === value.trim() && role.id !== roleData.id);
            return isNameUnique;
          },
        }),
      features: yup.array().ensure().min(1, 'Please select at least one feature'),
    })
    .required();

  const [allFeatures, setAllFeatures] = useState([]);
  const [isDialogOpen, setDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [initialFeatures, setInitialFeatures] = useState([]);
  const { fetchPermissionsData } = usePermissions();

  const formContext = useForm({
    defaultValues: {
      name: selectedRole?.name || '',
      features: selectedRole?.features || [],
    },
    resolver: yupResolver(schema),
  });

  const { setValue, formState, reset } = formContext;
  const { isDirty, errors } = formState;
  const openDialog = () => setDialogOpen(true);

  const fetchData = async () => {
    const featuresData = await fetchFeaturesData();
    setAllFeatures(featuresData);
  };

  const fetchRoleFeatures = async () => {
    if (selectedRole && selectedRole.id) {
      const roleFeaturesData = await fetchRoleFeaturesData();
      setValue('features', roleFeaturesData);
      setInitialFeatures(roleFeaturesData);
    }
  };

  useEffect(() => {
    fetchData();
  }, [fetchFeaturesData]);

  useEffect(() => {
    fetchRoleFeatures();
  }, [selectedRole, fetchRoleFeaturesData, setValue]);

  useEffect(() => {
    reset({
      name: selectedRole?.name || '',
      features: selectedRole?.features || [],
    });
  }, [selectedRole, reset]);

  const handleSubmit = async (data) => {
    if (isSaving) return;
    setIsSaving(true);
    try {
      const dirtyData = dirtyValues(formState.dirtyFields, data);
      const roleData = selectedRole || { id: undefined, name: '', features: [] };
      await handleFormSaveCallback({ allValues: data, dirtyValues: dirtyData, selectedRole: roleData });
      formContext.reset({ name: data.name, features: data.features });

      const featuresChanged = JSON.stringify(data.features) !== JSON.stringify(initialFeatures);
      if (featuresChanged) {
        await fetchPermissionsData();
      }
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      <DoublePanelBorder>
        <FormContainer onSuccess={handleSubmit} formContext={formContext}>
          <Stack spacing={2}>
            <TextFieldElement label="Name" name="name" />
          </Stack>
          <br />
          Permissions
          <Box display="flex">
            <Paper sx={{ width: '500px', overflow: 'hidden' }}>
              <TableContainer sx={{ maxHeight: '55vh' }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Features</TableCell>
                      <TableCell>Enabled</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {allFeatures?.map((feature, index) => (
                      <TableRow key={index}>
                        <TableCell>{feature}</TableCell>
                        <TableCell align="center" style={{ width: '10%' }}>
                          <Controller
                            name={`features`}
                            control={formContext.control}
                            render={({ field }) => (
                              <Checkbox
                                {...field}
                                size="small"
                                sx={{ padding: 0 }}
                                value={feature}
                                checked={field.value.includes(feature)}
                                onChange={(event, checked) => {
                                  // field.value is `features` value
                                  if (checked) {
                                    field.onChange([...field.value, event.target.value]);
                                  } else {
                                    field.onChange(field.value.filter((feature) => feature !== event.target.value));
                                  }
                                }}
                              />
                            )}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
          {errors.features && <FormHelperText error>{errors.features.message}</FormHelperText>}
          <br />
          <Stack spacing={2}>
            <Box width="auto">
              {selectedRole && selectedRole.id && (
                <Button autoFocus variant="outlined" color="error" onClick={openDialog} sx={{ marginRight: '8px' }}>
                  Delete
                </Button>
              )}
              <Button variant="contained" type="submit" disabled={!isDirty || isSaving}>
                Save
              </Button>
            </Box>
          </Stack>
          <DeleteConfirmationDialog
            open={isDialogOpen}
            onClose={() => setDialogOpen(false)}
            onConfirm={proceedWithDelete}
            selectedRole={selectedRole}
          />
        </FormContainer>
      </DoublePanelBorder>
    </>
  );
}

export default RoleEditor;
