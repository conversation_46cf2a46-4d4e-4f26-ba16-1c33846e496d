export const xsFormFieldSize = 12;
export const smFormFieldSize = 6;
export const mdFormFieldSize = 3;
export const formFieldSpacing = 1;
export const buttonSize = 'small';
export const tableSize = 'small';
export const textFieldSize = 'small';
export const saveButtonText = 'Save';
export const addButtonText = 'Add';
export const cancelButtonText = 'Cancel';
export const updateButtonText = 'Update';
export const backButtonText = 'Back';
export const submitButtonText = 'Submit';

export const PASSWORD_REQUIREMENTS = {
  LENGTH: { min: 8, max: 20 },
  LOWERCASE: 'The password requires lowercase character(s)',
  UPPERCASE: 'The password requires uppercase character(s)',
  NUMERIC: 'The password requires numeric character(s)',
  SPECIAL: 'The password requires special (!@#$%^&*) character(s)',
  STRONG_PASSWORD_MESSAGE: 'Please enter a stronger password from 8 to 20 characters in length',
};
