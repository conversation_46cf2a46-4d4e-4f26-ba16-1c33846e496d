import { <PERSON>ack, <PERSON><PERSON>, Box, Switch, FormControlLabel, MenuItem, InputLabel, Select, FormControl } from '@mui/material';
import { FormContainer, TextFieldElement } from 'react-hook-form-mui';
import React, { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { dirtyValues } from '../../../../../lib/utility';
import * as Constants from '@/app/globalConstants';
import Loader from '@/components/Loader';
import { QUESTIONNAIRE_CAPS } from '@/lib/widget-editor/utils/constants';
import { server_fetchQuestionnaireWidgetsList } from '@/actions/widgetConfig';

function GeneralSettingsTabPanel({ orgMetaData, handleFormSaveCallback }) {
  // Initialize form with default values
  const formContext = useForm({
    defaultValues: {
      autoPublishOrgProfile: false,
      googleApiId: '',
      dynamicWidgetId: '',
    },
  });

  const { formState, control, watch } = formContext;
  const [loading, setLoading] = useState(false);
  const [dynamicWidgets, setDynamicWidgets] = useState([]);
  const [widgetsLoading, setWidgetsLoading] = useState(true);

  const dynamicWidgetId = watch('dynamicWidgetId', '');

  useEffect(() => {
    if (orgMetaData) {
      formContext.reset({
        autoPublishOrgProfile: orgMetaData.autoPublishOrgProfile || false,
        googleApiId: orgMetaData.googleApiId || '',
        dynamicWidgetId: orgMetaData.dynamicWidgetId || '',
      });
    }
  }, [orgMetaData]);

  useEffect(() => {
    const fetchDynamicWidgets = async () => {
      setWidgetsLoading(true);
      try {
        const questionnaireWidgets = await server_fetchQuestionnaireWidgetsList();
        const dynamicWidgets = questionnaireWidgets.filter((widget) => widget.dynamicWidget === true);
        const formattedWidgets = dynamicWidgets.map((widget) => ({
          id: widget.SK.split('#')[1],
          name: widget.name,
          type: QUESTIONNAIRE_CAPS,
        }));
        setDynamicWidgets(formattedWidgets);
      } catch (error) {
        console.error('Error fetching dynamic widgets:', error);
      } finally {
        setWidgetsLoading(false);
      }
    };

    fetchDynamicWidgets();
  }, []);

  const handleSubmit = async (orgMetaData) => {
    setLoading(true);
    try {
      const dirtyFields = { ...formState.dirtyFields };
      const dirtyData = dirtyValues(dirtyFields, orgMetaData);
      await handleFormSaveCallback({
        allValues: orgMetaData,
        dirtyValues: dirtyData,
      });
    } catch (error) {
      console.error('Error saving data:', error);
    } finally {
      setLoading(false);
    }
  };

  const isFormChanged = () => Object.keys(formState.dirtyFields).length > 0;

  return (
    <>
      {widgetsLoading && <Loader active={loading} />}
      <FormContainer onSuccess={handleSubmit} formContext={formContext}>
        <Stack spacing={Constants.formFieldSpacing}>
          <Controller
            name="autoPublishOrgProfile"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={<Switch {...field} checked={!!field.value} />}
                label="Publish Profile to Network"
              />
            )}
          />
          <TextFieldElement label="Google API ID" name="googleApiId" />
          <Controller
            name="dynamicWidgetId"
            control={control}
            render={({ field }) => (
              <FormControl sx={{ width: '100%' }} size="small">
                <InputLabel id="dynamic-widget-select-label">Select Dynamic Widget</InputLabel>
                <Select
                  labelId="dynamic-widget-select-label"
                  label="Select Dynamic Widget"
                  value={field.value || ''}
                  onChange={(e) => {
                    field.onChange(e);
                  }}
                >
                  {dynamicWidgetId && (
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                  )}
                  {dynamicWidgets.map((widget) => (
                    <MenuItem key={widget.id} value={widget.id}>
                      {widget.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
          <Box width="auto" paddingTop={2} marginTop={2}>
            <Button variant="contained" type="submit" disabled={loading || !isFormChanged()}>
              Save
            </Button>
          </Box>
        </Stack>
      </FormContainer>
    </>
  );
}

export default GeneralSettingsTabPanel;
