'use client';

import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';
import { TwoColumnPage } from '@cambianrepo/ui';
import { SingleColumnPage } from '@cambianrepo/ui';
import { DoublePanelBorder } from '@cambianrepo/ui';
import { ComponentBorder } from '@cambianrepo/ui';
import { Grid, Box, TextField, Button, MenuItem, Link, IconButton, CircularProgress, Typography } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import SearchIcon from '@mui/icons-material/Search';

function LeftHandComponent() {
  return (
    <PanelBorder>
      <Grid container justifyContent="center">
        <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2} paddingTop={2}>
          Example Left
        </Grid>
      </Grid>
    </PanelBorder>
  );
}

function RightHandComponent() {
  return <DoublePanelBorder>Example Right</DoublePanelBorder>;
}

export default function Page() {
  function renderContent() {
    return <div>Margin left is set to 1, margin right is set to 2.</div>;
  }

  const idTypes = ['PHN'];

  return (
    <>
      <Typography variant="h2">Headers</Typography>
      <br></br>
      <PanelBorder>
        <Grid sx={{ padding: 2 }}>Headers are displayed using HeaderStyle component: </Grid>
        <Grid item sx={{ marginLeft: 2 }}>
          <HeaderStyle>Sample UI</HeaderStyle>
        </Grid>
      </PanelBorder>
      <br></br>
      <Typography variant="h2">Typography</Typography>
      <br></br>
      <PanelBorder>
        <Grid container direction="column">
          <Grid item sx={{ padding: 2 }}>
            <Typography variant="h1">Sample Text for Variant h1</Typography>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <Typography variant="h2">Sample Text for Variant h2</Typography>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <Typography variant="h3">Sample Text for Variant h3</Typography>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <Typography variant="h4">Sample Text for Variant h4</Typography>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <Typography variant="bodyTitle">Sample Text for Variant bodyTitle</Typography>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <Typography variant="body1">Sample Text for Variant body1</Typography>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <Link href="https://xd.adobe.com/view/9c97f75e-3fb5-4a57-6b85-3186630c7a69-eaa6/variables/?hints=off">
              https://xd.adobe.com/view/9c97f75e-3fb5-4a57-6b85-3186630c7a69-eaa6/variables/?hints=off
            </Link>
          </Grid>
        </Grid>
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Buttons</Typography>
      <br></br>
      <PanelBorder>
        <Grid sx={{ padding: 2 }}>
          Buttons: use contained buttons for Save or Submit, use outlined buttons if in headings for example: Add and
          for Cancel
        </Grid>
        <Grid container spacing={2}>
          <Grid item sx={{ marginLeft: 2 }}>
            <Button variant="contained" color="primary">
              Primary Button
            </Button>
          </Grid>
          <Grid item>
            <Button variant="outlined" color="primary">
              Secondary Button
            </Button>
          </Grid>
          <Grid item>
            <Button color="primary">Tertiary Button</Button>
          </Grid>
        </Grid>
        <br></br>
        <Grid container spacing={2}>
          <Grid item sx={{ marginLeft: 2 }}>
            <Button variant="contained" color="error">
              Primary Button
            </Button>
          </Grid>
          <Grid item>
            <Button variant="outlined" color="error">
              Secondary Button
            </Button>
          </Grid>
          <Grid item>
            <Button color="error">Tertiary Button</Button>
          </Grid>
        </Grid>
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Layout Component: PanelBorder</Typography>
      <br></br>
      <PanelBorder>
        <Grid container justifyContent="center">
          <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2} paddingTop={2}>
            Use up all the space inside and position components within a grid container with padding: 2, paddingBottom
            is included in Layout component Panel Border.
          </Grid>
        </Grid>
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Layout Component: DoublePanelBorder</Typography>
      <br></br>
      <DoublePanelBorder>
        Example, for size greater than md, the left margin is set to 0 and right margin is set to 2 (16px), hence this
        can be used for the right side component in TwoColumnPage below.
      </DoublePanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Layout Component: TwoColumnPage</Typography>
      <br></br>
      <TwoColumnPage leftColumn={<LeftHandComponent />} rightColumn={<RightHandComponent />} />
      <br></br>
      <br></br>
      <Typography variant="h2">Layout Component: SingleColumnPage, it includes title and subtitle</Typography>
      <br></br>
      <PanelBorder sx={{ padding: 2 }}>
        <SingleColumnPage title={'Title'} subtitle={'Subtitle'} content={renderContent()} />
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Layout Component: ComponentBorder</Typography>
      <br></br>
      <ComponentBorder>
        This component has padding left and right 2 but takes up 95% width with margin: 1
      </ComponentBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Form Group</Typography>
      <br></br>
      <PanelBorder>
        <Grid sx={{ padding: 2 }}>
          Forms include three fields for breakpoint lg in a line, two fields per line for breakpoint md, one field per
          line for breakpoint xs. Unless fields must be grouped together in which case the grouped fields must occupy
          the same space as other fields. Fields must be size small as included in cambian theme.{' '}
        </Grid>
        <Grid container spacing={2} sx={{ padding: 2 }}>
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField label="First Name" name="firstName" />
          </Grid>
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField label="Last Name" name="lastName" />
          </Grid>
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <Box display="flex" alignItems="center" width="100%">
              <TextField
                label="Type"
                select
                sx={{ width: '95px' }}
                variant="outlined"
                name="healthCareIdType"
                size="small"
              >
                {idTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </TextField>{' '}
              &nbsp;
              <Box flexGrow={1}>
                <TextField label="Value" fullWidth variant="outlined" name="healthCareIdValue" size="small" />
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField label="Email" name="emailAddress" />
          </Grid>
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <TextField label="Phone Number" name="primaryPhoneNumber" />
          </Grid>
          <Grid item xs={12} md={6} lg={4} xl={3}>
            <LocalizationProvider required dateAdapter={AdapterDayjs}>
              <DatePicker
                format={'YYYY-MM-DD'}
                label="Date of Birth"
                slot={{
                  textField: TextField,
                }}
                onChange={() => {
                  console.log('Date');
                }}
              />
            </LocalizationProvider>
          </Grid>
        </Grid>
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Colour palettes</Typography>
      <br></br>
      <PanelBorder>
        <Grid sx={{ padding: 2 }}>
          {' '}
          Colour palettes are included in the theme. They are included in this style guide:{' '}
          <Link href="https://xd.adobe.com/view/9c97f75e-3fb5-4a57-6b85-3186630c7a69-eaa6/variables/?hints=off">
            https://xd.adobe.com/view/9c97f75e-3fb5-4a57-6b85-3186630c7a69-eaa6/variables/?hints=off
          </Link>
          (password = H3althy!)
        </Grid>
        <Grid container spacing={2} sx={{ marginLeft: 2, marginTop: 1 }}>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.primary.main,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.cambianBlue,
            })}
          ></Grid>

          <Grid item xs={10} lg={10}>
            Cambian Blue is primary main color
          </Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.primary.hover,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.darkBlue,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Dark Blue is primary hover color
          </Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.background.default,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.white,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            White is a default background color
          </Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.background.primary,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.cambianBackground,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Cambian background is a primary background color
          </Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.background.secondary,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.lightGray,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Light gray is a secondary background color
          </Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.text.primary,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.darkGray2,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Dark gray 2 is a primary text color
          </Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.text.secondary,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.cambianBlue,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Cambian blue is a secondary text color
          </Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.text.subHeading,
            })}
          ></Grid>
          <Grid
            item
            xs={1}
            lg={1}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.skyBlue,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Sky blue is a subheading text color
          </Grid>
          <Grid
            item
            xs={2}
            lg={2}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.coconut,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Coconut is a background color
          </Grid>
          <Grid
            item
            xs={2}
            lg={2}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.mediumGray1,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Medium gray 1 is a border color
          </Grid>
          <Grid
            item
            xs={2}
            lg={2}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.mediumGray2,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Medium gray 2 is a border color
          </Grid>
          <Grid
            item
            xs={2}
            lg={2}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.darkGray1,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Dark gray 1 is a text color
          </Grid>
          <Grid
            item
            xs={2}
            lg={2}
            sx={(theme) => ({
              bgcolor: theme.palette.cambianCommon.black,
            })}
          ></Grid>
          <Grid item xs={10} lg={10}>
            Black is a text color
          </Grid>
        </Grid>
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Icons</Typography>
      <br></br>
      <PanelBorder>
        <Grid sx={{ padding: 2 }}>Set color to primary</Grid>
        <IconButton sx={{ paddingLeft: 2 }}>
          <SearchIcon color="primary" />
        </IconButton>
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Alignment</Typography>
      <br></br>
      <PanelBorder>
        <Grid sx={{ padding: 2 }}>There are ways to align left or right, using grid and boxes</Grid>
        <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2}>
          <Grid container direction="row" justifyContent="space-between" sx={{ paddingBottom: 0, paddingTop: 2 }}>
            <Grid item>
              <Box display="flex" justifyContent="flex-start">
                Left aligned
              </Box>
            </Grid>
            <Grid item>
              <Box display="flex" justifyContent="flex-end">
                Right aligned
              </Box>
            </Grid>
          </Grid>
        </Grid>
        <br></br>
        <br></br>
        <Grid sx={{ padding: 2 }}>There are ways to align left or right, using grids only</Grid>
        <Grid container sx={{ paddingLeft: 2, paddingRight: 2 }}>
          <Grid item xs={10} sm={10} md={11} sx={{ padding: 0 }}>
            Left aligned
          </Grid>
          <Grid item xs={2} sm={2} md={1} sx={{ padding: 0 }}>
            Right aligned
          </Grid>
        </Grid>
      </PanelBorder>
      <br></br>
      <br></br>
      <Typography variant="h2">Loading state</Typography>
      <br></br>
      <PanelBorder>
        <Grid sx={{ padding: 2 }}>Use a circular progress for loading state</Grid>
        <div>
          <Box sx={{ display: 'flex' }} display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
            <CircularProgress />
          </Box>
        </div>
      </PanelBorder>
    </>
  );
}
