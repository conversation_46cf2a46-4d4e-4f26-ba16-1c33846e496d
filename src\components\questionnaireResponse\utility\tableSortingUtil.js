function descendingComparator(a, b, orderBy) {
  const valueA = access(orderBy, a);
  const valueB = access(orderBy, b);
  if (valueB < valueA) {
    return -1;
  }
  if (valueB > valueA) {
    return 1;
  }

  return 0;
}

const access = (path, obj) => {
  const properties = path.split('.');
  for (let i = 0; i < properties.length; i++) {
    if (obj === undefined) return '';
    obj = obj[properties[i]];
  }

  if (obj) {
    return obj;
  }

  return '';
};

function getComparator(order, orderBy) {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

// Since 2020 all major browsers ensure sort stability with Array.prototype.sort().
// stableSort() brings sort stability to non-modern browsers (notably IE11). If you
// only support modern browsers you can replace stableSort(exampleArray, exampleComparator)
// with exampleArray.slice().sort(exampleComparator)
function stableSort(array, comparator) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) {
      return order;
    }
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
}

export { descendingComparator, access, getComparator, stableSort };
