import { Grid, TableCell } from '@mui/material';
import moment from 'moment';

export function formatSelectionOptions(optionType) {
  if (!optionType || !optionType.length) {
    return '';
  }
  // Define an array of words that should remain in all caps
  const allCapsWords = ['MDT', 'PCN', 'EFT', 'EMR', 'AM', 'PM', 'TM'];

  const words = optionType.split(/[_ ]/);

  const formattedWords = words.map((word) => {
    if (allCapsWords.includes(word.toUpperCase())) {
      return word.toUpperCase();
    } else {
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    }
  });

  const formattedType = formattedWords.join(' ');

  return formattedType;
}

// strip time from ISO date string
export function isoToDate(isoString) {
  if (!isoString) return null;
  return isoString.substring(0, 10);
}

export function getLocalIsoDateString(date) {
  return new Date(date.getTime() - date.getTimezoneOffset() * 60000).toISOString();
}

export const camelToSnake = (camelCaseString) =>
  camelCaseString.replace(/[A-Z]/g, (match) => `_${match.toLowerCase()}`);

export const calcAge = (dob) => {
  const age = moment().diff(moment(dob), 'years');

  if (isNaN(age)) {
    return ' - ';
  }

  return age;
};

export const getAddress = (addresses) => {
  const primaryAddress = addresses.find((address) => address.primary);
  if (primaryAddress) {
    const { address1, address2, city, province, country, postalCode } = primaryAddress;
    let formattedAddress = '';
    if (address1) {
      formattedAddress += address1 + ', ';
    }
    if (address2) {
      formattedAddress += address2 + ', ';
    }
    if (city) {
      formattedAddress += city + ', ';
    }
    if (province) {
      formattedAddress += province + ', ';
    }
    if (country) {
      formattedAddress += country + ', ';
    }
    if (postalCode) {
      formattedAddress += postalCode;
    }
    return formattedAddress;
  }
  return '';
};

export const checkForErrors = (formDetails, setFormDetails, fieldNames, errors) => {
  fieldNames.forEach((fieldName) => {
    const fieldNameWithConfigs = `${fieldName}Configs`;
    const fieldConfig = formDetails[fieldNameWithConfigs];
    const isRequired = fieldConfig.isRequired;
    const value = fieldConfig.value;
    if (isRequired) {
      const error = checkIfRequiredAndNotFilled(formDetails, setFormDetails, fieldNameWithConfigs, value);
      if (error) {
        errors[fieldName] = true;
      }
    }
  });
};

export const checkIfRequiredAndNotFilled = (formConfigs, setFormConfigs, configName, value) => {
  if (formConfigs[configName].isRequired && (!value || value.length === 0) && value !== '0' && value !== 0) {
    setFormConfigs((prevConfig) => ({
      ...prevConfig,
      [configName]: {
        ...prevConfig[configName],
        error: true,
        errorText: `This field is required`,
      },
    }));
    return true;
  }
  return false;
};

export const checkShouldBeNumber = (setFormConfigs, configName, value) => {
  if (isNaN(value) || value === Math.E) {
    setFormConfigs((prevConfig) => ({
      ...prevConfig,
      [configName]: {
        ...prevConfig[configName],
        error: true,
        errorText: 'Please enter a valid number.',
      },
    }));
    return true;
  } else {
    setFormConfigs((prevConfig) => ({
      ...prevConfig,
      [configName]: {
        ...prevConfig[configName],
        error: false,
        errorText: '',
      },
    }));
  }
};

export const formatSiteSchedule = (siteDetails) => {
  const {
    monday_opening_time,
    monday_closing_time,
    tuesday_opening_time,
    tuesday_closing_time,
    wednesday_opening_time,
    wednesday_closing_time,
    thursday_opening_time,
    thursday_closing_time,
    friday_opening_time,
    friday_closing_time,
    saturday_opening_time,
    saturday_closing_time,
    sunday_opening_time,
    sunday_closing_time,
  } = siteDetails;
  return {
    ...siteDetails,
    schedule: {
      monday: convertTime(monday_opening_time) + ' - ' + convertTime(monday_closing_time),
      tuesday: convertTime(tuesday_opening_time) + ' - ' + convertTime(tuesday_closing_time),
      wednesday: convertTime(wednesday_opening_time) + ' - ' + convertTime(wednesday_closing_time),
      thursday: convertTime(thursday_opening_time) + ' - ' + convertTime(thursday_closing_time),
      friday: convertTime(friday_opening_time) + ' - ' + convertTime(friday_closing_time),
      saturday: convertTime(saturday_opening_time) + ' - ' + convertTime(saturday_closing_time),
      sunday: convertTime(sunday_opening_time) + ' - ' + convertTime(sunday_closing_time),
    },
  };
};

export function convertTime(time) {
  if (time == null) {
    return '';
  }

  time = time.split(':'); // convert to array

  // fetch
  var hours = Number(time[0]);
  var minutes = Number(time[1]);

  // calculate
  var timeValue;

  if (hours > 0 && hours <= 12) {
    timeValue = '' + hours;
  } else if (hours > 12) {
    timeValue = '' + (hours - 12);
  } else if (hours == 0) {
    timeValue = '12';
  }

  timeValue += minutes < 10 ? ':0' + minutes : ':' + minutes; // get minutes
  timeValue += hours >= 12 ? ' PM' : ' AM'; // get AM/PM

  return timeValue;
}

export const getScheduleCell = (siteOrMdt, index) => {
  let isAMGreen = false;
  let isPMGreen = false;

  if (siteOrMdt.schedule) {
    let day = fullDaysOfWeek[index];
    isAMGreen = siteOrMdt.schedule[day + 'am'];
    isPMGreen = siteOrMdt.schedule[day + 'pm'];
  }

  return (
    <TableCell style={{ padding: '0px' }} key={index}>
      <Grid container justifyContent="center" textAlign="center">
        <Grid
          style={{
            color: isAMGreen ? '#FFF' : 'inherit',
            backgroundColor: isAMGreen ? '#4D76A9' : 'inherit',
            height: '100%',
          }}
          item
          xs={12}
        >
          AM
        </Grid>
        <Grid
          style={{ color: isPMGreen ? '#FFF' : 'inherit', backgroundColor: isPMGreen ? '#4D76A9' : 'inherit' }}
          item
          xs={12}
        >
          PM
        </Grid>
      </Grid>
    </TableCell>
  );
};

export const daysOfWeek = ['M', 'T', 'W', 'Th', 'F', 'Sa', 'Su'];

export const fullDaysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

export const timeOptions = [
  '6:00 AM',
  '6:15 AM',
  '6:30 AM',
  '6:45 AM',
  '7:00 AM',
  '7:15 AM',
  '7:30 AM',
  '7:45 AM',
  '8:00 AM',
  '8:15 AM',
  '8:30 AM',
  '8:45 AM',
  '9:00 AM',
  '9:15 AM',
  '9:30 AM',
  '9:45 AM',
  '10:00 AM',
  '10:15 AM',
  '10:30 AM',
  '10:45 AM',
  '11:00 AM',
  '11:15 AM',
  '11:30 AM',
  '11:45 AM',
  '12:00 PM',
  '12:15 PM',
  '12:30 PM',
  '12:45 PM',
  '1:00 PM',
  '1:15 PM',
  '1:30 PM',
  '1:45 PM',
  '2:00 PM',
  '2:15 PM',
  '2:30 PM',
  '2:45 PM',
  '3:00 PM',
  '3:15 PM',
  '3:30 PM',
  '3:45 PM',
  '4:00 PM',
  '4:15 PM',
  '4:30 PM',
  '4:45 PM',
  '5:00 PM',
  '5:15 PM',
  '5:30 PM',
  '5:45 PM',
  '6:00 PM',
  '6:15 PM',
  '6:30 PM',
  '6:45 PM',
  '7:00 PM',
  '7:15 PM',
  '7:30 PM',
  '7:45 PM',
  '8:00 PM',
  '8:15 PM',
  '8:30 PM',
  '8:45 PM',
  '9:00 PM',
  '9:15 PM',
  '9:30 PM',
  '9:45 PM',
  '10:00 PM',
  '10:15 PM',
  '10:30 PM',
  '10:45 PM',
  '11:00 PM',
  '11:15 PM',
  '11:30 PM',
  '11:45 PM',
  '12:00 AM',
];

export const defaultRequiredConfig = {
  value: null,
  isRequired: true,
  error: false,
  errorText: '',
};
export const defaultOptionalConfig = {
  value: null,
  isRequired: false,
  error: false,
  errorText: '',
};

export const createConfigObject = (keys, defaultConfig) => {
  return keys.reduce((result, key) => {
    result[key] = defaultConfig;
    return result;
  }, {});
};
