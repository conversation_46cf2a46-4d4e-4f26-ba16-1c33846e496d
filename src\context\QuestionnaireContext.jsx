'use client';
import React, { createContext, useContext, useState } from 'react';

const QuestionnaireContext = createContext();

export const useQuestionnaire = () => useContext(QuestionnaireContext);

export const QuestionnaireProvider = ({ children }) => {
  const [selectedQuestionnaire, setSelectedQuestionnaire] = useState(null);
  const [selectedQuestionnaireResponse, setSelectedQuestionnaireResponse] = useState(null);
  const [docGenerated, setDocGenerated] = useState('');
  const [selectedRepository, setSelectedRepository] = useState('');
  const [filteredChannels, setFilteredChannels] = useState([]);
  const [showAddDuplicateResponse, setShowAddDuplicateResponse] = useState(false);

  return (
    <QuestionnaireContext.Provider
      value={{
        selectedQuestionnaire,
        setSelectedQuestionnaire,
        selectedQuestionnaireResponse,
        setSelectedQuestionnaireResponse,
        docGenerated,
        setDocGenerated,
        selectedRepository,
        setSelectedRepository,
        filteredChannels,
        setFilteredChannels,
        showAddDuplicateResponse,
        setShowAddDuplicateResponse,
      }}
    >
      {children}
    </QuestionnaireContext.Provider>
  );
};
