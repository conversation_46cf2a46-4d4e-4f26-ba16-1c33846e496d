'use client';
import { HeaderStyle } from '@cambianrepo/ui';
import { Box, Button, Divider, Grid, Typography } from '@mui/material';
import OpaqueBackground from './OpaqueBackground';
import WarningModal from './WarningModal';

const SessionExpiresSoon = ({ sessionCountDown, sessionExpiresSoon, theme, signOutCallback, getNewSession }) => {
  const backgroundzIndex = theme.zIndex.drawer + 99;
  return (
    <Box sx={{ display: sessionExpiresSoon ? 'default' : 'none' }}>
      <OpaqueBackground zIndex={backgroundzIndex}></OpaqueBackground>
      <WarningModal zIndex={backgroundzIndex + 1}>
        <Grid>
          <Typography variant="subtitle1" fontWeight="bold">
            Session Expiring
          </Typography>
        </Grid>
        <Grid>
          <br />
          <Typography variant="body1">Your session will expire in {sessionCountDown}s</Typography>
          <br />
          <Typography variant="body1">
            Stay signed in to continue, or sign out to exit. Unsaved changes will be lost.
          </Typography>
          <br />
        </Grid>
        <Grid sx={{ marginTop: 1, float: 'right' }}>
          <Button sx={{}} variant="outlined" onClick={() => signOutCallback({ callbackUrl: '/' })}>
            Sign Out
          </Button>
          <Button sx={{ marginLeft: 1 }} variant="contained" onClick={() => getNewSession()}>
            Stay Signed In
          </Button>
        </Grid>
      </WarningModal>
    </Box>
  );
};

export default SessionExpiresSoon;
