import { NextResponse } from 'next/server';
import { handler } from '@/lib/middleware/handler';
import { initiateSignInWithCredentials, setUpMfa } from '@/lib/auth/cognito';
import { csrfProtected } from '@/lib/middleware/csrfProtected';
import { MFA_SETUP, SOFTWARE_TOKEN_MFA, NEW_PASSWORD_REQUIRED } from '@/lib/auth/cognito/constant';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  const { username, password } = await req.json();

  if (!username || !password) {
    return NextResponse.json(
      {
        message: 'username and password must be provided',
      },
      {
        status: 400,
      },
    );
  }
  console.log('username', username);

  const [res, error] = await initiateSignInWithCredentials({
    username,
    password,
  });

  if (error) {
    return error.toNextResponse();
  }

  const { ChallengeName: challengeName, Session: session } = res;

  switch (challengeName) {
    case MFA_SETUP:
      // When the user signs in for the very first time, provide QR code to set up user's authenticator app
      const [mfaResponse, mfaError] = await setUpMfa({ session });
      if (mfaError) {
        return mfaError.toNextResponse();
      }
      const mfaUrl = mfaResponse.SecretCode
        ? `otpauth://totp/${process.env.NEXT_PUBLIC_MFA_APP_NAME}:${username}?secret=${mfaResponse.SecretCode}`
        : undefined;
      return NextResponse.json({ session: mfaResponse.Session, mfaUrl, challengeName: MFA_SETUP }, { status: 200 });

    case SOFTWARE_TOKEN_MFA:
      return NextResponse.json({ session, challengeName: SOFTWARE_TOKEN_MFA }, { status: 200 });

    case NEW_PASSWORD_REQUIRED:
      return NextResponse.json(
        { userId: res.ChallengeParameters.USER_ID_FOR_SRP, session, challengeName: NEW_PASSWORD_REQUIRED },
        { status: 200 },
      );

    default:
      return NextResponse.json(
        {
          message: 'MFA is not enabled in this userpool',
        },
        {
          status: 500,
        },
      );
  }
};

export const POST = handler(csrfProtected(['POST']), postEndpoint);
