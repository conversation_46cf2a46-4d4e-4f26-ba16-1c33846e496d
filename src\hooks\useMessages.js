import { transformApiMessagesToUI, buildApiQueryParams } from '@cambianrepo/messaging';
import { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';

// Import Coordinator-specific API functions
import { getMessages, updateMessageReadStatus } from '@/lib/clientApi/organizationMessaging';

/**
 * Custom hook for Coordinator's messages that integrates with the app's API
 * @param {string} type - The type of messages to fetch ('inbox' or 'sent')
 * @param {Object} options - Filter options
 * @returns {Object} Messages data and functions
 */
export const useMessages = (type, options = {}) => {
  const [selectedMessageId, setSelectedMessageId] = useState(null);
  const queryClient = useQueryClient();

  // Ensure we have valid date objects and sort model
  const normalizedOptions = useMemo(() => {
    const {
      startDate = dayjs().subtract(7, 'day'),
      endDate = dayjs(),
      sortModel = [{ field: type === 'sent' ? 'sent' : 'received', sort: 'desc' }],
      searchText = '',
    } = options;

    // Ensure dates are valid dayjs objects
    const validStartDate = dayjs(startDate).isValid() ? startDate : dayjs().subtract(7, 'day');
    const validEndDate = dayjs(endDate).isValid() ? endDate : dayjs();

    return {
      startDate: validStartDate,
      endDate: validEndDate,
      sortModel,
      searchText,
    };
  }, [options, type]);

  const { startDate, endDate, sortModel, searchText } = normalizedOptions;

  // Map the DataGrid field names to the API's sortBy values
  const fieldToSortByMap = {
    received: 'date',
    from: 'name',
    sent: 'date',
    to: 'name',
  };

  // Get the current sort field and direction
  const currentSortField = sortModel[0]?.field || (type === 'sent' ? 'sent' : 'received');
  const currentSortDirection = sortModel[0]?.sort?.toUpperCase() || 'DESC';

  // Map the DataGrid field to the API's sortBy parameter
  const sortBy = fieldToSortByMap[currentSortField] || 'date';

  // Fetch messages
  const messagesQuery = useQuery({
    queryKey: [
      'organizationMessages',
      type,
      startDate?.format?.('YYYY-MM-DD'),
      endDate?.format?.('YYYY-MM-DD'),
      // Use JSON.stringify to avoid object reference issues
      JSON.stringify(sortModel),
      searchText,
    ],
    queryFn: () => {
      // Use shared utility to build API params
      return getMessages({
        type,
        startDate: startDate?.format('YYYY-MM-DD'),
        endDate: endDate?.format('YYYY-MM-DD'),
        sortOrder: currentSortDirection,
        sortBy: sortBy,
        searchText: searchText,
      });
    },
    select: (data) => {
      // Transform API data to UI format using shared utility
      return {
        ...data,
        messages: transformApiMessagesToUI(data.messages || [], type),
      };
    },
    enabled: !!startDate && !!endDate,
    staleTime: 30000, // Consider data fresh for 30 seconds
    refetchOnWindowFocus: false,
  });

  // Mark as read mutation - only relevant for inbox messages
  const markAsReadMutation = useMutation({
    mutationFn: ({ messageId, isRead }) => {
      return updateMessageReadStatus(messageId, isRead);
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries(['organizationMessages', type]);
    },
  });

  // Handle message click with read status update for inbox messages
  const handleMessageClick = (message) => {
    // If message is null, clear the selection
    if (!message) {
      setSelectedMessageId(null);
      return;
    }

    setSelectedMessageId(message.id);

    // Only update read status for inbox messages that are unread
    if (type === 'inbox' && message.status === 'unread') {
      markAsReadMutation.mutate({
        messageId: message.id,
        isRead: true,
      });

      // Optimistically update the local data while the mutation is in progress
      queryClient.setQueryData(
        [
          'organizationMessages',
          type,
          startDate?.format?.('YYYY-MM-DD'),
          endDate?.format?.('YYYY-MM-DD'),
          JSON.stringify(sortModel),
          searchText,
        ],
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            messages: oldData.messages.map((msg) =>
              msg.id === message.id
                ? { ...msg, status: 'read', originalMessage: { ...msg.originalMessage, IsRead: true } }
                : msg,
            ),
          };
        },
      );
    }
  };

  return {
    // Data
    messages: messagesQuery.data?.messages || [],
    isLoading: messagesQuery.isLoading,
    error: messagesQuery.error?.message || null,
    selectedMessageId,

    // Actions
    handleMessageClick,

    // Mutation status
    isMarkingAsRead: markAsReadMutation.isLoading,
  };
};
