'use client';

import { ArticlesList } from '@cambianrepo/articles-editor';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import useNotification from '@/lib/hooks/useNotification';
import { transformArticleData, uploadThumbnailToS3 } from './articleUtility';
import { deleteArtifact, getArtifact, getArtifactListByVisibility, putArtifact } from '@/lib/api/artifactRepository';
import { CREATE, PUBLIC, PRIVATE, ARTICLES, BOTH } from '@/lib/constant';
import { downloadFileInJsonFormat } from '@/lib/utility';

export default function ClientPage() {
  const queryClient = useQueryClient();
  const openSnackbar = useNotification();
  const { data: session } = useSession();
  const user = session?.user;
  const router = useRouter();

  const {
    data: articleList,
    isError,
    isLoading,
  } = useQuery({
    queryKey: [ARTICLES, PRIVATE],
    queryFn: () =>
      getArtifactListByVisibility({
        artifactType: ARTICLES,
        visibility: PRIVATE,
      }),
  });

  useEffect(() => {
    if (isError) {
      openSnackbar({
        variant: 'error',
        msg: 'Retrieving articles list failed.',
      });
    }
  }, [isError]);

  const getArticleWithReactQuery = async (articleId) => {
    const response = await queryClient.fetchQuery({
      queryKey: [ARTICLES, articleId],
      queryFn: () =>
        getArtifact({
          visibility: PRIVATE,
          artifactId: articleId,
          artifactType: ARTICLES,
          includeMetadata: true,
        }),
    });

    return await transformArticleData(response);
  };

  const handleDelete = async (articleId) => {
    const selectedArticle = articleList.find((article) => article.artifactId === articleId);
    try {
      openSnackbar({
        msg: `Deleting ${selectedArticle.title}`,
      });

      const { publishStatus } = selectedArticle;
      const deletePromises = [];

      deletePromises.push(
        deleteArtifact({
          artifactId: articleId,
          artifactType: ARTICLES,
          visibility: PRIVATE,
        }),
      );

      if (publishStatus === PUBLIC || publishStatus === BOTH) {
        deletePromises.push(
          deleteArtifact({
            artifactId: articleId,
            artifactType: ARTICLES,
            visibility: PUBLIC,
          }),
        );
      }

      const results = await Promise.all(deletePromises);

      const allSuccess = results.every((res) => res.ok);

      if (allSuccess) {
        queryClient.setQueryData([ARTICLES, PRIVATE], (oldData = []) =>
          oldData.filter((article) => article.artifactId !== articleId),
        );
        console.log(`Successfully deleted article, ${selectedArticle.title}`);
      } else {
        throw new Error('One or more deletions failed');
      }
    } catch (err) {
      console.error(err);
      openSnackbar({
        variant: 'error',
        msg: `Failed to delete article, ${selectedArticle.title}`,
      });
    }
  };

  const handleDuplicate = async (articleId) => {
    if (articleId === CREATE) {
      openSnackbar({
        variant: 'error',
        msg: 'Save article before duplicating.',
      });
      return { success: false };
    }

    try {
      const originalArticle = await getArticleWithReactQuery(articleId);
      if (!originalArticle) throw new Error('Original article not found');

      openSnackbar({
        msg: `Duplicating ${originalArticle.name} article`,
      });

      let thumbnailData = null;
      if (originalArticle.thumbnail?.data) {
        thumbnailData = {
          contentType: originalArticle.thumbnail.contentType || 'image/png',
          fileName: `copy_${originalArticle.thumbnail.fileName || 'thumbnail.png'}`,
          data: new Blob([originalArticle.thumbnail.data], { type: originalArticle.thumbnail.contentType }),
        };
      }

      const res = await putArtifact({
        artifactType: ARTICLES,
        visibility: PRIVATE,
        requestBody: {
          title: `${originalArticle.title} COPY`,
          description: originalArticle.description,
          name: `${originalArticle.name} COPY`,
          body: originalArticle.body,
          thumbnail: thumbnailData
            ? {
                contentType: thumbnailData.contentType,
                fileName: thumbnailData.fileName,
              }
            : null,
          publishStatus: 'no',
        },
      });

      if (!res.ok) {
        throw new Error('Failed to duplicate article');
      }

      const response = await res.json();
      const responseBody = response.responseBody;

      if (thumbnailData && responseBody?.thumbnailPresignedUrl) {
        try {
          await uploadThumbnailToS3(responseBody.thumbnailPresignedUrl, thumbnailData);
        } catch (error) {
          console.error('Failed to upload thumbnail for duplicate:', error);
          openSnackbar({
            variant: 'warning',
            msg: 'Article duplicated but thumbnail upload failed',
          });
        }
      }

      queryClient.invalidateQueries({ queryKey: [ARTICLES, PRIVATE] });
      console.log(`Duplicated "${originalArticle.name}" successfully`);

      return { success: true };
    } catch (err) {
      console.error('Duplicate failed:', err);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to duplicate the article',
      });
      return { success: false };
    }
  };

  const handleImport = async (article) => {
    try {
      openSnackbar({
        msg: 'Importing article',
      });

      let thumbnailData = null;
      if (article.thumbnail?.data) {
        // Check if data is base64
        if (typeof article.thumbnail.data === 'string' && article.thumbnail.data.startsWith('data:')) {
          // Convert base64 to Blob
          const base64Response = await fetch(article.thumbnail.data);
          const blob = await base64Response.blob();
          thumbnailData = {
            data: blob,
            contentType: article.thumbnail.contentType || blob.type || 'image/png',
            fileName: article.thumbnail.fileName || 'thumbnail.png',
          };
        } else {
          // Assume it's already a Blob or compatible format
          thumbnailData = {
            data: article.thumbnail.data,
            contentType: article.thumbnail.contentType || 'image/png',
            fileName: article.thumbnail.fileName || 'thumbnail.png',
          };
        }
      }

      const res = await putArtifact({
        artifactType: ARTICLES,
        visibility: PRIVATE,
        requestBody: {
          publishStatus: 'no',
          title: article.title,
          description: article.description,
          name: article.name || article.title,
          body: article.body,
          thumbnail: thumbnailData
            ? {
                contentType: thumbnailData.contentType,
                fileName: thumbnailData.fileName,
              }
            : null,
        },
      });

      if (!res.ok) {
        throw new Error('Failed to create imported article');
      }

      const response = await res.json();
      const responseBody = response.responseBody;

      if (thumbnailData && responseBody?.thumbnailPresignedUrl) {
        try {
          await uploadThumbnailToS3(responseBody.thumbnailPresignedUrl, thumbnailData);
        } catch (error) {
          console.error('Failed to upload thumbnail for imported article:', error);
          openSnackbar({
            variant: 'warning',
            msg: 'Article imported but thumbnail upload failed',
          });
        }
      }

      queryClient.invalidateQueries([ARTICLES, PRIVATE]);
      console.log(`Imported "${article.title}" successfully`);

      return { success: true };
    } catch (err) {
      console.error('Import failed:', err);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to import the article',
      });
      return { success: false };
    }
  };

  const handleExport = async (articleId) => {
    try {
      const article = await getArticleWithReactQuery(articleId);
      if (!article) {
        throw Error('Something went wrong');
      }

      openSnackbar({
        msg: `Exporting ${article.title}`,
      });

      // Convert thumbnail blob to base64 if it exists
      let thumbnailBase64 = null;
      if (article.thumbnail?.data) {
        thumbnailBase64 = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.readAsDataURL(article.thumbnail.data);
        });
      }

      // Create export object with thumbnail data
      const exportData = {
        ...article,
        thumbnail: article.thumbnail
          ? {
              ...article.thumbnail,
              data: thumbnailBase64,
            }
          : null,
      };

      downloadFileInJsonFormat(JSON.stringify(exportData), article.title);
      return { success: true };
    } catch (err) {
      console.error('Export failed:', err);
      openSnackbar({
        variant: 'error',
        msg: 'Error in exporting article.',
      });
      return { success: false };
    }
  };

  const handleEdit = (articleId) => {
    const articleMetadata = articleList.find((article) => article.artifactId === articleId);
    const { publishStatus } = articleMetadata;
    let visibility = PRIVATE;

    if (publishStatus === 'public' || publishStatus === 'both') {
      visibility = PUBLIC;
    }

    router.push(`/article-editor/${articleId}?visibility=${visibility}`);
  };

  const handleNavigation = () => {
    router.push(`/article-editor/${CREATE}`);
  };

  const handlePublish = async (articleId, newPublishStatus) => {
    const selectedArticleMetadata = articleList.find((article) => article.artifactId === articleId);

    if (!selectedArticleMetadata) {
      openSnackbar({
        variant: 'error',
        msg: 'Article not found',
      });
      return;
    }

    try {
      openSnackbar({
        msg: `Publishing ${selectedArticleMetadata.title} to ${newPublishStatus}`,
      });
      const article = await getArticleWithReactQuery(articleId);
      const { publishStatus: currentPublishStatus } = selectedArticleMetadata;

      if (currentPublishStatus === newPublishStatus) {
        openSnackbar({
          variant: 'warning',
          msg: 'There is nothing to publish. Aborting',
        });
        return;
      }

      const thumbnailData = article.thumbnail?.data
        ? {
            data: article.thumbnail.data,
            contentType: article.thumbnail.contentType || 'image/png',
            fileName: article.thumbnail.fileName || 'thumbnail.png',
          }
        : null;

      const articleData = {
        ...article,
        publishStatus: newPublishStatus,
        thumbnail: thumbnailData
          ? {
              contentType: thumbnailData.contentType,
              fileName: thumbnailData.fileName,
            }
          : null,
      };

      const privateUpdateResponse = await putArtifact({
        artifactType: ARTICLES,
        visibility: PRIVATE,
        requestBody: articleData,
        artifactId: articleId,
      });

      if (!privateUpdateResponse.ok) {
        throw new Error('Failed to update private article');
      }

      const privateResponse = await privateUpdateResponse.json();
      const privateUpdate = privateResponse.responseBody;

      const thumbnailUploads = [];
      if (thumbnailData && privateUpdate?.thumbnailPresignedUrl) {
        thumbnailUploads.push(uploadThumbnailToS3(privateUpdate.thumbnailPresignedUrl, thumbnailData));
      }

      if (newPublishStatus === BOTH || newPublishStatus === PUBLIC) {
        const publicUpdateResponse = await putArtifact({
          artifactType: ARTICLES,
          visibility: PUBLIC,
          requestBody: articleData,
          artifactId: articleId,
        });

        if (!publicUpdateResponse.ok) {
          throw new Error('Failed to update public article');
        }

        const publicResponse = await publicUpdateResponse.json();
        const publicUpdate = publicResponse.responseBody;

        if (thumbnailData && publicUpdate?.thumbnailPresignedUrl) {
          thumbnailUploads.push(uploadThumbnailToS3(publicUpdate.thumbnailPresignedUrl, thumbnailData));
        }
      } else if (currentPublishStatus === BOTH || currentPublishStatus === PUBLIC) {
        await deleteArtifact({
          artifactType: ARTICLES,
          visibility: PUBLIC,
          artifactId: articleId,
        });
      }

      // Wait for all thumbnail uploads to complete
      await Promise.all(thumbnailUploads);

      // Invalidate queries to refresh data
      await Promise.all([
        queryClient.invalidateQueries([ARTICLES, PRIVATE]),
        queryClient.invalidateQueries([ARTICLES, PUBLIC]),
        queryClient.invalidateQueries([ARTICLES, articleId]),
      ]);
      console.log(`Successfully published article, ${selectedArticleMetadata.title}`);
    } catch (err) {
      console.error('Publish error:', err);
      openSnackbar({
        variant: 'error',
        msg: `Failed to publish article, ${selectedArticleMetadata.title}`,
      });
    }
  };

  if (isLoading) {
    return <>LOADING...</>;
  }

  return (
    <>
      <ArticlesList
        handleNavigation={handleNavigation}
        articleList={articleList ?? []}
        onImportCallback={handleImport}
        handleEditArticle={handleEdit}
        handleDuplicateArticle={handleDuplicate}
        handleDeleteArticle={handleDelete}
        handleExportArticle={handleExport}
        handlePublishArticle={handlePublish}
        handleViewArticle={handleEdit}
      />
    </>
  );
}
