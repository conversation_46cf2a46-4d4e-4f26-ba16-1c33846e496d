import {
  Button,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
} from '@mui/material';
import { FormContainer } from 'react-hook-form-mui';
import React, { useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';

function FeaturesPanel({ orgFeaturesList, allFeaturesList, handleFormSaveCallback }) {
  const generateFormFeaturesList = (feature) => {
    feature.enable = orgFeaturesList?.includes(feature.name);
    return feature;
  };

  const formContext = useForm(
    {
      defaultValues: {
        features: allFeaturesList?.map(generateFormFeaturesList) || [],
      },
    },
    [orgFeaturesList, allFeaturesList],
  );

  useEffect(() => {
    const features = allFeaturesList?.map(generateFormFeaturesList);
    formContext.setValue('features', features);
  }, [orgFeaturesList]);

  const { formState } = formContext;
  const { isDirty } = formState;

  const handleSubmit = React.useCallback(
    async (data) => {
      if (await handleFormSaveCallback(data.features)) {
        formContext.reset({ features: data.features });
      }
    },
    [formState],
  );

  return (
    <FormContainer onSuccess={handleSubmit} formContext={formContext}>
      <Paper sx={{ maxWidth: '500px', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: '150vh' }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell>Feature</TableCell>
                <TableCell>Enable</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {formContext.getValues('features')?.map((feature, index) => (
                <TableRow key={index}>
                  <TableCell>{feature.name}</TableCell>
                  <TableCell align="center" sx={{ width: '10%' }}>
                    <Controller
                      name={`features[${index}].enable`}
                      control={formContext.control}
                      render={({ field }) => (
                        <Checkbox size="small" sx={{ padding: 0 }} {...field} checked={field.value} />
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Box width="auto">
        <Button sx={{ marginTop: '20px' }} variant="contained" type="submit" disabled={!isDirty}>
          Save
        </Button>
      </Box>
    </FormContainer>
  );
}

export default FeaturesPanel;
