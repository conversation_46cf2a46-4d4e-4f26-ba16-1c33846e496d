'use client';

import React, { useEffect, useState } from 'react';
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  IconButton,
  ClickAwayListener,
  Tooltip,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { makeStyles, useTheme } from '@mui/styles';
import { usePathname, useRouter } from 'next/navigation';
import { Branding } from './Branding';
import { useSession } from 'next-auth/react';
import { customSignOut } from '@/lib/auth/customSignout';
import ErrorBoundary from '@/components/ErrorBoundary';
import { AccountSettings } from '@cambianrepo/ui';
import { usePermissions } from '@/context/UserPermissions';
import { toolbarConfig } from '@/lib/constant';
import { toolbarIcons } from '@/lib/clientConstant';
import { useUserIcon } from '@/context/UserDetailsContext';
import { useOrgData } from '@/context/OrgDataContext';

const useStyles = makeStyles((theme) => ({
  appbarMenu: {
    margin: 'auto',
    display: 'flex',
    alignItems: 'center',
  },
  listItemText: {
    color: `${theme.palette.primary.main}`,
  },
  activeSubMenu: {
    backgroundColor: `${theme.palette.action.selected}`,
  },
  activeMenu: {
    borderBottom: `2px solid ${theme.palette.primary.main}`,
  },
}));

function DesktopAccountMenu() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { data } = useSession();
  const { user } = data || {};
  const { firstName, lastName } = usePermissions();
  const router = useRouter();
  const [anchorAccountEl, setAnchorAccountEl] = React.useState(null);
  const { userData, iconUrl, isLoading } = useUserIcon();

  const handleAccountMenuList = (event) => {
    setAnchorAccountEl(event.currentTarget);
  };

  const handleCloseAccountMenu = () => {
    console.log('handleCloseAccountMenu');
  };

  const handleAccountMenuSelected = (pageURL) => {
    setAnchorAccountEl(null);
    router.push(pageURL, { replace: true });
  };

  const handleSignOut = async () => {
    sessionStorage.removeItem('selectedTab');
    sessionStorage.removeItem('selectedSubMenu');
    customSignOut({ callbackUrl: '/' });
  };

  return (
    <div
      style={{
        margin: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <ClickAwayListener onClickAway={() => setAnchorAccountEl(null)}>
        <IconButton onClick={handleAccountMenuList}>
          <div
            style={{ fontSize: '16px', display: 'block', marginBottom: '8px', color: `${theme.palette.primary.main}` }}
          >
            <ErrorBoundary>
              <AccountSettings
                style={{ width: '0vw' }}
                firstName={userData.firstName ? userData.firstName : firstName}
                lastName={userData.lastName ? userData.lastName : lastName}
                photoImageUrl={!isLoading ? iconUrl : null}
                email={userData.email ? userData.email : user?.email}
              />
            </ErrorBoundary>
          </div>
        </IconButton>
      </ClickAwayListener>

      <Menu
        id="menu-account"
        anchorEl={anchorAccountEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        keepMounted
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        open={Boolean(anchorAccountEl)}
        onClose={() => handleCloseAccountMenu}
      >
        <MenuItem onClick={() => handleAccountMenuSelected(t('/user-profile'))}>{t('userProfile')}</MenuItem>
        <MenuItem onClick={() => handleAccountMenuSelected(t('/change-password'))}>{t('changePassword')}</MenuItem>
        <MenuItem onClick={() => handleSignOut()}>{t('signOut')}</MenuItem>
      </Menu>
    </div>
  );
}

function PrivateToolbar(props) {
  const router = useRouter();
  const pathname = usePathname();
  const { t } = useTranslation();
  const classes = useStyles();
  const theme = useTheme();
  const { permissions, authorized } = usePermissions();
  const [settingsMenu, setSettingsMenu] = useState(false);
  const [editorsMenu, setEditorsMenu] = useState(false);
  const { selectedTab, setSelectedTab } = props;
  const { selectedSubMenu, setSelectedSubMenu } = props;
  const { organizationName } = useOrgData();

  const handleMenuSelectionNavigation = async (selectedPath, selectedSubMenu, selectedTab) => {
    if (authorized) {
      router.push(selectedPath);
      setSelectedTab(selectedTab);
      setSelectedSubMenu(selectedSubMenu);
    }
    setSettingsMenu(false);
    setEditorsMenu(false);
  };

  const isPermissionEnabled = (requiredPermissions) => {
    if (!requiredPermissions) return true;
    const permissionsArray = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
    return permissionsArray.some((permission) => permissions.includes(permission));
  };

  const isSelected = (item) => pathname === item.path || item.children?.some((child) => pathname.includes(child.path));

  useEffect(() => {
    const updateSelectedStatesBasedOnPathname = () => {
      toolbarConfig.forEach((item) => {
        if (pathname === item.path) {
          setSelectedTab(item.id);
        }
        item.children?.forEach((child) => {
          if (pathname.includes(child.path)) {
            setSelectedTab(item.id);
            setSelectedSubMenu(child.id);
          }
        });
      });
    };
    updateSelectedStatesBasedOnPathname();
  }, [pathname]);

  return (
    <AppBar position="fixed" color="default" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1, height: '75px' }}>
      <Toolbar style={{ alignItems: 'center' }}>
        <Branding />
        <div style={{ flexGrow: 1 }}></div>
        <div className={classes.appbarMenu}>
          <Typography
            variant="body1"
            sx={(theme) => ({
              color: theme.palette.cambianCommon.darkGray1,
              p: 1,
              display: { xs: 'none', md: 'block' },
            })}
          >
            {organizationName || ''}
          </Typography>
          {toolbarConfig.map((item) => {
            if (!isPermissionEnabled(item.permissions)) return null;
            const Icon = toolbarIcons[item.icon];
            const isHomeIcon = item.id === 'home';

            return (
              <React.Fragment key={item.id}>
                <Tooltip key={item.id} title={t(item.title)} placement="bottom-start">
                  <IconButton
                    onClick={
                      isHomeIcon
                        ? () => handleMenuSelectionNavigation(item.path, item.id, item.id)
                        : (event) =>
                            item.id === 'editors'
                              ? setEditorsMenu(event.currentTarget)
                              : setSettingsMenu(event.currentTarget)
                    }
                    className={isSelected(item) ? classes.activeMenu : ''}
                    sx={{ color: theme.palette.primary.main, borderRadius: 0, borderBottom: `2px solid transparent` }}
                  >
                    {Icon}
                  </IconButton>
                </Tooltip>
                {item.id === 'editors' && (
                  <Menu
                    id={item.id}
                    anchorEl={editorsMenu}
                    keepMounted
                    open={Boolean(editorsMenu)}
                    onClose={() => setEditorsMenu(false)}
                  >
                    {item.children.map((child) => {
                      if (!isPermissionEnabled(child.permission)) return null;
                      return (
                        <MenuItem
                          key={child.id}
                          onClick={() => handleMenuSelectionNavigation(child.path, child.id, item.id)}
                          className={selectedSubMenu === child.id ? classes.activeSubMenu : ''}
                        >
                          <ListItemIcon className={classes.listItemText}>{toolbarIcons[child.icon]}</ListItemIcon>
                          <ListItemText primary={t(child.title)} className={classes.listItemText} />
                        </MenuItem>
                      );
                    })}
                  </Menu>
                )}
                {item.children && item.id === 'settings' && (
                  <Menu
                    id={item.id}
                    anchorEl={settingsMenu}
                    keepMounted
                    open={Boolean(settingsMenu)}
                    onClose={() => setSettingsMenu(false)}
                  >
                    {item.children.map((child) => {
                      if (!isPermissionEnabled(child.permission)) return null;
                      return (
                        <MenuItem
                          key={child.id}
                          onClick={() => handleMenuSelectionNavigation(child.path, child.id, item.id)}
                          className={selectedSubMenu === child.id ? classes.activeSubMenu : ''}
                        >
                          <ListItemIcon className={classes.listItemText}>{toolbarIcons[child.icon]}</ListItemIcon>
                          <ListItemText primary={t(child.title)} className={classes.listItemText} />
                        </MenuItem>
                      );
                    })}
                  </Menu>
                )}
              </React.Fragment>
            );
          })}
        </div>
        <DesktopAccountMenu />
      </Toolbar>
    </AppBar>
  );
}

export default PrivateToolbar;
