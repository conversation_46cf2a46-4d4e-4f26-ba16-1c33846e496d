name: Weekly code promotion
on:
  workflow_dispatch:
  schedule:
    # Every Friday at 12:00am UTC (5:00am PDT, 4:00am PST)
    - cron: "0 12 * * FRI"
permissions:
  id-token: write
  contents: write
jobs:

  merge-test-to-sandbox-prod-and-main:
    name: 'Merge test to sandbox, prod, and main'
    runs-on: ubuntu-latest
    steps:
      - name: Clone the GitHub repository
        uses: actions/checkout@v4
        with:
          ref: 'test'
      - name: Merge test to sandbox
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: test
          target_branch: sandbox
          message: Weekly merge of test to sandbox
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Merge test to prod
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: test
          target_branch: prod
          message: Weekly merge of test to prod
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Merge test to main
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: test
          target_branch: main
          message: Weekly merge of test to main
          github_token: ${{ secrets.GITHUB_TOKEN }}

  merge-dev-to-test:
    needs: merge-test-to-sandbox-prod-and-main
    name: 'Merge dev to test'
    runs-on: ubuntu-latest
    steps:
      - name: Clone the GitHub repository
        uses: actions/checkout@v4
        with:
          ref: 'dev'
      - name: Merge dev to test
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: dev
          target_branch: test
          message: Weekly merge of dev to test
          github_token: ${{ secrets.GITHUB_TOKEN }}
