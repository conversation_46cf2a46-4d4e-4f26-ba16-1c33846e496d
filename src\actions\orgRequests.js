'use server';

import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';
import ApiError from '@/lib/error/ApiError';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import {
  ORGANIZATION_ID,
  ORG_REQUESTS_API_BASE_URL,
  TYPE_QUESTIONNAIRE_RESPONSE,
  REQUEST_SOURCE_COORDINATOR,
} from '@/lib/constant';

/**
 * Creates a questionnaire request for an individual
 *
 * @param {string} individualId - The individual's Cambian ID
 * @param {string} artifactId - The questionnaire ID
 * @param {string} dueDate - The due date for the questionnaire (YYYY-MM-DD)
 * @param {string} expiryDate - The expiry date for the questionnaire (YYYY-MM-DD)
 * @param {string} clientId - The client ID
 * @param {number} reminderDays - The reminder in days
 * @returns {Promise<{requestId: string}>} - The created request ID
 */
export const createQuestionnaireRequest = async (
  individualId,
  organizationId,
  artifactId,
  dueDate,
  expiryDate,
  clientId,
  reminderDays,
) => {
  try {
    // Call the organization requests service directly
    const apiUrl = `${ORG_REQUESTS_API_BASE_URL}/organizations/${ORGANIZATION_ID}/requests`;
    console.log('Calling organization requests service at:', apiUrl);

    const response = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(apiUrl, {
      method: 'POST',
      body: JSON.stringify({
        requestType: TYPE_QUESTIONNAIRE_RESPONSE,
        cambianId: individualId,
        organizationId: organizationId,
        requestDetails: {
          questionnaireId: artifactId,
        },
        dueDate: dueDate,
        expiryDate: expiryDate,
        clientId: clientId,
        requestSource: REQUEST_SOURCE_COORDINATOR,
        reminder: reminderDays,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new ApiError({
        status: response.status,
        message: `Failed to create questionnaire request: ${errorText}`,
      });
    }

    const data = await response.json();
    console.log('Request created successfully:', data);
    return data;
  } catch (err) {
    console.error('Error creating questionnaire request:', err);
    throw err;
  }
};
