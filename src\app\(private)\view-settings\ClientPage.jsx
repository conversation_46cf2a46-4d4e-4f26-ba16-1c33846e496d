'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getOrgViews } from '@/lib/api/orgData';
import { ViewEditor } from './Editors';
import useNotification from '@/lib/hooks/useNotification';
import { ORGANIZATION_ID, ORGANIZATION_VIEWS } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

// TODO: Org Data is the source of truth and all data should be retrieved from there.
function ClientPage() {
  const openSnackbar = useNotification();
  const queryClient = useQueryClient();

  const orgViewsQuery = useQuery({ queryKey: [ORGANIZATION_VIEWS], queryFn: getOrgViews });

  const orgData = {
    ...orgViewsQuery.data,
  };

  const handleViewSaveCallback = async ({ allValues, dirtyValues }) => {
    console.log('allValues from form to API:', allValues);
    console.log('dirtyValues from form to API:', dirtyValues);
    try {
      // Create a promise array for parallel execution
      const promises = [];
      const messages = [];

      await updateOrgViews(allValues, promises, messages);

      const results = await Promise.all(promises);
      handleResults(results, messages);
      queryClient.setQueryData([ORGANIZATION_VIEWS], allValues);
    } catch (error) {
      console.log(error);
      openSnackbar({
        variant: 'error',
        msg: 'Something went wrong while saving views data. Please contact technical support.',
      });
    }
  };

  const updateOrgViews = async (allValues, promises, messages) => {
    promises.push(updateOrgDataViews(allValues));
    messages.push('Save Views Data to Org Data');
  };

  const updateOrgDataViews = (allValues) => {
    return fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/view-settings`, {
      method: 'PUT',
      body: JSON.stringify({ ...allValues }),
    });
  };

  const handleResults = (results, messages) => {
    let hasError = false;
    results.forEach((result, index) => {
      if (!result.ok) {
        hasError = true;
        openSnackbar({ variant: 'error', msg: `${messages[index]} failed` });
      }
    });

    if (!hasError) {
      console.log('Saving view data succeeded');
    }
  };

  return <ViewEditor orgMetaData={orgData} handleViewFormSaveCallback={handleViewSaveCallback} />;
}

export default ClientPage;
