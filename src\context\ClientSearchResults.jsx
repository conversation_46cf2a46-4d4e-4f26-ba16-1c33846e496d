'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';

const initialSearchParams = {
  firstName: '',
  lastName: '',
  email: '',
  primaryPhoneNumber: '',
  gender: '',
  dateOfBirth: null,
  healthCareIdType: '',
  healthCareIdIssuer: '',
  healthCareIdValue: '',
};

const ClientSearchResults = createContext();

export const useClientSearchResults = () => useContext(ClientSearchResults);

export const ClientSearchResultsProvider = ({ children }) => {
  const [viewMode, setViewMode] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState('');
  const [clientsData, setClientsData] = useState(null);
  const [clientSearchParams, setSearchParams] = useState(initialSearchParams);
  const [query, setQuery] = useState('');
  const [expand, setExpand] = React.useState(false);

  return (
    <ClientSearchResults.Provider
      value={{
        viewMode,
        setViewMode,
        selectedClientId,
        setSelectedClientId,
        clientsData,
        setClientsData,
        clientSearchParams,
        setSearchParams,
        query,
        setQuery,
        expand,
        setExpand,
      }}
    >
      {children}
    </ClientSearchResults.Provider>
  );
};
