'use client';

import React, { useEffect, useRef } from 'react';
import { makeStyles, useTheme } from '@mui/styles';
import {
  Drawer,
  Toolbar,
  ListItemText,
  List,
  ListItem,
  Tab,
  Tabs,
  <PERSON>lapse,
  Grid,
  IconButton,
  Tooltip,
} from '@mui/material';
import { useRouter, usePathname } from 'next/navigation';
import { usePermissions } from '@/context/UserPermissions';
import PushPinIcon from '@mui/icons-material/PushPin';
import { useClientSearchResults } from '@/context/ClientSearchResults';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { menuConfig } from '@/lib/constant';
import { sidebarIcons } from '@/lib/clientConstant';

const useStyles = makeStyles((theme) => ({
  icon: {
    borderRight: `0.5px solid ${theme.palette.divider}`,
  },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    color: `${theme.palette.primary.main}`,
    zIndex: 1,
    marginTop: -20,
  },
  listItemText: {
    color: `${theme.palette.primary.main}`,
  },
  activeSubMenu: {
    backgroundColor: `${theme.palette.action.selected}`,
  },
}));

export default function PrivateSidebar(props) {
  const classes = useStyles();
  const router = useRouter();
  const theme = useTheme();
  const pathname = usePathname();
  const { selectedTab, setSelectedTab } = props;
  const { selectedSubMenu, setSelectedSubMenu } = props;
  const { fetchPermissionsData, permissions, authorized } = usePermissions();
  const { viewMode, setViewMode, selectedClientId, setSelectedClientId } = useClientSearchResults();
  const { expandedSubmenu, setExpandedSubmenu } = props;
  const { showArrow, setShowArrow } = props;
  const sideMenuRef = useRef(null);
  const arrowRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!sideMenuRef.current || showArrow) return; // Skip if pinned

      const isClickInside =
        sideMenuRef.current.contains(event.target) || (arrowRef.current && arrowRef.current.contains(event.target));

      if (!isClickInside) {
        toggleSubmenu(expandedSubmenu); // Close only if the click is outside
      }
    };

    if (showArrow) return; // Skip updates if pinned
    document.addEventListener('click', handleClickOutside);

    const updateMenuState = () => {
      menuConfig.forEach((menuItem) => {
        if (pathname.includes(menuItem.path)) {
          setSelectedTab(menuItem.id);
          setSelectedSubMenu(menuItem.id);
        } else if (menuItem.children) {
          menuItem.children.forEach((subItem) => {
            if (pathname.includes(subItem.path)) {
              setSelectedTab(menuItem.id);
              setSelectedSubMenu(subItem.id);
            }
          });
        }
      });
    };

    updateMenuState();

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [router.pathname, showArrow]);

  const handleMenuSelectionNavigation = async (selectedPath, selectedSubMenuItem, parentId) => {
    if (showArrow && selectedSubMenu === selectedSubMenuItem) {
      return; // Prevent closing if pinned and the same submenu item is clicked
    }
    if (authorized) {
      router.push(selectedPath);
      setSelectedTab(parentId);
      setSelectedSubMenu(selectedSubMenuItem);
      if (selectedPath === '/clients') {
        setSelectedClientId('');
        setViewMode(false);
      }
      if (!showArrow) {
        setExpandedSubmenu(false);
      }
    }
  };

  const toggleSubmenu = (menuId) => {
    if (showArrow && expandedSubmenu === menuId) {
      return; // Prevent closing if pinned and the same submenu is already expanded
    }

    setExpandedSubmenu(expandedSubmenu === menuId ? false : menuId);
  };

  const clickOnPushPin = () => {
    setShowArrow(true);
    if (selectedTab) {
      setExpandedSubmenu(selectedTab); // Point the menu to the selected tab
    }
  };

  const clickOnArrow = () => {
    if (selectedSubMenu) {
      const parentMenuId = menuConfig.find((menuItem) =>
        menuItem.children?.some((child) => child.id === selectedSubMenu),
      )?.id;
      setSelectedTab(parentMenuId);
    }
    setShowArrow(false);
    setExpandedSubmenu(false);
  };

  const isPermissionEnabled = (requiredPermissions) => {
    if (!requiredPermissions) return true;
    const permissionsArray = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
    return permissionsArray.some((permission) => permissions.includes(permission));
  };

  const hasPermissionForMenuItem = (item) =>
    item.alwaysShow || (item.permissions && item.permissions.some(isPermissionEnabled));

  const handleTabChange = (id) => {
    if (showArrow && selectedTab === id) {
      return; // Prevent closing if pinned and the same tab is clicked
    }
    setSelectedTab(id);
  };

  const renderMenuItems = () => {
    return menuConfig.filter(hasPermissionForMenuItem).map((menuItem) => (
      <Collapse
        key={menuItem.id}
        in={expandedSubmenu === menuItem.id} // Keep expanded if pinned
        timeout="auto"
        unmountOnExit
        sx={{ width: { lg: 220, md: 220, sm: 220, xs: 180 } }}
      >
        <div ref={arrowRef}>
          <IconButton
            className={classes.closeButton}
            onClick={clickOnArrow}
            sx={{ display: showArrow ? 'block' : 'none', paddingTop: 3 }}
            aria-label="Arrow"
          >
            <ArrowBackIosIcon fontSize="small" />
          </IconButton>
          <IconButton
            className={classes.closeButton}
            onClick={clickOnPushPin}
            sx={{ display: !showArrow ? 'block' : 'none', paddingTop: 3 }}
            aria-label="PushPin"
          >
            <PushPinIcon fontSize="small" />
          </IconButton>
        </div>
        <List component="div" disablePadding>
          {menuItem.children.map(
            (subMenuItem) =>
              isPermissionEnabled(subMenuItem.permission) && (
                <ListItem
                  key={subMenuItem.id}
                  button
                  onClick={() => {
                    handleMenuSelectionNavigation(subMenuItem.path, subMenuItem.id, menuItem.id);
                  }}
                  className={selectedSubMenu === subMenuItem.id ? classes.activeSubMenu : ''}
                >
                  <ListItemText primary={subMenuItem.title} className={classes.listItemText} />
                </ListItem>
              ),
          )}
        </List>
      </Collapse>
    ));
  };

  return (
    <Drawer
      ref={sideMenuRef}
      anchor="left"
      PaperProps={expandedSubmenu ? { sx: { width: { lg: 310, md: 310, sm: 310, xs: 270 } } } : { sx: { width: 60 } }}
      open={true}
      variant="persistent"
      sx={{ zIndex: (theme) => theme.zIndex.appBar + 1 }}
    >
      <Toolbar />
      <Grid container direction="row" sx={{ paddingTop: { xs: '30px', sm: '22px' } }}>
        <Grid item>
          <Tabs
            orientation="vertical"
            value={menuConfig.some((item) => item.id === selectedTab) ? selectedTab : false}
            onChange={(event, newValue) => handleTabChange(newValue)}
            TabIndicatorProps={{
              sx: {
                left: 0,
              },
            }}
          >
            {menuConfig.filter(hasPermissionForMenuItem).map((item) => (
              <Tab
                icon={
                  <Tooltip key={item.id} title={item.title} placement="right">
                    <span>{sidebarIcons[item.icon]}</span>
                  </Tooltip>
                }
                className={expandedSubmenu ? classes.icon : ''}
                value={item.id}
                sx={{ minWidth: 'auto' }}
                key={item.id}
                onClick={() => {
                  item.path ? handleMenuSelectionNavigation(item.path, false, item.id) : toggleSubmenu(item.id);
                }}
              />
            ))}
          </Tabs>
        </Grid>
        <Grid item>
          <List>{renderMenuItems()}</List>
        </Grid>
      </Grid>
    </Drawer>
  );
}
