'use client';
import { <PERSON>er<PERSON>tyle, PanelBorder } from '@cambianrepo/ui';
import { EditUserProfile } from '@cambianrepo/user-profile';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import useNotification from '@/lib/hooks/useNotification';
import { useUserIcon } from '@/context/UserDetailsContext';
import { ORGANIZATION_ID, ORGANIZATION_USER_ID } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

export default function UserProfilePage() {
  const { data: session } = useSession();
  const openSnackbar = useNotification();
  const { updateUserData, iconUrl, setIconUrl, refetchIcon } = useUserIcon();
  //Fetch user details
  const fetchUserDetails = async (userId) => {
    try {
      const responseData = await fetchNextRoute(
        'organizationData',
        `/organizations/${ORGANIZATION_ID}/users/${userId}`,
      );

      const userDetails = await responseData.json();
      return userDetails;
    } catch (error) {
      console.error(
        'Error fetching data from /organizations/${ORGANIZATION_ID}/users/${userId} API:',
        error.message || error,
      );
    }
  };

  const {
    data: userDetailsData,
    error: userDetailsError,
    refetch: refetchUserDetails,
    isLoading,
  } = useQuery({
    queryKey: ['userDetails', session?.user.orgUserId],
    queryFn: () => fetchUserDetails(session?.user.orgUserId),
    enabled: !!session?.user.orgUserId,
  });

  // Update user details
  const updateUserDetailsHandler = async (updatedData, session, refetchUserDetails) => {
    try {
      const requestBody = {
        status: 'ACTIVE',
        emailAddress: updatedData.emailAddress,
        firstName: updatedData.firstName,
        lastName: updatedData.lastName,
        roleIds: updatedData.roles?.map((role) => role.id) || [],
      };
      if (updatedData.phoneNumber) {
        requestBody.phoneNumber = updatedData.phoneNumber;
      }
      const responseData = await fetchNextRoute(
        'organizationData',
        `/organizations/${ORGANIZATION_ID}/users/${session?.user?.orgUserId}`,
        {
          method: 'PUT',
          body: JSON.stringify({ ...requestBody }),
        },
      );

      if (!responseData.ok) {
        console.error('Request failed with status:', responseData.status);

        openSnackbar({
          variant: 'error',
          msg: 'Failed to save user profile data',
        });
        return false;
      }

      const userDetailsResult = await responseData.json();
      refetchUserDetails();
      updateUserData({
        firstName: updatedData.firstName,
        lastName: updatedData.lastName,
        email: updatedData.emailAddress,
      });
      console.log('User profile data saved successfully.');
      return userDetailsResult;
    } catch (error) {
      console.error('Error updating user details:', error);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to save user profile data.',
      });
      return false;
    }
  };

  const updateIconHandler = async (file, session) => {
    if (!file) {
      try {
        const deleteResponse = await fetchNextRoute(
          'organizationData',
          `/organizations/${ORGANIZATION_ID}/users/${ORGANIZATION_USER_ID}/icon`,
          {
            method: 'DELETE',
          },
        );

        setIconUrl(null);
        await refetchIcon();
        return deleteResponse;
      } catch (error) {
        console.error('Error deleting the icon:', error);
        throw error;
      }
    }

    try {
      const iconUrlResponse = await fetchNextRoute(
        'organizationData',
        `/organizations/${ORGANIZATION_ID}/users/${ORGANIZATION_USER_ID}/icon`,
        {
          method: 'PUT',
          body: JSON.stringify({ filename: file.name }),
        },
      );

      const { iconUrl } = await iconUrlResponse.json();

      const uploadResponse = await fetch(iconUrl, {
        method: 'PUT',
        headers: { 'Content-Type': file.type },
        body: file,
      });

      if (!uploadResponse.ok) {
        throw new Error('Error uploading the file to S3');
      }

      setIconUrl(iconUrl);
      await refetchIcon();
      return uploadResponse;
    } catch (error) {
      console.error('Error updating the icon:', error);
      throw error;
    }
  };

  if (isLoading) {
    return <></>;
  }

  if (userDetailsError) {
    return <div>Error loading user details</div>;
  }

  return (
    <>
      <HeaderStyle>User Profile</HeaderStyle>
      <PanelBorder style={{ padding: '0px' }}>
        <EditUserProfile
          visibleTabs={['name', 'contact']}
          nameTabConfig={['firstName', 'lastName']}
          singleContactInfo={true}
          userDetails={userDetailsData}
          updateUserDetails={(updatedData) => updateUserDetailsHandler(updatedData, session, refetchUserDetails)}
          updateIcon={(file) => updateIconHandler(file, session)}
          icon={iconUrl}
        />
      </PanelBorder>
    </>
  );
}
