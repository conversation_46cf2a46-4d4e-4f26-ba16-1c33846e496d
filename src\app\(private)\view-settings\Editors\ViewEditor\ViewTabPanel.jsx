import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typo<PERSON>, Icon<PERSON>utton, Modal } from '@mui/material';
import { Form<PERSON>ontainer, TextFieldElement } from 'react-hook-form-mui';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { dirtyValues } from '../../../../../lib/utility';
import * as Constants from '@/app/globalConstants';
import { CambianTooltip } from '@cambianrepo/ui';
import { Code, Help } from '@mui/icons-material';
import { ShowHelperTextFile } from './ShowHelperTextFile';

function ViewTabPanel({ orgViews, handleFormSaveCallback }) {
  const formContext = useForm({});
  const { formState } = formContext;
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState('edit'); // 'edit' or 'preview'
  const [text, setText] = useState('');
  const [openHelperTextFile, setOpenHelperTextFile] = useState(false);

  const onClose = () => {
    setOpenHelperTextFile(false);
  };

  const ModalStyle = {
    margin: 'auto',
    marginTop: '20px',
    maxWidth: '80%',
    borderRadius: '10px',
    maxHeight: '80vh',
    overflowY: 'scroll',
  };

  // Hardcoded example data for preview mode
  const example = {
    firstName: 'John',
    middleName: 'A.',
    lastName: 'Doe',
    dateOfBirth: '01/01/1990',
    gender: 'Male',
    email: '<EMAIL>',
    phone: '************',
    preferredContactMethod: 'Email',
    notifications: 'Enabled',
    addressLine: '123 Main Street',
    city: 'Toronto',
    province: 'ON',
    country: 'Canada',
    postalCode: 'K1A 0B1',
    identification: 'PHN BC 123456789',
  };

  useEffect(() => {
    if (orgViews) {
      formContext.reset({
        clientProfileSummary: orgViews.clientProfileSummary,
      });
      setText(orgViews.clientProfileSummary);
    }
  }, [orgViews]);

  const toggleMode = () => {
    setMode((prevMode) => (prevMode === 'edit' ? 'preview' : 'edit'));
  };

  const handleChange = (e) => {
    setText(e.target.value);
  };

  const renderText = () => {
    if (mode === 'edit') {
      return <TextFieldElement name="clientProfileSummary" value={text} onChange={handleChange} required />;
    }

    // Replace placeholders with example data in preview mode
    const previewText = text
      .replaceAll('{Client.firstName}', example.firstName)
      .replaceAll('{Client.middleName}', example.middleName)
      .replaceAll('{Client.lastName}', example.lastName)
      .replaceAll('{Client.dateOfBirth}', example.dateOfBirth)
      .replaceAll('{Client.gender}', example.gender)
      .replaceAll('{Client.email}', example.email)
      .replaceAll('{Client.phone}', example.phone)
      .replaceAll('{Client.preferredContactMethod}', example.preferredContactMethod)
      .replaceAll('{Client.notifications}', example.notifications)
      .replaceAll('{Client.address Line}', example.addressLine)
      .replaceAll('{Client.city}', example.city)
      .replaceAll('{Client.province}', example.province)
      .replaceAll('{Client.country}', example.country)
      .replaceAll('{Client.postalCode}', example.postalCode)
      .replaceAll('{Client.identification}', example.identification);

    return (
      <Typography
        name="clientProfileSummary"
        dangerouslySetInnerHTML={{ __html: previewText }}
        sx={{
          padding: '8px 14px',
          border: '1px solid rgba(0, 0, 0, 0.23)',
          borderRadius: '4px',
          color: 'rgba(0, 0, 0, 0.87)',
          backgroundColor: 'rgba(0, 0, 0, 0.12)',
          lineHeight: '1.4375em',
          fontSize: '1rem',
          maxWidth: '500px', // Set maxWidth to match TextField
          display: 'inline-block',
          width: '100%',
        }}
      ></Typography>
    );
  };

  const handleSubmit = async (orgViews) => {
    setLoading(true);
    try {
      const dirtyFields = { ...formState.dirtyFields };
      const dirtyData = dirtyValues(dirtyFields, orgViews);
      await handleFormSaveCallback({
        allValues: orgViews,
        dirtyValues: dirtyData,
      });
    } catch (error) {
      console.error('Error saving data:', error);
    } finally {
      setLoading(false);
    }
  };

  const isFormChanged = () => Object.keys(formState.dirtyFields).length > 0;

  return (
    <FormContainer onSuccess={handleSubmit} formContext={formContext}>
      <Stack spacing={Constants.formFieldSpacing}>
        <Box sx={{ maxWidth: '500px' }} display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h4">
            Client Profile Summary
            <CambianTooltip title="Get more information">
              <IconButton color="primary" onClick={() => setOpenHelperTextFile(true)}>
                <Help
                  fontSize="large"
                  sx={{
                    marginTop: -2,
                    fontSize: '20px',
                    fontWeight: 'normal',
                    color: '#0000008A',
                  }}
                />
              </IconButton>
            </CambianTooltip>
          </Typography>

          <CambianTooltip title={mode === 'edit' ? 'Switch to Preview' : 'Switch to Editor'}>
            <IconButton color="primary" onClick={toggleMode}>
              <Code
                fontSize="large"
                sx={{
                  background: mode === 'edit' ? '#4d76a933' : 'disabled',
                  border: '1px solid #4D76A9',
                  borderRadius: '4px',
                  height: '2rem',
                  width: '3rem',
                }}
              />
            </IconButton>
          </CambianTooltip>
        </Box>
        {renderText()}
        <Box width="auto" paddingTop={2} marginTop={2}>
          <Button variant="contained" type="submit" disabled={loading || !isFormChanged()}>
            Save
          </Button>
        </Box>
      </Stack>
      <Modal open={openHelperTextFile} onClose={() => setOpenHelperTextFile(false)}>
        <Box sx={ModalStyle}>
          <ShowHelperTextFile onClose={onClose} />
        </Box>
      </Modal>
    </FormContainer>
  );
}

export default ViewTabPanel;
