import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import ClientPage from './ClientPage';
import { getQueryClient } from '@/lib/reactQueryClient';
import { CREATE, ORGANIZATION_METADATA, ALL_ID_TYPES_DATA } from '@/lib/constant';
import { isValidV4UUID } from '@/lib/utility';
import {
  QUESTIONNAIRE_WIDGET_QUERY_KEY,
  REGISTRATION_WIDGET_QUERY_KEY,
  BOOKING_WIDGET_QUERY_KEY,
} from '@/lib/widget-editor/utils/constants';
import {
  server_fetchBookingWidgetsList,
  server_fetchQuestionnaireWidgetsList,
  server_fetchRegistrationWidgetsList,
  server_fetchQuestionnaireWidgetById,
  server_fetchBookingWidgetById,
} from '@/actions/widgetConfig';
import { server_getAllIdTypes, server_loadOrganization } from '@/actions/orgData';

export default async function Page({ params: { widgetId } }) {
  const queryClient = getQueryClient();

  const promises = [];
  if (widgetId !== CREATE && isValidV4UUID(widgetId)) {
    promises.push(
      queryClient.prefetchQuery({
        queryKey: [BOOKING_WIDGET_QUERY_KEY, widgetId],
        queryFn: () => server_fetchBookingWidgetById(widgetId),
      }),
    );
  }

  promises.push(
    queryClient.prefetchQuery({
      queryKey: [ORGANIZATION_METADATA],
      queryFn: () => server_loadOrganization(),
    }),
  );

  promises.push(
    queryClient.prefetchQuery({
      queryKey: [ALL_ID_TYPES_DATA],
      queryFn: () => server_getAllIdTypes(),
    }),
  );

  promises.push(
    queryClient.prefetchQuery({
      queryKey: [QUESTIONNAIRE_WIDGET_QUERY_KEY],
      queryFn: server_fetchQuestionnaireWidgetsList,
    }),
  );

  promises.push(
    queryClient.prefetchQuery({
      queryKey: [REGISTRATION_WIDGET_QUERY_KEY],
      queryFn: server_fetchRegistrationWidgetsList,
    }),
  );

  promises.push(
    queryClient.prefetchQuery({
      queryKey: [BOOKING_WIDGET_QUERY_KEY],
      queryFn: server_fetchBookingWidgetsList,
    }),
  );

  await Promise.all(promises);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ClientPage />
    </HydrationBoundary>
  );
}
