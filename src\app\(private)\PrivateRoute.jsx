'use client';
import { redirect } from 'next/navigation';
import SessionExpiresSoon from '@/components/SessionExpiry/SessionExpiresSoon';
import { customSignOut } from '@/lib/auth/customSignout';
import { useSession, getSession } from 'next-auth/react';
import { SESSION_WARNING_TIME_IN_SECONDS } from '@/lib/constant';
import { useEffect, useState } from 'react';
import { useTheme } from '@mui/material';

export default function PrivateRoute({ children }) {
  let { data: session, status } = useSession();
  const [currentSession, setCurrentSession] = useState(session);
  const [sessionCountDown, setSessionCountDown] = useState(SESSION_WARNING_TIME_IN_SECONDS);
  const [sessionExpiresSoon, setSessionExpiresSoon] = useState(false);
  const theme = useTheme();

  useEffect(() => {
    setCurrentSession(session);
  }, [session]);

  useEffect(() => {
    if (!currentSession.expires) {
      return;
    }
    if (status === 'unauthenticated') {
      customSignOut({ callbackUrl: '/' });
    }
    if (currentSession.refreshTokenError === 'Failed to refresh token') {
      // sign the user out when refresh token expires
      console.log('Refresh token expired!');
      setSessionExpiresSoon(false);
      customSignOut({ callbackUrl: '/' });
    }

    const sessionExpiry = new Date(currentSession.expires).getTime();
    let countDownId;

    const warningTimeoutId = setTimeout(
      () => {
        console.log('Session is expiring in:', sessionExpiry - new Date().getTime());
        setSessionExpiresSoon(true);
        countDownId = setInterval(() => {
          setSessionCountDown(Math.ceil((sessionExpiry - new Date().getTime()) / 1000));
        }, 1000);
      },
      sessionExpiry - new Date().getTime() - SESSION_WARNING_TIME_IN_SECONDS * 1000,
    );

    const expiryTimeoutId = setTimeout(() => {
      console.log('Session expired!');
      setSessionExpiresSoon(false);
      customSignOut({ callbackUrl: '/' });
    }, sessionExpiry - new Date().getTime()); // out of time

    return () => {
      console.log('Clearing timeouts');
      clearTimeout(warningTimeoutId);
      clearTimeout(expiryTimeoutId);
      if (countDownId) {
        clearInterval(countDownId);
        setSessionCountDown(SESSION_WARNING_TIME_IN_SECONDS);
      }
    };
  }, [currentSession, status]);

  const getNewSession = async () => {
    console.log('Attempt to renew session');
    const newSession = await getSession();
    setCurrentSession(newSession);
    if (new Date(newSession.expires).getTime() - new Date().getTime() > 0) {
      console.log(
        'Session renewed for:',
        (new Date(newSession.expires).getTime() - new Date().getTime()) / 1000,
        'seconds',
      );
      setSessionExpiresSoon(false);
    } else {
      console.log('Failed to renew session');
      customSignOut({ callbackUrl: '/' });
    }
  };

  if (!session || !session.user) {
    redirect('/');
  }

  return (
    <>
      <SessionExpiresSoon
        sessionCountDown={sessionCountDown}
        sessionExpiresSoon={sessionExpiresSoon}
        signOutCallback={customSignOut}
        getNewSession={getNewSession}
        theme={theme}
      ></SessionExpiresSoon>
      {children}
    </>
  );
}
