'use server';

import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';
import ApiError from '@/lib/error/ApiError';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import { extractGUID } from '@/lib/utility';
import {
  BOOKING_CAPS,
  BOOKING_WIDGET_ID,
  QUESTIONNAIRE_CAPS,
  QUESTIONNAIRE_WIDGET_ID,
  REGISTRATION_CAPS,
  REGISTRATION_WIDGET_ID,
  WIDGET_ID,
  WIDGET_TYPE,
} from '@/lib/widget-editor/utils/constants';
import {
  BOOKING_WIDGET_ENDPOINT,
  CREATE_BOOKING_WIDGET,
  IMPORT_BOOKING_WIDGET,
  CREATE_QUESTIONNAIRE_WIDGET,
  CREATE_REGISTRATION_WIDGET,
  DELETE_WIDGET,
  GET_ALL_BOOKING_WIDGET,
  GET_ALL_QUESTIONNAIRE_WIDGET,
  GET_ALL_REGISTRATION_WIDGET,
  GET_REGISTRATION_WIDGET,
  IMPORT_QUESTIONNAIRE_WIDGET,
  QUESTIONNAIRE_WIDGET_ENDPOINT,
  WIDGET_LAMBDA_BASE_URL,
} from '@/lib/widget-editor/utils/constants/awsApiEndpoints';
const headers = {
  'Content-Type': 'application/json',
};

export const server_deleteWidget = async (widgetId, widgetType) => {
  let widgetTypeUrl;
  switch (widgetType) {
    case BOOKING_CAPS:
      widgetTypeUrl = 'bookingWidget';
      break;
    case QUESTIONNAIRE_CAPS:
      widgetTypeUrl = 'questionnaireWidget';
      break;
    case REGISTRATION_CAPS:
      widgetTypeUrl = 'registrationWidget';
      break;
  }
  const url = WIDGET_LAMBDA_BASE_URL + DELETE_WIDGET.replace(WIDGET_TYPE, widgetTypeUrl).replace(WIDGET_ID, widgetId);

  const config = {
    method: 'DELETE',
    headers: headers,
  };
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(url, config);
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data.message });
    }
    return data;
  } catch (error) {
    console.log('widget delete error');
    throw error;
  }
};

export const server_fetchQuestionnaireWidgetById = async (widgetId, language) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(
      WIDGET_LAMBDA_BASE_URL +
        QUESTIONNAIRE_WIDGET_ENDPOINT.replace(QUESTIONNAIRE_WIDGET_ID, widgetId) +
        (language ? `?lang=${language}` : ''),
    );
    const data = await res.json();

    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data;
  } catch (error) {
    console.log('failed to fetch questionnaire widget by id', widgetId);
    throw error;
  }
};

export const server_fetchBookingWidgetById = async (widgetId, language) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(
      WIDGET_LAMBDA_BASE_URL +
        BOOKING_WIDGET_ENDPOINT.replace(BOOKING_WIDGET_ID, widgetId) +
        (language ? `?lang=${language}` : ''),
    );
    const data = await res.json();

    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data;
  } catch (error) {
    console.log('failed to fetch booking widget by id', widgetId);
    throw error;
  }
};

export const server_fetchRegistrationWidgetById = async (widgetId, language) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(
      WIDGET_LAMBDA_BASE_URL +
        GET_REGISTRATION_WIDGET.replace(REGISTRATION_WIDGET_ID, widgetId) +
        (language ? `?lang=${language}` : ''),
    );
    const data = await res.json();

    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data;
  } catch (error) {
    console.log('failed to fetch registration widget by id', widgetId);
    throw error;
  }
};

export const server_fetchQuestionnaireWidgetsList = async () => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + GET_ALL_QUESTIONNAIRE_WIDGET);
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data.questionnaireWidgets;
  } catch (error) {
    console.log('failed to fetch questionnaire widget list');
    throw error;
  }
};

export const server_fetchBookingWidgetsList = async () => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + GET_ALL_BOOKING_WIDGET);
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data.bookingWidgets;
  } catch (error) {
    console.log('failed to fetch booking widget list');
    throw error;
  }
};

export const server_fetchRegistrationWidgetsList = async () => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + GET_ALL_REGISTRATION_WIDGET);
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data.registrationWidgets;
  } catch (error) {
    console.log('failed to fetch registration widget list');
    throw error;
  }
};

export const server_updateQuestionnaireWidget = async (widgetData) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(
      WIDGET_LAMBDA_BASE_URL +
        QUESTIONNAIRE_WIDGET_ENDPOINT.replace(QUESTIONNAIRE_WIDGET_ID, extractGUID(widgetData?.SK)),
      {
        method: 'PUT',
        headers: headers,
        body: JSON.stringify(widgetData),
      },
    );
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data.message });
    }
    return data;
  } catch (error) {
    console.log('failed to update q widget');
    throw error;
  }
};

export const server_updateBookingWidget = async (widgetData) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + BOOKING_WIDGET_ENDPOINT.replace(BOOKING_WIDGET_ID, extractGUID(widgetData?.SK)), {
      method: 'PUT',
      headers: headers,
      body: JSON.stringify(widgetData),
    });
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data.message });
    }
    return data;
  } catch (error) {
    console.log('failed to update B widget');
    throw error;
  }
};

export const server_updateRegistrationWidget = async (widgetData) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + GET_REGISTRATION_WIDGET.replace(REGISTRATION_WIDGET_ID, extractGUID(widgetData?.SK)), {
      method: 'PUT',
      headers: headers,
      body: JSON.stringify(widgetData),
    });
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data.message });
    }
    return data;
  } catch (error) {
    console.log('failed to update R widget');
    throw error;
  }
};

export const server_createQuestionnaireWidget = async (widgetData, isImport) => {
  try {
    if (isImport && widgetData.action?.metaData?.actionConditions?.length) {
      const processedActionConditions = widgetData.action.metaData.actionConditions.map((condition) => {
        return {
          ...condition,
          scoreDefinitionName: '',
        };
      });

      widgetData = {
        ...widgetData,
        action: {
          ...widgetData.action,
          actionConditions: processedActionConditions,
          metaData: {
            ...widgetData.action.metaData,
            actionConditions: processedActionConditions,
          },
        },
      };
    }
    const URL = isImport ? IMPORT_QUESTIONNAIRE_WIDGET : CREATE_QUESTIONNAIRE_WIDGET;
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(widgetData),
    });
    const data = await res.json();
    console.log({ data });
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data.errors[0] });
    }
    return data;
  } catch (error) {
    console.log('failed to create q widget');
    throw error;
  }
};

export const server_importBookingWidget = async (widgetData) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + IMPORT_BOOKING_WIDGET, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(widgetData),
    });
    const data = await res.json();
    if (!res.ok) {
      console.log('IMPORT??', { res });
      console.log('IMPORT??', { data });
      throw new ApiError({ status: res.status, message: data.message });
    }
    return data;
  } catch (error) {
    console.log('failed to create b widget', { error });
    throw error;
  }
};

export const server_createBookingWidget = async (widgetData) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + CREATE_BOOKING_WIDGET, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(widgetData),
    });
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data;
  } catch (error) {
    console.log('failed to create b widget');
    throw error;
  }
};

export const server_createRegistrationWidget = async (widgetData) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(WIDGET_LAMBDA_BASE_URL + CREATE_REGISTRATION_WIDGET, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(widgetData),
    });
    const data = await res.json();
    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    return data;
  } catch (error) {
    console.log('failed to create r widget');
    throw error;
  }
};
