const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const envPath = path.resolve(__dirname, './environments/.env');
const commitId = execSync('git rev-parse HEAD').toString().trim();

let envContent = '';

if (fs.existsSync(envPath)) {
  envContent = fs.readFileSync(envPath, 'utf-8');

  // Remove old NEXT_PUBLIC_BUILD_SCRIPT_COMMIT_ID line if it exists
  envContent = envContent.replace(/^NEXT_PUBLIC_BUILD_SCRIPT_COMMIT_ID=.*/m, '');
  envContent = envContent.trim(); // Clean up trailing newline
  envContent += `\nNEXT_PUBLIC_BUILD_SCRIPT_COMMIT_ID=${commitId}\n`;
} else {
  envContent = `NEXT_PUBLIC_BUILD_SCRIPT_COMMIT_ID=${commitId}\n`;
}

fs.writeFileSync(envPath, envContent, 'utf-8');
console.log(`✅ NEXT_PUBLIC_BUILD_SCRIPT_COMMIT_ID set to ${commitId} in ${envPath}`);
