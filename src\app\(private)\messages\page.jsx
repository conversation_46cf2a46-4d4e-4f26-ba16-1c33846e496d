'use client';

import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import React, { useState, useRef } from 'react';
import { Grid, TextField, Button, Box, Stack, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';
import { server_orgMessaging } from '@/actions/orgMessaging';
import useNotification from '@/lib/hooks/useNotification';
import * as Constants from '@/app/globalConstants';

const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

export default function Page() {
  const [messageBody, setMessageBody] = useState('');
  const modules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ['bold', 'italic', 'underline', 'strike', 'blockquote'],
      [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
      ['link', 'image'],
      ['clean'],
    ],
  };
  const quillRef = useRef(null);
  const [recipient, setRecipient] = useState('');
  const openSnackbar = useNotification();
  const [deliveryMechanism, setDeliveryMechanism] = useState('EMAIL');

  const handleDeliveryMechanismChange = (event) => {
    const selectedDeliveryMechanism = event.target.value;
    setDeliveryMechanism(selectedDeliveryMechanism);
  };

  const handleQuillChange = (value) => {
    setMessageBody(value);
  };

  const onSubmit = async () => {
    if (!recipient) {
      openSnackbar({ variant: 'error', msg: 'Recipient is required' });
      return;
    }

    try {
      // Use the `messageBody` state instead of getting content from Quill directly
      await server_orgMessaging(deliveryMechanism, recipient, 'Coordinator', messageBody, messageBody); // Use messageBody as plain text and HTML content
      console.log('Message sent successfully');
      setMessageBody(''); // Clear the editor after submission
    } catch (error) {
      console.error('Failed to send message:', error);
      openSnackbar({ variant: 'error', msg: 'Failed to send message' });
    }
  };

  return (
    <div>
      <HeaderStyle>Messages</HeaderStyle>
      <PanelBorder>
        <Grid container direction="column">
          <Grid item sx={{ padding: 2 }}>
            <FormControl sx={{ width: '100%' }} size="small">
              <InputLabel required>Select Delivery Mechanism</InputLabel>
              <Select label="Select Delivery Mechanism" onChange={handleDeliveryMechanismChange} required>
                <MenuItem key="EMAIL" value="EMAIL">
                  EMAIL
                </MenuItem>
                <MenuItem key="SMS" value="SMS">
                  SMS
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <TextField label="Recipient" name="recipient" onChange={(event) => setRecipient(event.target.value)} />
          </Grid>
        </Grid>
      </PanelBorder>
      <div
        style={{
          backgroundColor: 'white',
          color: '#999',
        }}
      >
        <div>
          <ReactQuill theme="snow" value={messageBody} onChange={handleQuillChange} modules={modules} />
        </div>
      </div>
      <Button
        style={{
          marginTop: '5px',
          float: 'right',
        }}
        variant="contained"
        autoFocus
        onClick={onSubmit}
      >
        Send
      </Button>
    </div>
  );
}
