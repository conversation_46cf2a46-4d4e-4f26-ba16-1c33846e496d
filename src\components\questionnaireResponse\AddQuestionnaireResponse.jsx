// for all subjectTypes (patient, practitioner, location)
'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect } from 'react';
import { FormControl, InputLabel, Select, MenuItem, Stack, Box, Button } from '@mui/material';
import { QuestionnaireV2 as QuestionnaireComponentV2 } from '@cambianrepo/questionnaire';
import { getArtifactListByVisibility, getArtifact } from '@/lib/api/artifactRepository';
import { QUESTIONNAIRES, PRIVATE, BOTH, ORGANIZATION_ID, ORGANIZATION_USER_ID } from '@/lib/constant';
import * as Constants from '@/app/globalConstants';
import Loader from '@/components/Loader';
import { v4 as uuidv4 } from 'uuid';
import useNotification from '@/lib/hooks/useNotification';
import { useQuestionnaire } from '@/context/QuestionnaireContext';
import QuestionnaireReport from '@/components/questionnaireResponse/QuestionnaireReport';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

export default function AddQuestionnaireResponse(props) {
  const {
    clientId,
    targetSubjectType,
    cdrIdentifier,
    onCancel,
    initialQuestionnaireId = '',
    initialResponse = null,
    onComplete,
  } = props;
  const queryClient = useQueryClient();
  const [selectedQuestionnaireId, setSelectedQuestionnaireId] = useState('');
  const [loading, setLoading] = useState(false);
  const openSnackbar = useNotification();
  const [fhirQuestionnaireResponse, setFhirQuestionnaireResponse] = useState({});
  const questionnaireResponseIdPlaceholder = uuidv4();
  const [isQuestionnaireFinished, setIsQuestionnaireFinished] = useState(false);
  const [showQuestionnaireReport, setShowQuestionnaireReport] = useState(false);
  const [showQuestionnaire, setShowQuestionnaire] = useState(false);
  const [questionnaireSaving, setQuestionnaireSaving] = useState(false);
  const { docGenerated, setDocGenerated } = useQuestionnaire();

  const { data: questionnaireList } = useQuery({
    queryKey: [QUESTIONNAIRES, PRIVATE],
    queryFn: () =>
      getArtifactListByVisibility({
        artifactType: QUESTIONNAIRES,
        visibility: PRIVATE,
        queryStringParams: { publishStatuses: [PRIVATE, BOTH] },
      }),
  });

  useEffect(() => {
    if (initialQuestionnaireId) {
      setSelectedQuestionnaireId(initialQuestionnaireId);
      setShowQuestionnaire(true);
    }
  }, [initialQuestionnaireId]);

  const targetQuestionnaires = questionnaireList?.filter(
    (questionnaire) => questionnaire.subjectType === targetSubjectType,
  );

  const fetchPractitionerId = async () => {
    try {
      const responseData = await fetchNextRoute(
        'organizationData',
        `/organizations/${ORGANIZATION_ID}/users/${ORGANIZATION_USER_ID}`,
      );
      const data = await responseData.json();
      const practitionerRole = data.identifiers.find((role) => role.type === 'CDR_PRACTITIONER_ID');
      const practitionerIdValue = practitionerRole ? practitionerRole.value : null;
      return practitionerIdValue;
    } catch (error) {
      console.error('Error fetching Practitioner ID:', error.message || error);
    }
  };

  const { data: fetchedQuestionnaire } = useQuery({
    queryKey: ['questionnaire', selectedQuestionnaireId],
    queryFn: async () => {
      if (!selectedQuestionnaireId) {
        return { questionnaire: null, pdfTemplate: null };
      }
      try {
        const data = await getArtifact({
          artifactType: QUESTIONNAIRES,
          artifactId: selectedQuestionnaireId,
          visibility: PRIVATE,
        });
        const pdfTemplateData = data.pdfTemplate;
        setDocGenerated(pdfTemplateData);
        return data.questionnaire;
      } catch (err) {
        console.error('Error fetching questionnaire details:', err);
        throw err;
      }
    },
    enabled: !!selectedQuestionnaireId,
    onError: (error) => {
      console.error('Error fetching questionnaire details:', error);
    },
  });

  const handleChange = (event) => {
    const selectedId = event.target.value;
    setSelectedQuestionnaireId(selectedId);
    setShowQuestionnaireReport(false);
    queryClient.invalidateQueries(['questionnaire', selectedId]);
  };

  const questionnaireSaveCallback = async (fhirQuestionnaire, fhirQuestionnaireResponse, responses, isFinished) => {
    setQuestionnaireSaving(true);
    setLoading(true);
    const cdrSubjectIdValue = cdrIdentifier ? cdrIdentifier.value : null;

    const pdfExtension = {
      url: 'pdftemplate-base64',
      valueBase64Binary: docGenerated,
    };

    const updatedFhirQuestionnaire = {
      ...fhirQuestionnaire,
      extension: [...fhirQuestionnaire.extension, pdfExtension],
    };

    let questionnaireResponse =
      typeof fhirQuestionnaireResponse === 'object' ? fhirQuestionnaireResponse : JSON.parse(fhirQuestionnaireResponse);

    if (questionnaireResponse) {
      const practitionerId = await fetchPractitionerId();
      questionnaireResponse.id = questionnaireResponseIdPlaceholder;
      questionnaireResponse.subject = { reference: `${targetSubjectType}/${cdrSubjectIdValue}` };
      questionnaireResponse.author = { reference: `Practitioner/${practitionerId}` };
      // in the case of Location, let the default value be the same as the author for now
      // TO DO : Implement two source dropdowns - select the source type (Practitioner or Patient)
      // and select a source id (id of the practitioner or patient answering
      // - populate list of practitioners or patients based on the source type selection
      if (targetSubjectType === 'Location') {
        questionnaireResponse.source = { reference: `Practitioner/${practitionerId}` };
      } else {
        // for Practitioner, let the default value be the same as the subject
        questionnaireResponse.source = { reference: `${targetSubjectType}/${cdrSubjectIdValue}` };
      }

      if (fhirQuestionnaire.url) {
        questionnaireResponse.questionnaire = fhirQuestionnaire.url;
      }
    }

    const fhirQuestionnareResponseUpdated = {
      questionnaire: updatedFhirQuestionnaire,
      questionnaireResponse: questionnaireResponse,
    };
    setIsQuestionnaireFinished(isFinished);

    if (isFinished) {
      setFhirQuestionnaireResponse(questionnaireResponse);

      try {
        console.log('AAAAAAA');
        await fetchNextRoute(
          'organizationCDR',
          `/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse?patient=${questionnaireResponseIdPlaceholder}`,
          {
            method: 'POST',
            body: JSON.stringify({ ...fhirQuestionnareResponseUpdated }),
          },
        );
        setLoading(false);
        console.log('Questionnaire response saved successfully');
        setShowQuestionnaireReport(true);
        if (onComplete) {
          await onComplete();
        }
      } catch (error) {
        setLoading(false);
        displayErrorMessage('Failed to save questionnaire response');
        console.error('Error saving questionnaire response:', error);
      } finally {
        setQuestionnaireSaving(false);
      }
    } else {
      setQuestionnaireSaving(false);
    }
  };

  function displayErrorMessage(message) {
    openSnackbar({
      variant: 'error',
      msg: message,
    });
  }

  const handleNext = () => {
    if (selectedQuestionnaireId) {
      setShowQuestionnaire(true);
    } else {
      displayErrorMessage('Please select a questionnaire first');
    }
  };

  return (
    <>
      {!showQuestionnaire && (
        <Box ml={2} mr={2} mb={2} mt={-1}>
          <Stack direction="row" spacing={Constants.formFieldSpacing}>
            <FormControl sx={{ width: '100%', maxWidth: '500px' }} size="small">
              <InputLabel required>Select a Questionnaire</InputLabel>
              <Select label="Select a Questionnaire" value={selectedQuestionnaireId} onChange={handleChange} required>
                {targetQuestionnaires?.map((questionnaire) => (
                  <MenuItem key={questionnaire.artifactId} value={questionnaire.artifactId}>
                    {questionnaire.shortName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Button onClick={onCancel} variant="outlined">
              Cancel
            </Button>
            <Button onClick={handleNext} variant="contained" disabled={!selectedQuestionnaireId}>
              Next
            </Button>
          </Stack>
        </Box>
      )}
      {loading && <Loader active={loading} />}
      {!showQuestionnaireReport && showQuestionnaire && fetchedQuestionnaire && (
        <Box mt={-1}>
          <Box display="flex" justifyContent="flex-end" mr={2} mb={-2}>
            <Button onClick={onCancel} variant="outlined" disabled={questionnaireSaving}>
              Cancel
            </Button>
          </Box>
          <QuestionnaireComponentV2
            fhirQuestionnaire={fetchedQuestionnaire}
            fhirResponse={initialResponse}
            questionnaireCallback={questionnaireSaveCallback}
          />
        </Box>
      )}
      {showQuestionnaireReport && (
        <>
          <Box display="flex" justifyContent="flex-end" mr={2} mt={-1}>
            <Button onClick={onCancel} variant="contained">
              Next
            </Button>
          </Box>
          <Box>
            <QuestionnaireReport
              fhirQuestionnaireResponse={fhirQuestionnaireResponse}
              identifier={clientId}
              subjectType={targetSubjectType}
              clientInfoAvailable={true}
            />
          </Box>
        </>
      )}
    </>
  );
}
