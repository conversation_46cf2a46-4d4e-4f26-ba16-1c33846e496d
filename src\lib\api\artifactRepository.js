import CustomError from '../error/CustomError';
import { NO_STORE } from '../constant';
import { clientFetch, addCookie } from '@/lib/fetch/client';

const BASE_URL = `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/artifactRepository`;

export const deleteArtifact = async ({ artifactType, visibility, artifactId }) => {
  try {
    const res = await fetch(`${BASE_URL}/${visibility}/${artifactType}/${artifactId}`, {
      method: 'DELETE',
    });
    return res;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const putQuestionnaire = async (params) => {
  // Made a separate function for questionnaire as the new id needs to be injected in the fhir content as well.
  if (!params?.artifactId) {
    const artifactId = crypto.randomUUID();
    params.requestBody.questionnaire.id = artifactId; // Replace questionnaire fhir json's id attribute to the new artifact id.
    params.requestBody.questionnaire.url = `Questionnaire/${artifactId}`; // Replace url attribute with new id
    params.artifactId = artifactId;
  }
  return await putArtifact(params);
};

export const putArtifact = async ({ artifactType, visibility, requestBody, artifactId }) => {
  try {
    if (!artifactType || !requestBody) {
      throw Error('Bad Request. Artifact type and request body should be provided');
    }

    if (!artifactId) {
      // IMPORTANT: By not passing in `artifactId`, a new artifact is created in Artifact Repository.
      artifactId = crypto.randomUUID();
    }

    const res = await fetch(`${BASE_URL}/${visibility}/${artifactType}/${artifactId}`, {
      method: 'PUT',
      body: JSON.stringify(requestBody),
    });
    return res;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getArtifact = async ({ artifactType, visibility, artifactId, includeMetadata }) => {
  const uuidRegexAtEnd = /[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  const match = artifactId.match(uuidRegexAtEnd);
  if (!match) {
    throw new Error("There's no UUID from artifactId");
  }
  artifactId = match[0];
  try {
    const res = await clientFetch(addCookie)(
      `${BASE_URL}/${visibility}/${artifactType}/${artifactId}${includeMetadata ? '?includeMetadata=true' : ''}`,
      {
        cache: NO_STORE,
      },
    );

    if (res.status !== 200) {
      throw new CustomError({
        status: res.status,
        message: await res.json(),
      });
    }
    const data = await res.json();
    return data.responseBody;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getArtifactListByVisibility = async ({ artifactType, visibility, queryStringParams }) => {
  const { contentStatus, publishStatuses } = queryStringParams || {};

  const queryStrings = [];
  if (contentStatus) {
    queryStrings.push(`contentStatus=${contentStatus}`);
  }
  if (publishStatuses && publishStatuses.length > 0) {
    queryStrings.push(`publishStatus=${publishStatuses.join(',')}`);
  }

  const finalQueryString = queryStrings.length > 0 ? queryStrings.join('&') : '';
  try {
    const res = await clientFetch(addCookie)(`${BASE_URL}/${visibility}/${artifactType}?${finalQueryString}`, {
      cache: 'no-store',
    });
    if (res.status !== 200) {
      throw new CustomError({
        status: res.status,
        message: await res.json(),
      });
    }
    const data = await res.json();
    return data.responseBody;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
