import { NextResponse } from 'next/server';
import { ORG_REQUESTS_API_BASE_URL } from '@/lib/constant';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import { getToken } from 'next-auth/jwt';
import ApiError from '@/lib/error/ApiError';

/**
 * Creates a new request in the organization requests service
 * @param {Request} request - The HTTP request
 * @returns {Promise<Response>} The HTTP response
 */
export async function POST(request) {
  try {
    // Get the token which contains organization ID
    const token = await getToken({ req: request });
    const organizationId = token?.orgId;

    if (!organizationId) {
      return NextResponse.json({ message: 'Unauthorized: Organization ID not found in session' }, { status: 401 });
    }

    // Get request body
    const requestBody = await request.json();

    // Build the API URL with the actual organization ID
    const apiUrl = `${ORG_REQUESTS_API_BASE_URL}/organizations/${organizationId}/requests`;
    console.log('Calling organization requests service at:', apiUrl);

    // Use the middleware pattern with proper token handling
    const response = await fetchWithMiddleware(
      addMachineAccessToken(request),
      addUserToken(request, { replaceOrgId: false }),
    )(apiUrl, {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const responseData = await response.json();
    console.log('Request created successfully:', responseData);

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error in requests API route:', error);
    const apiError = new ApiError({ status: 500, message: error.message });
    return apiError.toNextResponse();
  }
}
