import { DOC_GEN_URL } from '@/lib/constant';
import { addMachineAccessToken, fetchWithMiddleware } from '@/lib/fetch/server';

export async function retrievePDFDocument(req, formData) {
  const response = await fetchWithMiddleware(addMachineAccessToken(req))(`${DOC_GEN_URL}`, {
    method: 'POST',
    body: JSON.stringify(formData),
  });
  const serverResponse = await response.json();
  if (response.status !== 201) {
    throw new Error(serverResponse.message);
  }
  return serverResponse;
}
