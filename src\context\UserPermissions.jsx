'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import AccessDenied from '@/components/AccessDenied';
import { usePathname } from 'next/navigation';
import { toolbarConfig, menuConfig, ORGANIZATION_ID, ORGANIZATION_USER_ID } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

const UserPermissions = createContext();

export const usePermissions = () => useContext(UserPermissions);

export const PermissionsProvider = ({ children }) => {
  const pathname = usePathname();

  const [permissions, setPermissions] = useState([]);
  const [roles, setRole] = useState([]);
  const [authorized, setAuthorized] = useState(true);
  const [loading, setLoading] = useState(true);
  const [firstName, setFirstName] = useState(null);
  const [lastName, setLastName] = useState(null);
  const { data: session, status } = useSession();

  const fetchPermissionsData = async () => {
    if (status === 'authenticated') {
      try {
        const responseData = await fetchNextRoute(
          'organizationData',
          `/organizations/${ORGANIZATION_ID}/users/${ORGANIZATION_USER_ID}`,
        );

        const data = await responseData.json();
        setPermissions(data.features || []);
        setFirstName(data.firstName || null);
        setRole(data.roles || []);
        setLastName(data.lastName || null);
      } catch (error) {
        console.error('Error fetching data from API:', error.message || error);
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchPermissionsData();
  }, [status]);

  const generatePathToPermissionPairs = (configs, pathToPermissionPairs = {}) => {
    configs.forEach((config) => {
      config.forEach((item) => {
        if (item.path) {
          const permission = item.permission || '';
          pathToPermissionPairs[item.path] = Array.isArray(permission) ? permission.join(', ') : permission;
        }
        if (item.children) {
          generatePathToPermissionPairs([item.children], pathToPermissionPairs);
        }
      });
    });
    return pathToPermissionPairs;
  };

  const PATH_TO_PERMISSION_PAIRS = generatePathToPermissionPairs([menuConfig, toolbarConfig]);

  const checkPermissionForPath = () => {
    for (const basePath in PATH_TO_PERMISSION_PAIRS) {
      if (pathname.startsWith(basePath)) {
        const requiredPermission = PATH_TO_PERMISSION_PAIRS[basePath];
        const requiredPermissionsArray = requiredPermission.split(',').map((permission) => permission.trim());
        return requiredPermissionsArray.some((permission) => permission === '' || permissions.includes(permission));
      }
    }
    return true;
  };

  useEffect(() => {
    if (!loading) {
      setAuthorized(checkPermissionForPath());
    }
  }, [permissions, pathname, loading]);

  if (loading) {
    return null;
  }

  return (
    <>
      {authorized ? (
        <UserPermissions.Provider
          value={{ permissions, setPermissions, fetchPermissionsData, firstName, lastName, authorized, roles }}
        >
          {children}
        </UserPermissions.Provider>
      ) : (
        <AccessDenied />
      )}
    </>
  );
};
