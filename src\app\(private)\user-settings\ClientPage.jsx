'use client';

import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { OrgUserEditor } from './OrgUserEditor';
import useNotification from '@/lib/hooks/useNotification';
import { addCsrfToken, clientFetch } from '@/lib/fetch/client';
import { customSignOut } from '@/lib/auth/customSignout';
import { ORGANIZATION_ID } from '@/lib/constant';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

function ClientPage() {
  const { data: session } = useSession();
  const { user } = session || {};
  const [usersData, setUsersData] = useState([]);
  const [roleNameData, setRoleNameData] = useState([]);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [selectedUserInfo, setSelectedUserInfo] = useState([]);
  const [isRightColumnVisible, setIsRightColumnVisible] = useState(false);
  const openSnackbar = useNotification();

  const fetchUsers = async () => {
    try {
      const responseData = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/users`);

      const data = await responseData.json();

      const users =
        data.users.sort((a, b) => {
          // Compare by firstName
          if (a.firstName < b.firstName) return -1;
          if (a.firstName > b.firstName) return 1;
          // If firstName is the same, compare by lastName
          if (a.lastName < b.lastName) return -1;
          if (a.lastName > b.lastName) return 1;
          return 0;
        }) || [];

      setUsersData(users);
    } catch (error) {
      console.error('Error fetching data from /organizations/${ORGANIZATION_ID}/users API:', error.message || error);
    }
  };

  useEffect(() => {
    const fetchRoleName = async () => {
      try {
        const responseData = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/roles`);
        const data = await responseData.json();
        const roles = data.roles || [];
        setRoleNameData(roles);
      } catch (error) {
        console.error('Error fetching data from /organizations/${ORGANIZATION_ID}/roles API:', error.message || error);
      }
    };

    fetchUsers();
    fetchRoleName();
  }, []);

  const handleItemClickCallback = async (userId) => {
    try {
      const responseData = await fetchNextRoute(
        'organizationData',
        `/organizations/${ORGANIZATION_ID}/users/${userId}`,
      );

      const userInfo = await responseData.json();
      setSelectedUserInfo(userInfo);
      setSelectedUserId(userId);
      setIsRightColumnVisible(true);
    } catch (error) {
      console.error(
        'Error fetching data from /organizations/${ORGANIZATION_ID}/users/${userId} API:',
        error.message || error,
      );
    }
  };

  const handleButtonClickCallback = () => {
    setSelectedUserId(null);
    setSelectedUserInfo({ emailAddress: '', phoneNumber: '', firstName: '', lastName: '', roles: [] });
    setIsRightColumnVisible(true);
  };

  const createUserCallback = async ({ username, emailAddress, phoneNumber }) => {
    try {
      const response = await clientFetch(addCsrfToken)(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/adminCreateUser`,
        {
          method: 'POST',
          body: JSON.stringify({
            username,
            emailAddress,
            phoneNumber,
          }),
        },
      );

      if (response.status === 200) {
        return { success: true };
      }

      const result = await response.json();
      if (result.errorName === 'UsernameExistsException') {
        return { success: false, message: 'Email or phone number already exists' };
      }
    } catch (error) {
      console.error(error);
      return { success: false, message: error.response.data.message };
    }
  };

  const invalidPhoneNumber = (value) => {
    // NOTE: this could be done as setError in UserEditor instead
    const regex = /^\+[1-9][0-9]{9,12}$/; // this is stricter than the AWS allowed format (max length is 12 instead of 24)
    if (!regex.test(value)) {
      return 'Phone number must be +<country-code><number> (example +19998887777)';
    }
    return false;
  };

  const handleUserFormSaveCallback = async ({ allValues, dirtyValues }) => {
    const phoneNumberIsInvalid = invalidPhoneNumber(allValues.phoneNumber);
    if (allValues.phoneNumber !== '' && phoneNumberIsInvalid) {
      openSnackbar({
        variant: 'error',
        msg: phoneNumberIsInvalid,
      });
      return false;
    }

    try {
      const endpoint = selectedUserId
        ? `/organizations/${ORGANIZATION_ID}/users/${selectedUserId}`
        : `/organizations/${ORGANIZATION_ID}/users`;
      const responseData = await fetchNextRoute('organizationData', endpoint, {
        method: selectedUserId ? 'PUT' : 'POST',
        body: JSON.stringify({
          ...allValues,
          status: 'ACTIVE',
          roleIds: allValues.roles.map((roleName) => {
            return roleNameData.find((role) => role.name === roleName)?.id;
          }),
        }),
      });

      const data = await responseData.json();

      if (!responseData.ok) {
        console.error('Request failed with status:', responseData.status, data.message);
        openSnackbar({
          variant: 'error',
          msg: 'Save user failed: ' + data.message, //TODO update this according to error code sent from org data - requires org data backend changes
        });
        return false;
      } else {
        if (!selectedUserId) {
          const cognitoCreateUserResult = await createUserCallback({
            username: data.userId,
            emailAddress: allValues.emailAddress,
            phoneNumber: allValues.phoneNumber,
          });

          if (cognitoCreateUserResult && cognitoCreateUserResult.success) {
            console.log('Create user succeeded');
            fetchUsers();
            handleItemClickCallback(data.userId);
            return true;
          } else {
            console.error('Create request failed:', cognitoCreateUserResult);
            openSnackbar({
              variant: 'error',
              msg: 'Create user failed: ' + cognitoCreateUserResult,
            });
            deleteOrgDataUser(data.userId);
            return false;
          }
        } else {
          if (dirtyValues.emailAddress) {
            // Update email in Cognito
            const updateEmailResult = await updateCognitoEmail(selectedUserId, allValues.emailAddress);
            if (!updateEmailResult.success) {
              // Rollback org data update if Cognito update fails
              const rollbackResponse = await fetch(endpoint, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                  userId: selectedUserId,
                },
                body: JSON.stringify({
                  ...selectedUserInfo,
                  roleIds: selectedUserInfo.roles.map((role) => role.id),
                }),
              });

              if (!rollbackResponse.ok) {
                console.error('Rollback failed with status:', rollbackResponse.status);
                openSnackbar({
                  variant: 'error',
                  msg: 'Failed to update email in Cognito and rollback unsuccessful. Please contact support.',
                });
              } else {
                openSnackbar({
                  variant: 'error',
                  msg: 'Failed to update email in Cognito but rollback in orgData succeeded.',
                });
              }
              return false;
            }
          }
          console.log('Save user info succeeded');
          fetchUsers();
          return true;
        }
      }
    } catch (error) {
      console.error('Error saving data to API:', error.message || error);
      openSnackbar({
        variant: 'error',
        msg: 'Something went wrong while saving information. Please contact technical support.',
      });
      return false;
    }
  };

  const deleteOrgDataUser = async (targetUserId) => {
    const orgDeleteResponse = await fetchNextRoute(
      'organizationData',
      `/organizations/${ORGANIZATION_ID}/users/${selectedUserId}`,
      {
        method: 'DELETE',
      },
    );

    if (!orgDeleteResponse.ok) {
      console.error('Delete user from org data request failed with status:', orgDeleteResponse.status);
      orgDeleteResponse
        .text()
        .then((errorText) => {
          console.error('Error message from the response:', errorText);
          openSnackbar({
            variant: 'error',
            msg: `Failed to delete user in OrgData`,
          });
        })
        .catch((err) => {
          console.error('Failed to read the error message from the response:', err);
          openSnackbar({
            variant: 'error',
            msg: 'Failed to delete user: Unable to parse error response',
          });
        });
      return false;
    }
    console.log('User successfully deleted from organization database');
    return true;
  };

  const deleteCognitoUser = async (targetUserId) => {
    const cognitoDeleteResponse = await fetch(`${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/adminDeleteUser`, {
      method: 'DELETE',
      headers: {
        userId: targetUserId,
      },
    });
    if (!cognitoDeleteResponse.ok) {
      console.error('Delete user from cognito request failed with status:', cognitoDeleteResponse.status);
      const errorText = await cognitoDeleteResponse.text();
      return { success: false, error: errorText, status: cognitoDeleteResponse.status };
    }
    console.log('User successfully deleted from Cognito');
    return { success: true };
  };

  const deleteUserCallback = async ({ userId = null }) => {
    try {
      const targetUserId = selectedUserId || userId;
      const userInfoResponse = await fetchNextRoute(
        'organizationData',
        `/organizations/${ORGANIZATION_ID}/users/${selectedUserId}`,
      );

      if (!userInfoResponse.ok) {
        console.error('Failed to fetch user info with status:', userInfoResponse.status);
        openSnackbar({
          variant: 'error',
          msg: 'Failed to delete user: Unable to fetch user information',
        });
        return false;
      }
      const userInfo = await userInfoResponse.json();

      // Delete in Organization Data
      const orgDeleteSuccess = await deleteOrgDataUser(targetUserId);
      if (!orgDeleteSuccess) return false;

      // Delete in Cognito
      const result = await deleteCognitoUser(targetUserId);
      // If cognito deletion fails with status 400 (not found)
      // then we would still allow the deletion from org data to persist
      if (!result.success && result.status !== 400) {
        console.error('Cognito deletion failed:', result.error);
        const transformedUserInfo = {
          emailAddress: userInfo.emailAddress,
          phoneNumber: userInfo.phoneNumber || '',
          firstName: userInfo.firstName,
          lastName: userInfo.lastName,
          roles: userInfo.roles.map((role) => role.name),
          roleIds: userInfo.roles.map((role) => role.id),
        };
        const endpoint = `/organizations/${ORGANIZATION_ID}/users`;
        const rollbackResponse = await fetchNextRoute('organizationData', endpoint, {
          method: 'POST',
          body: JSON.stringify({ ...transformedUserInfo }),
        });
        if (!rollbackResponse.ok) {
          console.error('Rollback failed with status:', rollbackResponse.status);
          openSnackbar({
            variant: 'error',
            msg: 'Delete User failed and rollback unsuccessful. Please contact support.',
          });
        } else {
          openSnackbar({
            variant: 'error',
            msg: 'Failed to delete user in cognito but rollback in orgData succeeded.',
          });
        }
        fetchUsers();
        return false;
      } else if (result.status === 400) {
        console.log(
          'Cognito user deletion responded with:',
          result.error,
          '\nDeletion in Org Data is still allowed to clean up mismatching records',
        );
      }
      console.log('Delete user succeeded');
      setUsersData((prevUsersData) => prevUsersData.filter((user) => user.id !== targetUserId));
      setSelectedUserInfo(null);
      setSelectedUserId(null);
      setIsRightColumnVisible(false);
      if (targetUserId === user.orgUserId) {
        customSignOut({ callbackUrl: '/' });
      }
      return true;
    } catch (error) {
      console.error('Error deleting user:', error.message || error);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to delete user',
      });
      return false;
    }
  };

  const updateCognitoEmail = async (targetUserId, newEmail) => {
    const cognitoUpdateResponse = await fetch(
      `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/adminUpdateUserEmail`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: targetUserId,
          newEmail,
        }),
      },
    );

    if (!cognitoUpdateResponse.ok) {
      console.error('Update email in Cognito request failed with status:', cognitoUpdateResponse.status);
      const errorText = await cognitoUpdateResponse.text();
      return { success: false, error: errorText, status: cognitoUpdateResponse.status };
    }

    console.log('Email successfully updated in Cognito');
    return { success: true };
  };

  return (
    <OrgUserEditor
      usersData={usersData}
      rolesData={roleNameData}
      setSelectedUserId={setSelectedUserId}
      selectedUserId={selectedUserId}
      selectedUserInfo={selectedUserInfo}
      handleItemClickCallback={handleItemClickCallback}
      isRightColumnVisible={isRightColumnVisible}
      handleButtonClickCallback={handleButtonClickCallback}
      handleUserFormSaveCallback={handleUserFormSaveCallback}
      deleteUserCallback={deleteUserCallback}
    />
  );
}

export default ClientPage;
