'use client';

import { SnackbarProvider } from 'notistack';
import { ErrorOutline, InfoOutlined, TaskAlt, WarningAmberOutlined } from '@mui/icons-material';

export default function CustomSnackbarProvider({ children }) {
  const snackbarIconVariants = {
    success: <TaskAlt fontSize="small" />,
    error: <ErrorOutline fontSize="small" />,
    info: <InfoOutlined fontSize="small" />,
    warning: <WarningAmberOutlined fontSize="small" />,
  };

  return (
    <SnackbarProvider iconVariant={snackbarIconVariants} maxSnack={3}>
      {children}
    </SnackbarProvider>
  );
}
