'use server';

import { GET_LOCATIONS_LIST, GET_SERVICES_LIST } from '@/lib/widget-editor/utils/constants/awsApiEndpoints';

import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import ApiError from '@/lib/error/ApiError';
import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';
const BASE_URL = process.env.NEXT_PUBLIC_SCHEDULER_BOOKING_BASE_URL;

export const server_fetchServicesList = async () => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(BASE_URL + GET_SERVICES_LIST);
    const data = await res.json();

    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }
    const list = data?.compose?.include[0]?.concept.map((option) => ({ ...option, name: option.display }));
    return list;
  } catch (error) {
    console.log('failed to fetch services list');
    throw error;
  }
};

export const server_fetchLocationsList = async () => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(BASE_URL + GET_LOCATIONS_LIST);
    const data = await res.json();

    if (!res.ok) {
      throw new ApiError({ status: res.status, message: data });
    }

    const list = data?.entry?.map((location) => location.resource);
    return list;
  } catch (error) {
    console.log('failed to fetch services list');
    throw error;
  }
};
