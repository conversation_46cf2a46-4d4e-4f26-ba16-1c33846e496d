'use client';

import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback, useEffect, useRef } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  IconButton,
  Radio,
  Chip,
  RadioGroup,
  FormControlLabel,
  CircularProgress,
  Avatar,
} from '@mui/material';
import { Close } from '@mui/icons-material';
import { getArtifactListByVisibility } from '@/lib/api/artifactRepository';
import {
  QUESTIONNAIRES,
  PRIVATE,
  PUBLIC,
  BOTH,
  TYPE_QUESTIONNAIRE_RESPONSE,
  REQUEST_SOURCE_COORDINATOR,
  ORGANIZATION_ID,
} from '@/lib/constant';
import * as Constants from '@/app/globalConstants';
import useNotification from '@/lib/hooks/useNotification';
import { server_orgMessaging, hie_orgMessaging } from '@/actions/orgMessaging';
import { createQuestionnaireRequest } from '@/actions/orgRequests';
import Loader from '@/components/Loader';
import { useSession } from 'next-auth/react';
import { getOrgMetaData } from '@/lib/api/common';
import { getOrgSettings } from '@/lib/api/orgData';
import { ORGANIZATION_METADATA, ORGANIZATION_SETTINGS } from '@/lib/constant';
import { useQuestionnaire } from '@/context/QuestionnaireContext';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

const MessageDialog = ({
  open,
  onClose,
  title,
  recipientName,
  recipientContact,
  body,
  onChange,
  onSend,
  sending,
  isEmail,
  isSms,
  isHie,
  subject,
  onSubjectChange,
  selectedQuestionnaire,
}) => (
  <Dialog
    open={open}
    onClose={onClose}
    sx={{
      '& .MuiDialog-paper': {
        width: isEmail ? '570px' : '520px',
        maxWidth: '90vw',
        borderRadius: '5px',
        overflow: 'hidden',
      },
    }}
  >
    <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 1, px: 2 }}>
      <Typography variant="subtitle1" fontWeight="bold">
        {title}
      </Typography>
      <IconButton size="small" onClick={() => onClose(false)}>
        <Close fontSize="small" />
      </IconButton>
    </DialogTitle>
    <DialogContent dividers sx={{ padding: 0, borderTop: '1px solid #e0e0e0', borderBottom: 'none' }}>
      {(isEmail || isSms) && (
        <Typography
          variant="subtitle1"
          sx={{ color: 'black', p: 0.5, pl: 1, m: 2, border: '1px solid rgba(0, 0, 0, 0.23)', borderRadius: 1 }}
        >
          To: <b>{recipientName}</b> &lt;{recipientContact}&gt;
        </Typography>
      )}
      {isHie && (
        <Typography
          variant="subtitle1"
          sx={{
            color: 'black',
            p: 0.5,
            pl: 1,
            m: 2,
            border: '1px solid rgba(0, 0, 0, 0.23)',
            borderRadius: 1,
            display: 'flex',
            alignItems: 'center',
            lineHeight: 1.5,
          }}
        >
          <Chip avatar={<Avatar src={process.env.NEXT_PUBLIC_ICON_NAME} />} label={recipientName} size="small" />
        </Typography>
      )}

      {isEmail && (
        <Box
          sx={{
            color: 'black',
            p: 0.5,
            pl: 1,
            m: 2,
            border: '1px solid rgba(0, 0, 0, 0.23)',
            borderRadius: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Typography variant="subtitle1" component="span" sx={{ display: 'flex', alignItems: 'center' }}>
            Subject:{' '}
          </Typography>
          <TextField
            variant="standard"
            value={subject}
            onChange={(e) => onSubjectChange(e.target.value)}
            sx={{
              ml: 1,
              flex: 1,
              '& .MuiInputBase-root': {
                '&:before, &:after': {
                  borderBottom: 'none',
                },
                '&:hover:not(.Mui-disabled):before': {
                  borderBottom: 'none',
                },
                display: 'flex',
                alignItems: 'center',
              },
              '& .MuiInputBase-input': {
                padding: '2px 0',
              },
            }}
            InputProps={{
              disableUnderline: true,
            }}
          />
        </Box>
      )}
      {isEmail ? (
        <Box
          sx={{
            border: '1px solid rgba(0, 0, 0, 0.23)',
            borderRadius: 1,
            m: 2,
            '& .quill': {
              '& .ql-toolbar': {
                border: 'none',
                borderBottom: '1px solid rgba(0, 0, 0, 0.23)',
                p: 0.5,
              },
              '& .ql-container': {
                border: 'none',
              },
            },
          }}
        >
          <ReactQuill
            theme="snow"
            value={body}
            onChange={onChange}
            modules={{
              toolbar: [
                [{ header: [1, 2, false] }],
                ['bold', 'italic', 'underline', 'strike', 'blockquote'],
                [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
                ['link', 'image'],
                ['clean'],
              ],
            }}
            style={{ height: '160px' }}
          />
        </Box>
      ) : (
        <TextField
          multiline
          rows={7}
          value={body}
          onChange={onChange}
          placeholder="Write a message here..."
          sx={{ p: 1, ml: 1, mt: -1, mb: 1 }}
        />
      )}
    </DialogContent>
    <DialogActions
      sx={{
        display: 'flex',
        justifyContent: isHie ? 'space-between' : 'flex-end',
        alignItems: 'center',
        px: 2,
      }}
    >
      {isHie && (
        <Typography variant="h4" sx={{ fontSize: '15px', fontWeight: 500, mb: 2 }}>
          {selectedQuestionnaire}
        </Typography>
      )}
      <Button variant="contained" onClick={onSend} disabled={sending} sx={{ mt: -1.5, mb: 0.5 }}>
        Send
      </Button>
    </DialogActions>
  </Dialog>
);

export default function RequestToRespondToQuestionnaire(props) {
  const { data: session } = useSession();
  const user = session?.user;
  const hasAppendedLinkEmail = useRef(false);
  const hasAppendedLinkSms = useRef(false);
  const hasAppendedLinkHie = useRef(false);
  const { clientId, targetSubjectType, onCancel } = props;
  const {
    selectedQuestionnaire,
    setSelectedQuestionnaire,
    selectedRepository,
    setSelectedRepository,
    filteredChannels,
    setFilteredChannels,
  } = useQuestionnaire();
  const queryClient = useQueryClient();
  const openSnackbar = useNotification();
  const [selectedQuestionnaireId, setSelectedQuestionnaireId] = useState('');
  const [selectedChannel, setSelectedChannel] = useState('');
  const [emailBody, setEmailBody] = useState('');
  const [smsBody, setSmsBody] = useState('');
  const [hieBody, setHieBody] = useState('');
  const [openDialog, setOpenDialog] = useState(null);
  const [loading, setLoading] = useState(false);
  const [emailSending, setEmailSending] = useState(false);
  const [smsSending, setSmsSending] = useState(false);
  const [clientData, setClientData] = useState({});
  const [dueDate, setDueDate] = useState(dayjs());
  const [expiryDate, setExpiryDate] = useState(dayjs().add(2, 'week'));
  const [reminder, setReminder] = useState(7); // Default reminder of 7 days
  const [isExpiryDateValid, setIsExpiryDateValid] = useState(true); // Track if expiry date is valid
  const lastValidExpiryDate = useRef(dayjs().add(2, 'week'));
  const [emailSubject, setEmailSubject] = useState('');
  const [showConfigWarning, setShowConfigWarning] = useState(false);

  const { data: questionnaireList, isLoading: isQuestionnaireLoading } = useQuery({
    queryKey: [QUESTIONNAIRES, selectedRepository],
    queryFn: () =>
      getArtifactListByVisibility({
        artifactType: QUESTIONNAIRES,
        visibility: selectedRepository === 'Public' ? PUBLIC : PRIVATE,
        queryStringParams: { publishStatuses: [PRIVATE, BOTH] },
      }),
    enabled: !!selectedRepository,
  });

  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => getOrgMetaData(),
  });

  const orgSettingsQuery = useQuery({ queryKey: [ORGANIZATION_SETTINGS], queryFn: getOrgSettings });

  const targetQuestionnaires = questionnaireList?.filter(
    (questionnaire) => questionnaire.subjectType === targetSubjectType,
  );

  // Helper function to extract Cambian ID from the identifiers array
  const getCambianId = (clientData) => {
    if (!clientData?.identifiers || !Array.isArray(clientData.identifiers)) {
      return null;
    }

    const cambianIdIdentifier = clientData.identifiers.find((identifier) => identifier.type === 'CAMBIAN_ID');

    return cambianIdIdentifier?.value || null;
  };

  const fetchClientData = async () => {
    try {
      setLoading(true);
      const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${clientId}`);
      const dataResponse = await response.json();
      const clientData = dataResponse;
      setClientData(clientData);
      setLoading(false);
      return clientData;
    } catch (err) {
      setLoading(false);
      console.error('Error fetching data:', err.message);
      openSnackbar({ variant: 'error', msg: 'Error fetching client data' });
      return;
    }
  };

  const updateAvailableChannels = useCallback(
    (clientData, repositoryValue = selectedRepository) => {
      const availableChannels = [];
      if (clientData?.emailAddresses?.[0]?.emailAddress) {
        availableChannels.push('EMAIL');
      }
      if (clientData?.phoneNumbers?.[0]?.phoneNumber) {
        availableChannels.push('SMS');
      }
      const cambianId = getCambianId(clientData);
      if (repositoryValue === 'Public' && cambianId) {
        availableChannels.push('HIE');
      }
      setFilteredChannels(availableChannels);
      if (selectedChannel && !availableChannels.includes(selectedChannel)) {
        setSelectedChannel('');
      }
    },
    [selectedChannel],
  );

  const handleQuestionnaireChange = (event) => {
    const questionnaireId = event.target.value;
    const selected = targetQuestionnaires?.find((q) => q.artifactId === questionnaireId);
    hasAppendedLinkEmail.current = false;
    hasAppendedLinkSms.current = false;
    setEmailBody('');
    setSmsBody('');
    setSelectedQuestionnaireId(questionnaireId);
    setSelectedQuestionnaire(selected);
    queryClient.invalidateQueries(['questionnaire', questionnaireId]);
  };

  const handleChannelChange = (event) => {
    setSelectedChannel(event.target.value);
    hasAppendedLinkEmail.current = false;
    hasAppendedLinkSms.current = false;
    setEmailBody('');
    setSmsBody('');
  };

  const handleRepositoryChange = async (event) => {
    const selectedValue = event.target.value;
    setSelectedRepository(selectedValue);
    setSelectedQuestionnaireId('');
    setSelectedQuestionnaire(null);
    setSelectedChannel('');

    const clientData = await fetchClientData();
    updateAvailableChannels(clientData, selectedValue);
  };

  const handleNext = async () => {
    const clientData = await fetchClientData();
    updateAvailableChannels(clientData, selectedRepository);
    if (!filteredChannels.includes(selectedChannel)) {
      openSnackbar({
        variant: 'error',
        msg: 'Selected channel is no longer available for this client',
      });
      return;
    }

    // Validate dates
    if (!dueDate) {
      openSnackbar({
        variant: 'error',
        msg: 'Please provide a due date',
      });
      return;
    }

    if (!expiryDate) {
      openSnackbar({
        variant: 'error',
        msg: 'Please provide an expiry date',
      });
      return;
    }

    // Validate reminder
    if (!reminder || reminder < 1) {
      openSnackbar({
        variant: 'error',
        msg: 'Please provide a reminder period (in days)',
      });
      return;
    }

    // Ensure expiry date is not before due date
    if (expiryDate.isBefore(dueDate)) {
      openSnackbar({
        variant: 'error',
        msg: 'Expiry date cannot be before due date',
      });
      return;
    }
    setEmailSubject(selectedQuestionnaire?.title || '');

    if ((selectedChannel === 'EMAIL' || selectedChannel === 'SMS') && !orgSettingsQuery?.data.dynamicWidgetId) {
      setShowConfigWarning(true);
      return;
    }

    setOpenDialog(selectedChannel);
    if (selectedChannel === 'EMAIL') {
      hasAppendedLinkEmail.current = false;
    } else if (selectedChannel === 'SMS') {
      hasAppendedLinkSms.current = false;
    } else if (selectedChannel === 'HIE') {
      hasAppendedLinkHie.current = false;
    }
  };

  const handleCloseDialog = () => {
    setLoading(false);
    setOpenDialog(null);
    setEmailBody('');
    setSmsBody('');
    setEmailSending(false);
    setSmsSending(false);
    hasAppendedLinkEmail.current = false;
    hasAppendedLinkSms.current = false;
  };

  const generateWidgetUrl = useCallback(
    (questionnaireId) => {
      const orgId = user?.orgId;
      if (!orgId) {
        openSnackbar({
          variant: 'error',
          msg: 'Technical error. Please contact the administrator.',
        });
        console.log('org id does not exists in user session');
        return null;
      }
      if (!orgSettingsQuery?.data.dynamicWidgetId) {
        return null;
      }

      return `${process.env.NEXT_PUBLIC_WIDGET_BASE_URL}/widget/organizations/${orgId}/questionnaireWidget/${orgSettingsQuery.data.dynamicWidgetId}?qid=${questionnaireId}`;
    },
    [user?.orgId, orgSettingsQuery.data, openSnackbar],
  );

  useEffect(() => {
    if (openDialog && selectedQuestionnaire) {
      const widgetUrl = generateWidgetUrl(selectedQuestionnaire?.artifactId);

      if (!widgetUrl && (openDialog === 'EMAIL' || openDialog === 'SMS')) {
        setOpenDialog(null);
        setShowConfigWarning(true);
        return;
      }

      if (openDialog === 'HIE' && !hasAppendedLinkHie.current) {
        const body = `Please respond to the questionnaire: ${selectedQuestionnaire?.title}`;
        setHieBody(body);
        hasAppendedLinkHie.current = true;
      }

      if (widgetUrl) {
        if (openDialog === 'EMAIL' && !hasAppendedLinkEmail.current) {
          const body = `Please respond to the questionnaire: <a href="${widgetUrl}">${selectedQuestionnaire?.title}</a>`;
          setEmailBody(body);
          hasAppendedLinkEmail.current = true;
        } else if (openDialog === 'SMS' && !hasAppendedLinkSms.current) {
          const body = `Please respond to the questionnaire ${selectedQuestionnaire?.title} using the following link: ${widgetUrl}`;
          setSmsBody(body);
          hasAppendedLinkSms.current = true;
        }
      } else {
        if (openDialog === 'EMAIL') {
          setEmailBody('Failed to generate questionnaire link.');
        } else if (openDialog === 'SMS') {
          setSmsBody('Failed to generate questionnaire link.');
        }
      }
    }
  }, [openDialog, selectedQuestionnaire, clientData, orgMetaDataQuery.data, generateWidgetUrl]);

  // Ensure isExpiryDateValid is updated whenever expiryDate changes
  useEffect(() => {
    setIsExpiryDateValid(!!expiryDate && expiryDate.isValid());
  }, [expiryDate]);

  // Initialize lastValidExpiryDate with the initial expiryDate value
  useEffect(() => {
    lastValidExpiryDate.current = expiryDate;
  }, []);

  const handleSendMessage = async (channel) => {
    try {
      if (channel === 'EMAIL') setEmailSending(true);
      else if (channel === 'SMS') setSmsSending(true);

      const recipient =
        channel === 'EMAIL'
          ? clientData?.emailAddresses?.[0]?.emailAddress
          : clientData?.phoneNumbers?.[0]?.phoneNumber;
      const messageBody = channel === 'EMAIL' ? emailBody.trim() : smsBody.trim();

      if (!recipient) {
        openSnackbar({
          variant: 'error',
          msg:
            channel === 'EMAIL'
              ? 'Failed to send Email. No email address found for this client.'
              : 'Failed to send SMS. No phone number found for this client.',
        });
        return;
      }

      const name =
        `${clientData?.firstName || ''} ${clientData?.middleName || ''} ${clientData?.lastName || ''}`.trim();
      const recipientName = `${name} <${recipient}>`;
      await server_orgMessaging(channel, recipient, recipientName, emailSubject, messageBody, messageBody);

      console.log(`${channel === 'EMAIL' ? 'Email' : 'SMS'} sent successfully`);

      if (channel === 'EMAIL') {
        setEmailSending(false);
        setEmailBody('');
      } else if (channel === 'SMS') {
        setSmsSending(false);
        setSmsBody('');
      }
      setTimeout(() => {
        setSelectedQuestionnaireId('');
        setSelectedChannel('');
        setOpenDialog(null);
        setSelectedQuestionnaire(null);
        hasAppendedLinkEmail.current = false;
        hasAppendedLinkSms.current = false;
        onCancel();
      }, 500);
    } catch (error) {
      if (channel === 'EMAIL') setEmailSending(false);
      else if (channel === 'SMS') setSmsSending(false);
      console.error(`Failed to send ${channel === 'EMAIL' ? 'email' : 'SMS'}:`, error);
      openSnackbar({ variant: 'error', msg: `Failed to send ${channel === 'EMAIL' ? 'email' : 'SMS'}` });
    }
  };

  const handleSendEmail = async () => {
    await handleSendMessage('EMAIL');
  };

  const handleSendSms = async () => {
    await handleSendMessage('SMS');
  };

  const handleSendHIE = async (channel, narrative) => {
    const cambianId = getCambianId(clientData);
    try {
      setLoading(true);
      if (!cambianId) {
        setLoading(false);
        openSnackbar({
          variant: 'error',
          msg: 'Failed to send HIE Message. No Cambian ID found for this client.',
        });
        return;
      }
      if (!selectedQuestionnaireId) {
        setLoading(false);
        openSnackbar({
          variant: 'error',
          msg: 'No questionnaire selected for this client.',
        });
        return;
      }

      // Format dates to ISO string format for request creation
      const formattedDueDate = dueDate ? dueDate.format('YYYY-MM-DD') : '';
      const formattedExpiryDate = expiryDate ? expiryDate.format('YYYY-MM-DD') : '';

      let requestCreated = false;
      let requestData = null;
      try {
        // First create a request in the organization requests service
        const requestResponse = await createQuestionnaireRequest(
          cambianId,
          user?.orgId,
          selectedQuestionnaireId,
          formattedDueDate,
          formattedExpiryDate,
          clientId, // client ID
          reminder, // reminder in days
        );

        console.log('Questionnaire request created:', requestResponse);
        requestCreated = true;

        // Create the complete request object with the format needed
        requestData = {
          requestId: requestResponse.requestId,
          cambianId: cambianId,
          organizationId: user?.orgId,
          requestDetails: {
            questionnaireId: selectedQuestionnaireId,
          },
          requestType: TYPE_QUESTIONNAIRE_RESPONSE,
          dueDate: formattedDueDate,
          expiryDate: formattedExpiryDate,
          requestSource: REQUEST_SOURCE_COORDINATOR,
          reminder: reminder,
          clientId: clientId,
        };
      } catch (requestError) {
        console.error('Failed to create questionnaire request:', requestError);
        // Just log the error, don't show to user yet as we'll still try to send HIE message
      }

      // Then send the HIE message with the request data
      const recipientName =
        `${clientData?.firstName || ''} ${clientData?.middleName || ''} ${clientData?.lastName || ''}`.trim();
      await hie_orgMessaging(channel, cambianId, narrative, requestData, recipientName);
      setLoading(false);

      if (requestCreated) {
        console.log(`Questionnaire request created and message sent successfully`);
      } else {
        openSnackbar({
          variant: 'warning',
          msg: `Message sent successfully, but questionnaire request could not be created.`,
        });
      }

      setTimeout(() => {
        setSelectedQuestionnaireId('');
        setSelectedChannel('');
        setOpenDialog(null);
        setSelectedQuestionnaire(null);
        onCancel();
      }, 500);
    } catch (error) {
      setLoading(false);
      console.error('Failed to send HIE:', error);
      openSnackbar({ variant: 'error', msg: 'Failed to send HIE' });
    }
  };

  // Add handler functions for the date changes
  const handleDueDateChange = (newDate) => {
    setDueDate(newDate);

    // If expiry date is before the new due date, update it to be the same as due date
    if (expiryDate && newDate && expiryDate.isBefore(newDate)) {
      setExpiryDate(newDate);
      setIsExpiryDateValid(true);
    }
  };

  const handleExpiryDateChange = (newDate) => {
    // If the new date is null or invalid, set validity to false
    if (!newDate || !newDate.isValid()) {
      setExpiryDate(null);
      setIsExpiryDateValid(false);
      return;
    }

    // Ensure expiry date is not before the due date
    if (dueDate && newDate.isBefore(dueDate)) {
      openSnackbar({
        variant: 'warning',
        msg: 'Expiry date cannot be before due date',
      });
      // Don't update the state at all when invalid
      return;
    }

    // Only update the ref and state when we have a valid date
    lastValidExpiryDate.current = newDate;
    setExpiryDate(newDate);
    setIsExpiryDateValid(true);
  };

  return (
    <>
      <Box ml={2} mr={2} mb={2} mt={-1}>
        <Stack direction="row" spacing={Constants.formFieldSpacing} alignItems="center" sx={{ mb: 2 }}>
          <Typography variant="subtitle1">Repository *</Typography>
          <RadioGroup row value={selectedRepository} onChange={handleRepositoryChange}>
            <FormControlLabel value="Public" control={<Radio />} label="Public" />
            <FormControlLabel value="Private" control={<Radio />} label="Private" />
          </RadioGroup>
        </Stack>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: Constants.formFieldSpacing }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', maxWidth: '500px', gap: 2 }}>
            <FormControl sx={{ width: '100%' }} size="small">
              <InputLabel required>Select a Questionnaire</InputLabel>
              <Select
                label="Select a Questionnaire"
                value={selectedQuestionnaireId}
                onChange={handleQuestionnaireChange}
                required
              >
                {!selectedRepository ? (
                  <MenuItem disabled>
                    <Typography variant="body2">Please select a repository first</Typography>
                  </MenuItem>
                ) : isQuestionnaireLoading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} />
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      Loading...
                    </Typography>
                  </MenuItem>
                ) : (
                  targetQuestionnaires?.map((questionnaire) => (
                    <MenuItem key={questionnaire.artifactId} value={questionnaire.artifactId}>
                      {questionnaire.title}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>

            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                format="YYYY-MM-DD"
                value={dueDate}
                onChange={handleDueDateChange}
                label="Due Date"
                slotProps={{
                  textField: {
                    size: 'small',
                    fullWidth: true,
                    variant: 'outlined',
                    required: true,
                  },
                }}
              />
            </LocalizationProvider>

            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                format="YYYY-MM-DD"
                value={expiryDate}
                onChange={handleExpiryDateChange}
                label="Expiry Date"
                minDate={dueDate}
                slotProps={{
                  textField: {
                    size: 'small',
                    fullWidth: true,
                    variant: 'outlined',
                    required: true,
                  },
                }}
              />
            </LocalizationProvider>

            <TextField
              label="Reminder (days)"
              type="number"
              value={reminder}
              onChange={(e) => {
                const value = e.target.value;
                // Handle empty string or invalid values
                if (value === '' || isNaN(parseInt(value))) {
                  setReminder(null);
                } else {
                  setReminder(parseInt(value));
                }
              }}
              InputProps={{ inputProps: { min: 1 } }}
              size="small"
              fullWidth
              required
            />
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', maxWidth: '500px', gap: 2 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: { xs: 'stretch', sm: 'flex-start' },
                gap: 2,
              }}
            >
              <FormControl sx={{ width: '100%' }} size="small">
                <InputLabel required>Select a Channel</InputLabel>
                <Select label="Select a Channel" value={selectedChannel} onChange={handleChannelChange} required>
                  {!selectedRepository ? (
                    <MenuItem disabled>
                      <Typography variant="body2">Please select a repository first</Typography>
                    </MenuItem>
                  ) : filteredChannels.length === 0 ? (
                    <MenuItem disabled>
                      <Typography variant="body2">No available channels for this client</Typography>
                    </MenuItem>
                  ) : (
                    filteredChannels.map((channel) => (
                      <MenuItem key={channel} value={channel}>
                        {channel}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  gap: 1,
                }}
              >
                <Button
                  onClick={onCancel}
                  variant="outlined"
                  sx={{
                    width: { xs: '100%', sm: 'auto' },
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleNext}
                  variant="contained"
                  disabled={
                    !selectedChannel ||
                    !selectedQuestionnaire ||
                    !dueDate ||
                    !isExpiryDateValid ||
                    !reminder ||
                    reminder < 1
                  }
                  sx={{ width: { xs: '100%', sm: 'auto' } }}
                >
                  Next
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {loading && <Loader active={loading} />}

      <MessageDialog
        open={openDialog === 'EMAIL'}
        onClose={handleCloseDialog}
        title="New Email"
        recipientName={`${clientData?.firstName || ''} ${clientData?.middleName || ''} ${clientData?.lastName || ''}`.trim()}
        recipientContact={`${clientData?.emailAddresses?.[0]?.emailAddress || ''}`}
        body={emailBody}
        onChange={setEmailBody}
        onSend={handleSendEmail}
        sending={emailSending}
        isEmail={true}
        selectedQuestionnaire={selectedQuestionnaire?.title}
        subject={emailSubject}
        onSubjectChange={setEmailSubject}
      />

      <MessageDialog
        open={openDialog === 'SMS'}
        onClose={handleCloseDialog}
        title="New SMS"
        recipientName={`${clientData?.firstName || ''} ${clientData?.middleName || ''} ${clientData?.lastName || ''}`.trim()}
        recipientContact={`${clientData?.phoneNumbers?.[0]?.phoneNumber || ''}`}
        body={smsBody}
        onChange={setSmsBody}
        onSend={handleSendSms}
        sending={smsSending}
        isSms={true}
        selectedQuestionnaire={selectedQuestionnaire?.title}
      />

      <MessageDialog
        open={openDialog === 'HIE'}
        onClose={handleCloseDialog}
        title="New Message"
        recipientName={`${clientData?.firstName || ''} ${clientData?.middleName || ''} ${clientData?.lastName || ''}`.trim()}
        body={hieBody}
        onChange={setHieBody}
        onSend={() => handleSendHIE('HIE', hieBody)}
        sending={smsSending}
        isHie={true}
        selectedQuestionnaire={selectedQuestionnaire?.title}
      />

      <WidgetConfigurationDialog open={showConfigWarning} onClose={() => setShowConfigWarning(false)} />
    </>
  );
}

const WidgetConfigurationDialog = ({ open, onClose }) => (
  <Dialog
    open={open}
    onClose={onClose}
    sx={{
      '& .MuiDialog-paper': {
        width: '400px',
        maxWidth: '90vw',
        borderRadius: '5px',
        overflow: 'hidden',
      },
    }}
  >
    <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 1, px: 2 }}>
      <Typography variant="subtitle1" fontWeight="bold">
        Cannot Send the Message
      </Typography>
    </DialogTitle>
    <DialogContent dividers sx={{ padding: 2, borderTop: '1px solid #e0e0e0', borderBottom: 'none' }}>
      <Typography variant="body1">
        To send via Email or SMS, please first configure your Default Dynamic Widget in Settings - General.
      </Typography>
    </DialogContent>
    <DialogActions>
      <Button variant="contained" onClick={onClose}>
        Close
      </Button>
    </DialogActions>
  </Dialog>
);
