'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, Grid, Typography } from '@mui/material';
import { useRouter } from 'next/navigation.js';
import { HeaderStyle } from '@cambianrepo/ui';
import { CoordinatorNewClient } from '@cambianrepo/ui';
import useNotification from '@/lib/hooks/useNotification';
import { countriesAndProvinces, ORGANIZATION_ID } from '@/lib/constant';
import { getOrgMetaData } from '@/lib/api/common';
import { ORGANIZATION_METADATA } from '@/lib/constant';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

function CreateAndEditClient(props) {
  const openSnackbar = useNotification();
  const router = useRouter();
  const { data: session } = useSession();
  const userId = session?.user?.orgUserId;
  const clientId = props.id || undefined;
  const [saving, setSaving] = useState(false);
  const [issuerRequiredError, setIssuerRequiredError] = useState(false);
  const [healthcareError, setHealthcareError] = useState(false);
  const [existingClientData, setExistingClientData] = useState(null);

  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => getOrgMetaData(),
  });

  const orgDataValues = { ...orgMetaDataQuery.data?.clientInformation };
  const healthcareValues = Array.isArray(orgMetaDataQuery.data?.idTypes)
    ? orgMetaDataQuery.data.idTypes.filter((item) => item.allowed)
    : [];

  const [firstNameConfigs, setFirstNameConfigs] = useState({
    firstName: '',
    isRequired: false,
    error: false,
    errorText: '',
    allowed: false,
  });

  const [lastNameConfigs, setLastNameConfigs] = useState({
    lastName: '',
    isRequired: false,
    error: false,
    errorText: '',
    allowed: false,
  });

  const [middleNameConfigs, setMiddleNameConfigs] = useState({
    middleName: '',
    isRequired: false,
    error: false,
    errorText: '',
    allowed: false,
  });
  const [dateOfBirthConfigs, setDateOfBirthConfigs] = useState({
    dateOfBirth: null,
    isRequired: false,
    error: false,
    errorText: '',
    allowed: false,
  });

  const [genderConfigs, setGenderConfigs] = useState({
    gender: '',
    isRequired: false,
    error: false,
    errorText: '',
    allowed: false,
  });

  const [consentAgreementDateConfigs, setConsentAgreementDateConfigs] = useState({
    consentAgreementDate: null,
  });

  const [emailConfigs, setEmailConfigs] = useState({
    emailAddresses: [
      {
        emailAddress: '',
        primary: false,
      },
    ],
    primaryEmailIndex: 0,
    isRequired: false,
    error: false,
    errorText: '',
    multiple: false,
    allowed: false,
  });

  const [phoneNumberConfigs, setPhoneNumberConfigs] = useState({
    phoneNumbers: [
      {
        phoneNumber: '',
        primary: false,
      },
    ],
    primaryPhoneNumberIndex: 0,
    isRequired: false,
    error: false,
    errorText: '',
    multiple: false,
    allowed: false,
  });

  const [preferredContactMechanismConfigs, setPreferredContactMechanismConfigs] = useState({
    preferredContactMechanism: '',
    isRequired: false,
    error: false,
    errorText: '',
    allowed: false,
  });

  const [subscribeToNotificationsConfigs, setSubscribeToNotificationsConfigs] = useState({
    subscribeToNotifications: false,
    isRequired: false,
    allowed: false,
  });

  const [addressConfigs, setAddressConfigs] = useState({
    addresses: [
      {
        address1: '',
        address2: '',
        city: '',
        province: '',
        country: '',
        postalCode: '',
        primary: false,
      },
    ],
    primaryAddressIndex: 0,
    addressOneError: false,
    addressOneErrorText: '',
    addressTwoError: false,
    addressTwoErrorText: '',
    cityError: false,
    cityErrorText: '',
    countryError: false,
    countryErrorText: '',
    provinceError: false,
    provinceErrorText: '',
    postalCodeError: '',
    postalCodeErrorText: '',
    errorText: '',
    isRequired: false,
    value: '',
    provinceRequiredDueToPHN: false,
    multiple: false,
    allowed: false,
  });

  const [idConfigs, setIdConfigs] = useState({
    healthCareIds: [
      {
        type: '',
        value: '',
        primary: false,
        issuer: '',
        issuers: [],
      },
    ],
    primaryHealthCareIdIndex: 0,
    idTypeError: false,
    idValueError: false,
    issuerError: false,
    idTypeErrorText: '',
    idValueErrorText: '',
    issuerErrorText: '',
    isRequired: false,
    value: '',
    multiple: false,
    allowed: false,
  });

  useEffect(() => {
    if (orgMetaDataQuery.isSuccess && orgDataValues) {
      Object.values(orgDataValues).forEach((field) => {
        switch (field.attribute) {
          case 'FIRST_NAME':
            if (field.required !== firstNameConfigs.isRequired) {
              setFirstNameConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.allowed !== firstNameConfigs.allowed) {
              setFirstNameConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'LAST_NAME':
            if (field.required !== lastNameConfigs.isRequired) {
              setLastNameConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.allowed !== lastNameConfigs.allowed) {
              setLastNameConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'MIDDLE_NAME':
            if (field.required !== middleNameConfigs.isRequired) {
              setMiddleNameConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.allowed !== middleNameConfigs.allowed) {
              setMiddleNameConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'DATE_OF_BIRTH':
            if (field.required !== dateOfBirthConfigs.isRequired) {
              setDateOfBirthConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.allowed !== dateOfBirthConfigs.allowed) {
              setDateOfBirthConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'GENDER':
            if (field.required !== genderConfigs.isRequired) {
              setGenderConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.allowed !== genderConfigs.allowed) {
              setGenderConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'EMAIL':
            if (field.required !== emailConfigs.isRequired) {
              setEmailConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.multiple !== emailConfigs.multiple) {
              setEmailConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            if (field.allowed !== emailConfigs.allowed) {
              setEmailConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'PHONE':
            if (field.required !== phoneNumberConfigs.isRequired) {
              setPhoneNumberConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.multiple !== phoneNumberConfigs.multiple) {
              setPhoneNumberConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            if (field.allowed !== phoneNumberConfigs.allowed) {
              setPhoneNumberConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'PREFERRED_CONTACT_METHOD':
            if (field.required !== preferredContactMechanismConfigs.isRequired) {
              setPreferredContactMechanismConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.allowed !== preferredContactMechanismConfigs.allowed) {
              setPreferredContactMechanismConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'ADDRESS':
            if (field.required !== addressConfigs.isRequired) {
              setAddressConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.multiple !== addressConfigs.multiple) {
              setAddressConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            if (field.allowed !== addressConfigs.allowed) {
              setAddressConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'IDENTIFICATION':
            if (field.required !== idConfigs.isRequired) {
              setIdConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.multiple !== idConfigs.multiple) {
              setIdConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            if (field.allowed !== idConfigs.allowed) {
              setIdConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'NOTIFICATIONS':
            if (field.required !== subscribeToNotificationsConfigs.isRequired) {
              setSubscribeToNotificationsConfigs((prev) => ({
                ...prev,
                isRequired: field.required,
              }));
            }
            if (field.allowed !== subscribeToNotificationsConfigs.allowed) {
              setSubscribeToNotificationsConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          default:
            break;
        }
      });
    }
  }, [
    orgMetaDataQuery.isSuccess,
    orgDataValues,
    firstNameConfigs.isRequired,
    lastNameConfigs.isRequired,
    middleNameConfigs.isRequired,
    dateOfBirthConfigs.isRequired,
    genderConfigs.isRequired,
    emailConfigs.isRequired,
    phoneNumberConfigs.isRequired,
    preferredContactMechanismConfigs.isRequired,
    subscribeToNotificationsConfigs.isRequired,
    addressConfigs.isRequired,
    idConfigs.isRequired,
    emailConfigs.multiple,
    phoneNumberConfigs.multiple,
    addressConfigs.multiple,
    idConfigs.multiple,
    firstNameConfigs.allowed,
    lastNameConfigs.allowed,
    middleNameConfigs.allowed,
    dateOfBirthConfigs.allowed,
    genderConfigs.allowed,
    emailConfigs.allowed,
    phoneNumberConfigs.allowed,
    preferredContactMechanismConfigs.allowed,
    subscribeToNotificationsConfigs.allowed,
    addressConfigs.allowed,
    idConfigs.allowed,
  ]);

  useEffect(() => {
    if (clientId) {
      fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${clientId}`)
        .then((res) => res.json())
        .then((data) => {
          if (data === 'ERR_BAD_RESPONSE' || data === 'ERR_BAD_REQUEST') {
            throw new Error();
          }
          setExistingClientData(data);
          updateFormData(data);
        })
        .catch((fetchError) => {
          console.error('Fetch error:', fetchError);
          openSnackbar({
            variant: 'error',
            msg: 'No Client found with given data!',
          });
        });
    }
  }, [clientId]);

  function updateFormData(data) {
    const firstName = data.firstName || '';
    const middleName = data.middleName || '';
    const lastName = data.lastName || '';
    const dateOfBirth = data.dateOfBirth || null;
    const gender = data.gender || '';
    const consentAgreementDate = data.consentAgreementDate || null;
    const emailAddresses = data.emailAddresses?.length ? data.emailAddresses : [{ emailAddress: '', primary: true }];
    const phoneNumbers = data.phoneNumbers?.length ? data.phoneNumbers : [{ phoneNumber: '', primary: true }];
    const healthCareIds = data.healthCareIds?.length
      ? data.healthCareIds
      : [{ type: '', value: '', primary: true, issuer: '', issuers: [] }];
    const addresses = data.addresses?.length
      ? data.addresses
      : [{ address1: '', address2: '', city: '', province: '', country: '', postalCode: '', primary: true }];
    const subscribeToNotifications = data.subscribeToNotifications || false;
    const preferredContactMechanism = data.preferredContactMechanism || '';
    const provinceRequiredDueToPHN = healthCareIds.some((id) => id.type === 'PHN');

    setFirstNameConfigs((prevConfigs) => ({
      ...prevConfigs,
      firstName,
      isRequired: firstNameConfigs.isRequired,
    }));
    setMiddleNameConfigs((prevConfigs) => ({
      ...prevConfigs,
      middleName,
      isRequired: middleNameConfigs.isRequired,
    }));
    setLastNameConfigs((prevConfigs) => ({
      ...prevConfigs,
      lastName,
      isRequired: lastNameConfigs.isRequired,
    }));
    setDateOfBirthConfigs((prevConfigs) => ({
      ...prevConfigs,
      dateOfBirth,
      isRequired: dateOfBirthConfigs.isRequired,
    }));
    setGenderConfigs((prevConfigs) => ({
      ...prevConfigs,
      gender,
      isRequired: genderConfigs.isRequired,
    }));
    setConsentAgreementDateConfigs((prevConfigs) => ({
      ...prevConfigs,
      consentAgreementDate,
    }));
    setPhoneNumberConfigs((prevConfigs) => ({
      ...prevConfigs,
      phoneNumbers,
      primaryPhoneNumberIndex: phoneNumbers.findIndex((phone) => phone.primary) || 0,
      isRequired: phoneNumberConfigs.isRequired,
    }));
    setEmailConfigs((prevConfigs) => ({
      ...prevConfigs,
      emailAddresses,
      primaryEmailIndex: emailAddresses.findIndex((email) => email.primary) || 0,
      isRequired: emailConfigs.isRequired,
    }));
    setIdConfigs((prevConfigs) => ({
      ...prevConfigs,
      healthCareIds,
      primaryHealthCareIdIndex: healthCareIds.findIndex((id) => id.primary) || 0,
      isRequired: idConfigs.isRequired,
    }));
    setPreferredContactMechanismConfigs((prevConfigs) => ({
      ...prevConfigs,
      preferredContactMechanism,
      isRequired: preferredContactMechanismConfigs.isRequired,
    }));
    setSubscribeToNotificationsConfigs((prevConfigs) => ({
      ...prevConfigs,
      subscribeToNotifications,
      isRequired: subscribeToNotificationsConfigs.isRequired,
    }));
    setAddressConfigs((prevConfigs) => ({
      ...prevConfigs,
      addresses,
      primaryAddressIndex: addresses.findIndex((address) => address.primary) || 0,
      isRequired: addressConfigs.isRequired,
      provinceRequiredDueToPHN,
    }));
  }

  function handleFirstNameChange(value) {
    setFirstNameConfigs((prevConfigs) => ({
      ...prevConfigs,
      firstName: value,
    }));
  }

  function handleMiddleNameChange(value) {
    setMiddleNameConfigs((prevConfigs) => ({
      ...prevConfigs,
      middleName: value,
    }));
  }

  function handleLastNameChange(value) {
    setLastNameConfigs((prevConfigs) => ({
      ...prevConfigs,
      lastName: value,
    }));
  }

  function handleGenderChange(value) {
    setGenderConfigs((prevConfigs) => ({
      ...prevConfigs,
      gender: value,
    }));
  }

  function handleDateOfBirthChange(value) {
    setDateOfBirthConfigs((prevConfigs) => ({
      ...prevConfigs,
      dateOfBirth: value,
    }));
  }

  function handleEmailChange({ value, validationError, validationHelperText }, index) {
    setEmailConfigs((prevConfigs) => {
      const updatedEmails = [...prevConfigs.emailAddresses];
      const isRequired = index === 0 && prevConfigs.isRequired;
      const requiredError = isRequired && (!value || value.trim() === '');

      updatedEmails[index] = {
        ...updatedEmails[index],
        emailAddress: value,
        validationError: requiredError || validationError,
        validationErrorText: requiredError ? 'Please enter a value.' : validationHelperText,
      };
      return {
        ...prevConfigs,
        emailAddresses: updatedEmails,
        error: updatedEmails.some((email) => email.validationError),
      };
    });
  }

  function handleAddressChange(index, field, value) {
    setAddressConfigs((prevConfigs) => {
      const updatedAddresses = [...prevConfigs.addresses];
      const updatedAddress = { ...updatedAddresses[index], [field]: value };
      if (field === 'country') {
        const previousProvince = updatedAddresses[index].province;
        const availableProvinces = countriesAndProvinces[value] || [];
        if (countriesAndProvinces[value] && !availableProvinces.includes(previousProvince)) {
          updatedAddress.province = '';
        }
      }
      updatedAddresses[index] = updatedAddress;

      const newPrimaryIndex = updatedAddresses.findIndex((address) => address.primary);
      if (updatedAddresses.length === 1 && newPrimaryIndex === -1) {
        updatedAddresses[0].primary = true;
      }

      return {
        ...prevConfigs,
        addresses: updatedAddresses,
      };
    });
  }

  function handlePhoneNumberChange({ value, validationError, validationHelperText }, index) {
    setPhoneNumberConfigs((prevConfigs) => {
      const updatedPhoneNumbers = [...prevConfigs.phoneNumbers];
      const isRequired = index === 0 && prevConfigs.isRequired;
      const requiredError = isRequired && (!value || value.trim() === '' || value.trim() === '+');

      updatedPhoneNumbers[index] = {
        ...updatedPhoneNumbers[index],
        phoneNumber: value,
        validationError: requiredError || validationError,
        validationErrorText: requiredError ? 'Please enter a value.' : validationHelperText,
      };
      return {
        ...prevConfigs,
        phoneNumbers: updatedPhoneNumbers,
        error: updatedPhoneNumbers.some((phone) => phone.validationError),
      };
    });
  }

  function handlePreferredContactMechanismChange(value) {
    setPreferredContactMechanismConfigs((prevConfigs) => ({
      ...prevConfigs,
      preferredContactMechanism: value,
    }));
  }

  function handleSubscribeToNotificationsChange(value) {
    setSubscribeToNotificationsConfigs((prevConfigs) => ({
      ...prevConfigs,
      subscribeToNotifications: value,
    }));
  }

  function handleIdChange(index, field, value) {
    setIdConfigs((prevConfigs) => {
      const updatedHealthCareIds = [...prevConfigs.healthCareIds];
      const currentIdEntry = updatedHealthCareIds[index];
      updatedHealthCareIds[index] = { ...currentIdEntry, [field]: value };

      if (field === 'type' && !value) {
        updatedHealthCareIds[index] = {
          ...updatedHealthCareIds[index],
          issuer: '',
        };
      }

      const allFieldsEmpty =
        (!updatedHealthCareIds[index].type || updatedHealthCareIds[index].type.trim() === '') &&
        (!updatedHealthCareIds[index].issuer || updatedHealthCareIds[index].issuer.trim() === '') &&
        (!updatedHealthCareIds[index].value || updatedHealthCareIds[index].value.trim() === '');

      const updatedErrors = {
        idTypeError:
          healthcareError && allFieldsEmpty && !(index === 0 && idConfigs?.isRequired)
            ? false
            : prevConfigs.idTypeError,
        idTypeErrorText:
          healthcareError && allFieldsEmpty && !(index === 0 && idConfigs?.isRequired)
            ? ''
            : prevConfigs.idTypeErrorText,
        issuerError:
          healthcareError && allFieldsEmpty && !(index === 0 && idConfigs?.isRequired)
            ? false
            : prevConfigs.issuerError,
        issuerErrorText:
          healthcareError && allFieldsEmpty && !(index === 0 && idConfigs?.isRequired)
            ? ''
            : prevConfigs.issuerErrorText,
        idValueError:
          healthcareError && allFieldsEmpty && !(index === 0 && idConfigs?.isRequired)
            ? false
            : prevConfigs.idValueError,
        idValueErrorText:
          healthcareError && allFieldsEmpty && !(index === 0 && idConfigs?.isRequired)
            ? ''
            : prevConfigs.idValueErrorText,
      };

      if (idConfigs.isRequired && idConfigs.healthCareIds.length > 0) {
        const firstId = idConfigs.healthCareIds[0];
        if (healthcareError && firstId.type?.trim() === '') {
          updatedErrors.idTypeError = true;
          updatedErrors.idTypeErrorText = 'Type is required.';
        }
        if (healthcareError && firstId.value?.trim() === '') {
          updatedErrors.idValueError = true;
          updatedErrors.idValueErrorText = 'Value is required.';
        }
        if (healthcareError && firstId.issuer?.trim() === '') {
          updatedErrors.issuerError = true;
          updatedErrors.issuerErrorText = 'Issuer is required.';
        }
      }

      if (field === 'type') {
        const selectedType = healthcareValues.find((type) => type.idType === value);
        const newIssuers = selectedType ? selectedType.issuers.map((issuer) => issuer.displayName) : [];
        const updatedIssuer = newIssuers.includes(updatedHealthCareIds[index].issuer)
          ? updatedHealthCareIds[index].issuer
          : '';

        updatedHealthCareIds[index] = {
          ...updatedHealthCareIds[index],
          issuer: updatedIssuer,
          issuers: newIssuers,
        };

        if (value === 'PHN') {
          if (addressConfigs?.addresses[0]?.country === '') {
            setAddressConfigs((prevConfigs) => ({
              ...prevConfigs,
              addresses: prevConfigs.addresses.map((addr) => ({
                ...addr,
                country: 'Canada',
              })),
            }));
          }
          setAddressConfigs((prevConfigs) => ({
            ...prevConfigs,
            isRequiredDueToPHN: true,
          }));
        } else {
          setAddressConfigs((prevConfigs) => ({
            ...prevConfigs,
            isRequiredDueToPHN: false,
            error: false,
          }));
        }
      }

      const requiredIssuers =
        healthcareValues
          .find((type) => type.idType === updatedHealthCareIds[index].type)
          ?.issuers.filter((issuer) => issuer.required)
          .map((issuer) => issuer.issuer) || [];

      const selectedIssuers = updatedHealthCareIds.map((idEntry) => idEntry.issuer).filter(Boolean);

      const missingRequiredIssuers = requiredIssuers
        .filter((requiredIssuer) => !selectedIssuers.includes(requiredIssuer))
        .map((issuerCode) => {
          const issuer = healthcareValues
            .find((type) => type.idType === updatedHealthCareIds[index].type)
            ?.issuers.find((i) => i.issuer === issuerCode);
          return issuer ? issuer.displayName : issuerCode;
        });

      if (issuerRequiredError && missingRequiredIssuers.length > 0) {
        prevConfigs.issuerRequiredError = true;
        prevConfigs.issuerRequiredErrorText = `Issuer(s) ${missingRequiredIssuers.join(', ')} are required for Type "${updatedHealthCareIds[index].type}".`;
      } else {
        prevConfigs.issuerRequiredError = false;
        prevConfigs.issuerRequiredErrorText = '';
      }

      updatedHealthCareIds.forEach((healthCareId) => {
        if (healthcareError && healthCareId.type?.trim() === '' && (healthCareId.value || healthCareId.issuer)) {
          updatedErrors.idTypeError = true;
          updatedErrors.idTypeErrorText = 'Type is required.';
        }
        if (healthcareError && healthCareId.issuer?.trim() === '' && (healthCareId.value || healthCareId.type)) {
          updatedErrors.issuerError = true;
          updatedErrors.issuerErrorText = 'Issuer is required.';
        }
        if (healthcareError && healthCareId.value?.trim() === '' && (healthCareId.type || healthCareId.issuer)) {
          updatedErrors.idValueError = true;
          updatedErrors.idValueErrorText = 'Value is required.';
        }
      });

      return {
        ...prevConfigs,
        ...updatedErrors,
        healthCareIds: updatedHealthCareIds,
      };
    });
  }

  function handlePrimaryEmailChange(index) {
    setEmailConfigs((prevConfigs) => ({
      ...prevConfigs,
      primaryEmailIndex: index,
    }));
  }

  function handlePrimaryPhoneNumberChange(index) {
    setPhoneNumberConfigs((prevConfigs) => ({
      ...prevConfigs,
      primaryPhoneNumberIndex: index,
    }));
  }

  function handlePrimaryAddressChange(index) {
    setAddressConfigs((prevConfigs) => {
      const updatedAddresses = [...prevConfigs.addresses];
      updatedAddresses.forEach((address, i) => (address.primary = i === index));
      return {
        ...prevConfigs,
        addresses: updatedAddresses,
        primaryAddressIndex: index,
      };
    });
  }

  function handlePrimaryHealthCareIdChange(index) {
    setIdConfigs((prevConfigs) => {
      const updatedHealthCareIds = [...prevConfigs.healthCareIds];
      updatedHealthCareIds.forEach((id, i) => (id.primary = i === index));

      return {
        ...prevConfigs,
        healthCareIds: updatedHealthCareIds,
        primaryHealthCareIdIndex: index,
      };
    });
  }

  function handleAddEmail() {
    setEmailConfigs((prevConfigs) => {
      const newEmails = [...prevConfigs.emailAddresses, { emailAddress: '', primary: false }];

      if (newEmails.length === 1) {
        newEmails[0].primary = true;
      }

      return {
        ...prevConfigs,
        emailAddresses: newEmails,
        primaryEmailIndex: newEmails.length === 1 ? 0 : prevConfigs.primaryEmailIndex,
      };
    });
  }

  function handleAddPhoneNumber() {
    setPhoneNumberConfigs((prevConfigs) => {
      const newPhoneNumbers = [...prevConfigs.phoneNumbers, { phoneNumber: '', primary: false }];

      if (newPhoneNumbers.length === 1) {
        newPhoneNumbers[0].primary = true;
      }

      return {
        ...prevConfigs,
        phoneNumbers: newPhoneNumbers,
        primaryPhoneNumberIndex: newPhoneNumbers.length === 1 ? 0 : prevConfigs.primaryPhoneNumberIndex,
      };
    });
  }

  const handleAddAddress = () => {
    setAddressConfigs((prevConfigs) => {
      const updatedAddresses = [
        ...prevConfigs.addresses,
        {
          address1: '',
          address2: '',
          city: '',
          province: '',
          country: '',
          postalCode: '',
          primary: false,
        },
      ];

      if (updatedAddresses.length === 1) {
        updatedAddresses[0].primary = true;
      }

      return {
        ...prevConfigs,
        addresses: updatedAddresses,
        primaryAddressIndex: updatedAddresses.length === 1 ? 0 : prevConfigs.primaryAddressIndex,
      };
    });
  };

  const handleAddHealthCareId = () => {
    setIdConfigs((prevConfigs) => {
      const updatedHealthCareIds = [
        ...prevConfigs.healthCareIds,
        {
          type: '',
          value: '',
          issuer: '',
          issuers: [],
          primary: false,
        },
      ];
      if (updatedHealthCareIds.length === 1) {
        updatedHealthCareIds[0].primary = true;
      }
      return {
        ...prevConfigs,
        healthCareIds: updatedHealthCareIds,
        primaryHealthCareIdIndex: updatedHealthCareIds.length === 1 ? 0 : prevConfigs.primaryHealthCareIdIndex,
      };
    });
  };

  function handleRemoveEmail(index) {
    setEmailConfigs((prevConfigs) => {
      const updatedEmails = prevConfigs.emailAddresses.filter((_, i) => i !== index);
      let newPrimaryEmailIndex = prevConfigs.primaryEmailIndex;
      if (!prevConfigs.multiple) {
        if (index === prevConfigs.primaryEmailIndex) {
          return {
            ...prevConfigs,
            emailAddresses: [],
            primaryEmailIndex: -1,
          };
        }
      }
      if (index === prevConfigs.primaryEmailIndex) {
        if (updatedEmails.length > 0) {
          updatedEmails[0].primary = true;
          newPrimaryEmailIndex = 0;
        } else {
          newPrimaryEmailIndex = -1;
        }
      } else if (index < prevConfigs.primaryEmailIndex) {
        newPrimaryEmailIndex -= 1;
      }

      return {
        ...prevConfigs,
        emailAddresses: updatedEmails,
        primaryEmailIndex: newPrimaryEmailIndex,
      };
    });
  }

  function handleRemovePhoneNumber(index) {
    setPhoneNumberConfigs((prevConfigs) => {
      const updatedPhoneNumbers = prevConfigs.phoneNumbers.filter((_, i) => i !== index);
      let newPrimaryPhoneNumberIndex = prevConfigs.primaryPhoneNumberIndex;
      if (!prevConfigs.multiple) {
        if (index === prevConfigs.primaryPhoneNumberIndex) {
          return {
            ...prevConfigs,
            phoneNumbers: [],
            primaryPhoneNumberIndex: -1,
          };
        }
      }
      if (index === prevConfigs.primaryPhoneNumberIndex) {
        if (updatedPhoneNumbers.length > 0) {
          updatedPhoneNumbers[0].primary = true;
          newPrimaryPhoneNumberIndex = 0;
        } else {
          newPrimaryPhoneNumberIndex = -1;
        }
      } else if (index < prevConfigs.primaryPhoneNumberIndex) {
        newPrimaryPhoneNumberIndex -= 1;
      }

      return {
        ...prevConfigs,
        phoneNumbers: updatedPhoneNumbers,
        primaryPhoneNumberIndex: newPrimaryPhoneNumberIndex,
      };
    });
  }

  const handleRemoveAddress = (index) => {
    setAddressConfigs((prevConfigs) => {
      const updatedAddresses = prevConfigs.addresses.filter((_, i) => i !== index);
      let newPrimaryAddressIndex = prevConfigs.primaryAddressIndex;
      if (!prevConfigs.multiple) {
        if (index === prevConfigs.primaryAddressIndex) {
          return {
            ...prevConfigs,
            addresses: [],
            primaryAddressIndex: -1,
          };
        }
      }
      if (index === prevConfigs.primaryAddressIndex) {
        if (updatedAddresses.length > 0) {
          updatedAddresses[0].primary = true;
          newPrimaryAddressIndex = 0;
        } else {
          newPrimaryAddressIndex = -1;
        }
      } else if (index < prevConfigs.primaryAddressIndex) {
        newPrimaryAddressIndex -= 1;
      }

      return {
        ...prevConfigs,
        addresses: updatedAddresses,
        primaryAddressIndex: newPrimaryAddressIndex,
      };
    });
  };

  const handleRemoveHealthCareId = (index) => {
    setIdConfigs((prevConfigs) => {
      const updatedHealthCareIds = prevConfigs.healthCareIds.filter((_, i) => i !== index);
      let newPrimaryHealthCareIdIndex = prevConfigs.primaryHealthCareIdIndex;

      const updatedHealthCareIdsWithNoErrors = updatedHealthCareIds.map((id) => ({
        ...id,
        idTypeError: false,
        idTypeErrorText: '',
        issuerError: false,
        issuerErrorText: '',
        idValueError: false,
        idValueErrorText: '',
      }));

      if (!prevConfigs.multiple) {
        if (index === prevConfigs.primaryHealthCareIdIndex) {
          return {
            ...prevConfigs,
            healthCareIds: [],
            primaryHealthCareIdIndex: -1,
          };
        }
      }
      if (index === prevConfigs.primaryHealthCareIdIndex) {
        if (updatedHealthCareIds.length > 0) {
          updatedHealthCareIds[0].primary = true;
          newPrimaryHealthCareIdIndex = 0;
        } else {
          newPrimaryHealthCareIdIndex = -1;
        }
      } else if (index < prevConfigs.primaryHealthCareIdIndex) {
        newPrimaryHealthCareIdIndex -= 1;
      }

      return {
        ...prevConfigs,
        healthCareIds: updatedHealthCareIdsWithNoErrors,
        primaryHealthCareIdIndex: newPrimaryHealthCareIdIndex,
      };
    });
  };

  const handleFormCancellation = () => {
    if (props.closeEditMode) {
      props.closeEditMode();
    } else {
      router.back();
    }
  };

  function setValidationErrors(setConfigFunction, error, errorText) {
    setConfigFunction((prevConfigs) => ({
      ...prevConfigs,
      error,
      errorText,
    }));
  }

  function setIdValidationErrors(
    setConfigFunction,
    idTypeError,
    idTypeErrorText,
    idValueError,
    idValueErrorText,
    issuerError,
    issuerErrorText,
    issuerRequiredError,
    issuerRequiredErrorText,
  ) {
    setConfigFunction((prevConfigs) => ({
      ...prevConfigs,
      idTypeError,
      idTypeErrorText,
      idValueError,
      idValueErrorText,
      issuerError,
      issuerErrorText,
      issuerRequiredError,
      issuerRequiredErrorText,
    }));
  }

  function setAddressValidationErrors(
    setConfigFunction,
    addressOneError,
    addressOneErrorText,
    addressTwoError,
    addressTwoErrorText,
    cityError,
    cityErrorText,
    countryError,
    countryErrorText,
    provinceError,
    provinceErrorText,
    postalCodeError,
    postalCodeErrorText,
  ) {
    setConfigFunction((prevConfigs) => ({
      ...prevConfigs,
      addressOneError,
      addressOneErrorText,
      addressTwoError,
      addressTwoErrorText,
      cityError,
      cityErrorText,
      countryError,
      countryErrorText,
      provinceError,
      provinceErrorText,
      postalCodeError,
      postalCodeErrorText,
    }));
  }

  function validateForm() {
    setIssuerRequiredError(true);
    setHealthcareError(true);
    let isValid = true;
    let errors = {
      firstNameError: false,
      firstNameErrorText: '',
      middleNameError: false,
      middleNameErrorText: '',
      lastNameError: false,
      lastNameErrorText: '',
      genderError: false,
      genderErrorText: '',
      dateOfBirthError: false,
      dateOfBirthErrorText: '',
      emailError: false,
      emailErrorText: '',
      phoneNumberError: false,
      phoneNumberErrorText: '',
      idTypeError: false,
      idTypeErrorText: '',
      idValueError: false,
      idValueErrorText: '',
      issuerError: false,
      issuerErrorText: '',
      issuerRequiredError: false,
      issuerRequiredErrorText: '',
      addressOneError: false,
      addressOneErrorText: '',
      addressTwoError: false,
      addressTwoErrorText: '',
      cityError: false,
      cityErrorText: '',
      countryError: false,
      countryErrorText: '',
      provinceError: false,
      provinceErrorText: '',
      postalCodeError: false,
      postalCodeErrorText: '',
      preferredContactMechanismError: false,
      preferredContactMechanismErrorText: '',
    };

    if (firstNameConfigs.isRequired && !firstNameConfigs.firstName) {
      isValid = false;
      errors.firstNameError = true;
      errors.firstNameErrorText = `First name is required.`;
    }

    if (middleNameConfigs.isRequired && !middleNameConfigs.middleName) {
      isValid = false;
      errors.middleNameError = true;
      errors.middleNameErrorText = `Middle name is required.`;
    }

    if (lastNameConfigs.isRequired && !lastNameConfigs.lastName) {
      isValid = false;
      errors.lastNameError = true;
      errors.lastNameErrorText = `Last name is required.`;
    }

    if (genderConfigs.isRequired && !genderConfigs.gender) {
      isValid = false;
      errors.genderError = true;
      errors.genderErrorText = `Gender is required.`;
    }

    if (dateOfBirthConfigs.isRequired && !dateOfBirthConfigs.dateOfBirth) {
      isValid = false;
      errors.dateOfBirthError = true;
      errors.dateOfBirthErrorText = `Date of Birth is required.`;
    }

    if (emailConfigs.isRequired) {
      if (
        !emailConfigs.emailAddresses ||
        emailConfigs.emailAddresses.length === 0 ||
        !emailConfigs.emailAddresses[0].emailAddress?.trim()
      ) {
        isValid = false;
        errors.emailError = true;
        errors.emailErrorText = `Email is required.`;
      }
    }

    if (emailConfigs.allowed && emailConfigs.emailAddresses.length > 0) {
      const hasValidationError = emailConfigs.emailAddresses.some((num) => num.validationError);
      if (hasValidationError) {
        isValid = false;
      }
    }

    if (phoneNumberConfigs.isRequired) {
      if (
        !phoneNumberConfigs.phoneNumbers ||
        phoneNumberConfigs.phoneNumbers.length === 0 ||
        !phoneNumberConfigs.phoneNumbers[0].phoneNumber?.trim()
      ) {
        isValid = false;
        errors.phoneNumberError = true;
        errors.phoneNumberErrorText = `Phone is required.`;
      }
    }

    if (phoneNumberConfigs.allowed && phoneNumberConfigs.phoneNumbers.length > 0) {
      const hasValidationError = phoneNumberConfigs.phoneNumbers.some((num) => num.validationError);
      if (hasValidationError) {
        isValid = false;
      }
    }

    if (idConfigs.isRequired && idConfigs.healthCareIds.length > 0) {
      const firstId = idConfigs.healthCareIds[0];
      if (firstId.type?.trim() === '') {
        isValid = false;
        errors.idTypeError = true;
        errors.idTypeErrorText = 'Type is required.';
      }
      if (firstId.value?.trim() === '') {
        isValid = false;
        errors.idValueError = true;
        errors.idValueErrorText = 'Value is required.';
      }
      if (firstId.issuer?.trim() === '') {
        isValid = false;
        errors.issuerError = true;
        errors.issuerErrorText = 'Issuer is required.';
      }
    }

    if (idConfigs.healthCareIds.length > 0) {
      idConfigs.healthCareIds.forEach((healthCareId) => {
        if (healthCareId.type?.trim() === '' && (healthCareId.value || healthCareId.issuer)) {
          isValid = false;
          errors.idTypeError = true;
          errors.idTypeErrorText = 'Type is required.';
        }
        if (healthCareId.issuer?.trim() === '' && (healthCareId.value || healthCareId.type)) {
          isValid = false;
          errors.issuerError = true;
          errors.issuerErrorText = 'Issuer is required.';
        }
        if (healthCareId.value?.trim() === '' && (healthCareId.type || healthCareId.issuer)) {
          isValid = false;
          errors.idValueError = true;
          errors.idValueErrorText = 'Value is required.';
        }
      });
    }

    const selectedIssuersByType = {};
    idConfigs.healthCareIds.forEach((healthCareId) => {
      const selectedType = healthcareValues.find((type) => type.idType === healthCareId.type);

      if (selectedType) {
        if (!selectedIssuersByType[healthCareId.type]) {
          selectedIssuersByType[healthCareId.type] = [];
        }
        if (healthCareId.issuer) {
          const issuerDisplayName = selectedType.issuers.find(
            (issuer) => issuer.issuer === healthCareId.issuer,
          )?.displayName;
          if (issuerDisplayName && !selectedIssuersByType[healthCareId.type].includes(issuerDisplayName)) {
            selectedIssuersByType[healthCareId.type].push(issuerDisplayName);
          }
        }
      }
    });

    healthcareValues.forEach((type) => {
      const requiredIssuers = type.issuers.filter((issuer) => issuer.required).map((issuer) => issuer.displayName);

      if (requiredIssuers.length > 0) {
        const selectedIssuers = selectedIssuersByType[type.idType] || [];
        const missingRequiredIssuers = requiredIssuers.filter(
          (requiredIssuer) => !selectedIssuers.includes(requiredIssuer),
        );
        if (missingRequiredIssuers.length > 0) {
          isValid = false;
          errors.issuerRequiredError = true;
          errors.issuerRequiredErrorText = `Issuer(s) ${missingRequiredIssuers.join(', ')} are required for Type "${type.idType}".`;
        }
      }
    });

    if (addressConfigs.isRequired && addressConfigs.addresses.length > 0) {
      const firstAddress = addressConfigs.addresses[0];
      if (!firstAddress.address1?.trim()) {
        isValid = false;
        errors.addressOneError = true;
        errors.addressOneErrorText = 'Line 1 is required.';
      }
      if (!firstAddress.city?.trim()) {
        isValid = false;
        errors.cityError = true;
        errors.cityErrorText = 'City is required.';
      }
      if (!firstAddress.country?.trim()) {
        isValid = false;
        errors.countryError = true;
        errors.countryErrorText = 'Country is required.';
      }
      if (!firstAddress.province?.trim()) {
        isValid = false;
        errors.provinceError = true;
        errors.provinceErrorText = 'Province is required.';
      }
      if (!firstAddress.postalCode?.trim()) {
        isValid = false;
        errors.postalCodeError = true;
        errors.postalCodeErrorText = 'Postal Code is required.';
      }
    }

    if (preferredContactMechanismConfigs.isRequired && !preferredContactMechanismConfigs.preferredContactMechanism) {
      isValid = false;
      errors.preferredContactMechanismError = true;
      errors.preferredContactMechanismErrorText = `Preferred Contact Method is required.`;
    }

    return {
      isValid,
      ...errors,
    };
  }

  const updateClient = async (clientId, clientData) => {
    try {
      const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${clientId}`, {
        method: 'PUT',
        body: JSON.stringify(clientData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update client: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating client:', error);
      throw error;
    }
  };

  const addClient = async (clientData) => {
    try {
      const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients`, {
        method: 'POST',
        body: JSON.stringify(clientData),
      });

      if (!response.ok) {
        throw new Error(`Failed to add client: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error adding client:', error);
      throw error;
    }
  };

  const submissionHandler = async () => {
    const {
      isValid,
      firstNameError,
      firstNameErrorText,
      middleNameError,
      middleNameErrorText,
      lastNameError,
      lastNameErrorText,
      genderError,
      genderErrorText,
      dateOfBirthError,
      dateOfBirthErrorText,
      emailError,
      emailErrorText,
      phoneNumberError,
      phoneNumberErrorText,
      idTypeError,
      idTypeErrorText,
      idValueError,
      idValueErrorText,
      issuerError,
      issuerErrorText,
      issuerRequiredError,
      issuerRequiredErrorText,
      addressOneError,
      addressOneErrorText,
      addressTwoError,
      addressTwoErrorText,
      cityError,
      cityErrorText,
      countryError,
      countryErrorText,
      provinceError,
      provinceErrorText,
      postalCodeError,
      postalCodeErrorText,
      preferredContactMechanismError,
      preferredContactMechanismErrorText,
    } = validateForm();

    if (!isValid) {
      setValidationErrors(setFirstNameConfigs, firstNameError, firstNameErrorText);
      setValidationErrors(setMiddleNameConfigs, middleNameError, middleNameErrorText);
      setValidationErrors(setLastNameConfigs, lastNameError, lastNameErrorText);
      setValidationErrors(setGenderConfigs, genderError, genderErrorText);
      setValidationErrors(setDateOfBirthConfigs, dateOfBirthError, dateOfBirthErrorText);
      setValidationErrors(setEmailConfigs, emailError, emailErrorText);
      setValidationErrors(setPhoneNumberConfigs, phoneNumberError, phoneNumberErrorText);
      setValidationErrors(
        setPreferredContactMechanismConfigs,
        preferredContactMechanismError,
        preferredContactMechanismErrorText,
      );
      setIdValidationErrors(
        setIdConfigs,
        idTypeError,
        idTypeErrorText,
        idValueError,
        idValueErrorText,
        issuerError,
        issuerErrorText,
        issuerRequiredError,
        issuerRequiredErrorText,
      );
      setAddressValidationErrors(
        setAddressConfigs,
        addressOneError,
        addressOneErrorText,
        addressTwoError,
        addressTwoErrorText,
        cityError,
        cityErrorText,
        countryError,
        countryErrorText,
        provinceError,
        provinceErrorText,
        postalCodeError,
        postalCodeErrorText,
      );

      return;
    }

    const filterAndMapPrimary = (configs, fieldName, primaryIndex, fieldsToCheck = []) => {
      if (!configs?.allowed || !configs?.[fieldName]) return [];
      const items = configs.multiple
        ? (configs[fieldName] ?? [])
        : configs[fieldName]?.[primaryIndex]
          ? [configs[fieldName][primaryIndex]]
          : [];
      return items
        .filter((item) => {
          return (
            fieldsToCheck.length === 0 ||
            fieldsToCheck.some((field) => {
              const value = item[field];
              return value !== null && value !== undefined && value.toString().trim() !== '';
            })
          );
        })
        .map((item, index) => ({
          ...item,
          primary: index === primaryIndex,
        }))
        .sort((a, b) => b.primary - a.primary);
    };

    const data = {
      firstName: firstNameConfigs?.allowed ? firstNameConfigs.firstName : null,
      middleName: middleNameConfigs?.allowed ? middleNameConfigs.middleName : null,
      lastName: lastNameConfigs?.allowed ? lastNameConfigs.lastName : null,
      gender: genderConfigs?.allowed ? genderConfigs.gender : null,
      dateOfBirth: dateOfBirthConfigs?.allowed ? dateOfBirthConfigs.dateOfBirth : null,
      consentAgreementDate: consentAgreementDateConfigs.consentAgreementDate,
      preferredContactMechanism: preferredContactMechanismConfigs?.allowed
        ? preferredContactMechanismConfigs.preferredContactMechanism
        : null,
      subscribeToNotifications: subscribeToNotificationsConfigs?.allowed
        ? subscribeToNotificationsConfigs.subscribeToNotifications
        : null,
      emailAddresses: filterAndMapPrimary(emailConfigs, 'emailAddresses', emailConfigs.primaryEmailIndex, [
        'emailAddress',
      ]),
      phoneNumbers: filterAndMapPrimary(
        phoneNumberConfigs,
        'phoneNumbers',
        phoneNumberConfigs.primaryPhoneNumberIndex,
        ['phoneNumber'],
      ),
      addresses: filterAndMapPrimary(addressConfigs, 'addresses', addressConfigs.primaryAddressIndex, [
        'address1',
        'address2',
        'city',
        'province',
        'country',
        'postalCode',
      ]),
      healthCareIds: filterAndMapPrimary(idConfigs, 'healthCareIds', idConfigs.primaryHealthCareIdIndex, [
        'type',
        'value',
        'issuer',
      ]),
      ...(!clientId && { createdBy: userId ? `organization_user_id/${userId}` : 'COORDINATOR' }),
      ...(clientId && { updatedBy: userId ? `organization_user_id/${userId}` : 'COORDINATOR' }),
    };
    if (!clientId) {
      data.dataSource = 'COORDINATOR';
    } else {
      data.dataSource = existingClientData?.dataSource || 'COORDINATOR';
    }
    setSaving(true);

    try {
      const apiResponse = clientId ? await updateClient(clientId, data) : await addClient(data);
      if (!apiResponse) throw new Error('No response from server');
      if (apiResponse.errorName || apiResponse.error) {
        const errorMsg = apiResponse.message || 'Failed to add/update client';
        console.error('Response error:', errorMsg);
        openSnackbar({ variant: 'error', msg: errorMsg });
        setSaving(false);
        return;
      }

      console.log(clientId ? 'The client was successfully saved' : 'Client Added Successfully!');
      setSaving(false);

      if (!clientId) {
        props.closeEditMode ? props.closeEditMode() : setTimeout(() => router.back(), 3000);
        return;
      }

      fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${clientId}`)
        .then((res) => res.json())
        .then((data) => {
          if (data === 'ERR_BAD_RESPONSE' || data === 'ERR_BAD_REQUEST') throw new Error('Bad response or bad request');
          updateFormData(data);
          props.closeEditMode ? props.closeEditMode() : router.back();
        })
        .catch((fetchError) => {
          console.error('Fetch error:', fetchError);
          openSnackbar({ variant: 'error', msg: 'No client with given data found!' });
        });
    } catch (error) {
      console.error('Response error:', error);
      setSaving(false);
      openSnackbar({ variant: 'error', msg: 'An error occurred. Please try again!' });
    }
  };

  const renderCoordinatorNewClient = () => (
    <CoordinatorNewClient
      submitBtnText="Save"
      cancelBtnText="Cancel"
      saveBtnClicked={saving}
      emailConfigs={emailConfigs}
      handleEmailChange={handleEmailChange}
      handlePrimaryEmailChange={handlePrimaryEmailChange}
      handleAddEmail={handleAddEmail}
      handleRemoveEmail={handleRemoveEmail}
      submissionHandler={submissionHandler}
      handleAddressChange={handleAddressChange}
      addressConfigs={addressConfigs}
      handleAddAddress={handleAddAddress}
      handleRemoveAddress={handleRemoveAddress}
      handlePrimaryAddressChange={handlePrimaryAddressChange}
      countriesAndProvincesList={countriesAndProvinces}
      phoneNumberConfigs={phoneNumberConfigs}
      handlePhoneNumberChange={handlePhoneNumberChange}
      handlePrimaryPhoneNumberChange={handlePrimaryPhoneNumberChange}
      handleAddPhoneNumber={handleAddPhoneNumber}
      handleRemovePhoneNumber={handleRemovePhoneNumber}
      handleIdChange={handleIdChange}
      idConfigs={idConfigs}
      handleAddHealthCareId={handleAddHealthCareId}
      handleRemoveHealthCareId={handleRemoveHealthCareId}
      handlePrimaryHealthCareIdChange={handlePrimaryHealthCareIdChange}
      healthcareValues={healthcareValues}
      handleFormCancellation={handleFormCancellation}
      firstNameConfigs={firstNameConfigs}
      middleNameConfigs={middleNameConfigs}
      lastNameConfigs={lastNameConfigs}
      genderConfigs={genderConfigs}
      dateOfBirthConfigs={dateOfBirthConfigs}
      preferredContactMechanismConfigs={preferredContactMechanismConfigs}
      handlePreferredContactMechanismChange={handlePreferredContactMechanismChange}
      subscribeToNotificationsConfigs={subscribeToNotificationsConfigs}
      handleSubscribeToNotificationsChange={handleSubscribeToNotificationsChange}
      handleFirstNameChange={handleFirstNameChange}
      handleMiddleNameChange={handleMiddleNameChange}
      handleLastNameChange={handleLastNameChange}
      handleDateOfBirthChange={handleDateOfBirthChange}
      handleGenderChange={handleGenderChange}
    />
  );

  return (
    <>
      {!clientId && (
        <HeaderStyle>
          <Typography variant="h4">Add Client</Typography>
        </HeaderStyle>
      )}
      <Grid container sx={{ mt: 2, mb: 2 }}>
        <Grid item xs={12} sm={12}>
          {clientId ? (
            renderCoordinatorNewClient()
          ) : (
            <Card elevation={3} sx={{ p: 2 }}>
              <CardContent>{renderCoordinatorNewClient()}</CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </>
  );
}

export default CreateAndEditClient;
