import { getArtifact } from '@/lib/api/artifactRepository';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID, PRIVATE, QUESTIONNAIRES } from '@/lib/constant';

const questionnaire = async (
  row,
  identifier,
  setDocGenLoading,
  displayErrorMessage,
  t,
  setSelectedQuestionnaireResponse,
  setSelectedQuestionnaire,
  setDocGenerated,
  router,
) => {
  let formData = {};
  setDocGenLoading(true);

  if (identifier) {
    try {
      const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${identifier}`);
      const dataResponse = await response.json();
      const data = dataResponse;
      formData.client_data = data;
    } catch (err) {
      console.error('Error fetching client info:', err);
      setDocGenLoading(false);
      displayErrorMessage(t('Error : Could not fetch client info'));
      return;
    }
  } else {
    setDocGenLoading(false);
    displayErrorMessage(t('Error : Patient does not have client identifier'));
    return;
  }

  try {
    const response = await fetchNextRoute(
      'organizationCDR',
      `/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse/${row.resource.id}`,
    );
    const dataResponse = await response.json();
    const data = dataResponse.dataResponse;
    formData.questionnaire_response_data = data;
    setSelectedQuestionnaireResponse(data);
  } catch (err) {
    console.error('Error fetching questionnaire response details:', err);
    setDocGenLoading(false);
    displayErrorMessage(t('Error : Could not fetch questionnaire response details'));
    return;
  }

  let questionnaireData;
  try {
    questionnaireData = await getArtifact({
      artifactType: QUESTIONNAIRES,
      artifactId: row.resource.questionnaire,
      visibility: PRIVATE,
    });
    formData.binary_data = questionnaireData.pdfTemplate;
    setSelectedQuestionnaire(questionnaireData.questionnaire);
  } catch (err) {
    console.error('Error retrieving Questionnaire:', err);
    setDocGenLoading(false);
    displayErrorMessage(t('Error : Could not retrieve Questionnaire'));
    return;
  }

  if (formData.questionnaire_response_data && formData.client_data && formData.binary_data) {
    try {
      let baseUrl = `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/questionnaireResponses/retrievePDFDocument`;
      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ formData }),
      });
      const dataResponse = await response.json();
      const data = dataResponse.dataResponse;
      setDocGenerated(data.content);
      setSelectedQuestionnaireResponse(formData.questionnaire_response_data);
      setSelectedQuestionnaire(questionnaireData.questionnaire);
      setDocGenLoading(false);

      router.push(`/questionnaire-responses/${row.resource.id}`);
    } catch (err) {
      console.error('Error retrieving document:', err);
      setDocGenLoading(false);
      displayErrorMessage(t('Error : Could not retrieve document'));
      return;
    }
  } else {
    setDocGenLoading(false);
    setSelectedQuestionnaireResponse(formData.questionnaire_response_data);
    setSelectedQuestionnaire(questionnaireData.questionnaire);
    setDocGenerated('');
    console.log('No pdf version');
    router.push(`/questionnaire-responses/${row.resource.id}`);
    return;
  }
};

export default questionnaire;
