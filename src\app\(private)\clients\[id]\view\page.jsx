'use client';
import React, { useEffect, useState, useRef } from 'react';
import { Alert, Box, Button, Grid, Paper } from '@mui/material';
import { useRouter } from 'next/navigation.js';
import { CoordinatorDetailsReadOnlyAndEditMode } from '@cambianrepo/client-info';
import useNotification from '@/lib/hooks/useNotification';
import * as Constants from '@/app/globalConstants';
import { getOrgMetaData } from '@/lib/api/common';
import { ORGANIZATION_ID, ORGANIZATION_METADATA } from '@/lib/constant';
import { useQuery } from '@tanstack/react-query';
import CreateAndEditClientComponent from '@/components/client/CreateAndEditClient';
import { useQuestionnaire } from '@/context/QuestionnaireContext';
import Loader from '@/components/Loader';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';

function CreateAndEditClient(props) {
  const openSnackbar = useNotification();
  const [error, setError] = useState(false);
  const [success, setSuccess] = useState(false);
  const [clientNotFoundError, setClientNotFoundError] = useState(false);
  const [editMode, setEditMode] = React.useState(false);
  const editModeRef = useRef(false);
  const [loading, setLoading] = useState(false);
  const { setSelectedQuestionnaire, setSelectedRepository, setFilteredChannels } = useQuestionnaire();

  const router = useRouter();
  const clientId = props.params.id || undefined;
  const clientPage = props.params.clientPage || false;

  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => getOrgMetaData(),
  });
  const orgDataValues = { ...orgMetaDataQuery.data?.clientInformation };
  const healthcareValues = Array.isArray(orgMetaDataQuery.data?.idTypes) ? orgMetaDataQuery.data.idTypes : [];

  function updateFormData(data) {
    setLoading(true);
    setSelectedQuestionnaire('');
    setSelectedRepository('');
    setFilteredChannels('');
    handleFirstNameChange(data.firstName);
    handleMiddleNameChange(data.middleName);
    handleLastNameChange(data.lastName);
    handleDateOfBirthChange(data.dateOfBirth);
    handleGenderChange(data.gender);
    handlePreferredContactMechanismChange(data.preferredContactMechanism);
    handleSubscribeToNotificationsChange(data.subscribeToNotifications);

    // Handle primary values
    handlePrimaryEmailChange(data.emailAddresses.find((email) => email.primary)?.emailAddress || '');
    handlePrimaryPhoneNumberChange(data.phoneNumbers.find((phone) => phone.primary)?.phoneNumber || '');
    handlePrimaryAddressLineOneChange(data.addresses.find((address) => address.primary)?.address1 || '');
    handlePrimaryAddressLineTwoChange(data.addresses.find((address) => address.primary)?.address2 || '');
    handlePrimarySelectedCityChange(data.addresses.find((address) => address.primary)?.city || '');
    handlePrimarySelectedCountryChange(data.addresses.find((address) => address.primary)?.country || '');
    handlePrimaryProvinceChange(data.addresses.find((address) => address.primary)?.province || '');
    handlePrimaryPostalCodeChange(data.addresses.find((address) => address.primary)?.postalCode || '');
    const primaryIds = data.healthCareIds.filter((healthCareId) => healthCareId.primary);
    handlePrimaryIdChange(primaryIds);

    // Handle non-primary values
    const nonPrimaryEmails = data.emailAddresses.filter((email) => !email.primary);
    const nonPrimaryPhoneNumbers = data.phoneNumbers.filter((phone) => !phone.primary);
    const nonPrimaryAddresses = data.addresses.filter((address) => !address.primary);
    const nonPrimaryIds = data.healthCareIds.filter((healthCareId) => !healthCareId.primary);

    handleNonPrimaryEmailsChange(nonPrimaryEmails);
    handleNonPrimaryPhoneNumbersChange(nonPrimaryPhoneNumbers);
    handleNonPrimaryAddressesChange(nonPrimaryAddresses);
    handleNonPrimaryIdsChange(nonPrimaryIds);
    setLoading(false);
  }

  function handleFirstNameChange(value) {
    if (value === '') {
      value = '';
    }
    setFirstNameConfigs({
      value,
    });
  }

  function handleMiddleNameChange(value) {
    if (value === '') {
      value = '';
    }
    setMiddleNameConfigs({
      value,
    });
  }

  function handleLastNameChange(value) {
    if (value === '') {
      value = '';
    }
    setLastNameConfigs({
      value,
    });
  }

  function handleDateOfBirthChange(value) {
    if (value === '') {
      value = '';
    }
    setDateOfBirthConfigs({
      value,
    });
  }

  function handleGenderChange(value) {
    if (value === '') {
      value = '';
    }
    setGenderConfigs({
      value,
    });
  }

  function handlePreferredContactMechanismChange(value) {
    if (value === '') {
      value = '';
    }
    setPreferredContactMechanismConfigs({
      value,
    });
  }

  function handleSubscribeToNotificationsChange(value) {
    if (value === '') {
      value = '';
    }
    setSubscribeToNotificationsConfigs({
      value,
    });
  }

  function handlePrimaryEmailChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimaryEmailConfigs({
      value,
    });
  }

  function handlePrimaryPhoneNumberChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimaryPhoneNumberConfigs({
      value,
    });
  }

  function handlePrimaryAddressLineOneChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimaryAddressLineOneConfigs({
      value,
    });
  }

  function handlePrimaryAddressLineTwoChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimaryAddressLineTwoConfigs({
      value,
    });
  }

  function handlePrimarySelectedCountryChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimarySelectedCountryConfigs({
      value,
    });
  }

  function handlePrimaryProvinceChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimaryProvinceConfigs({
      value,
    });
  }

  function handlePrimarySelectedCityChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimarySelectedCityConfigs({
      value,
    });
  }

  function handlePrimaryPostalCodeChange(value) {
    if (value === '') {
      value = '';
    }
    setPrimaryPostalCodeConfigs({
      value,
    });
  }

  function handlePrimaryIdChange(primaryId) {
    setIdConfigs(
      primaryId.map((id) => ({
        type: id.type || '',
        value: id.value || '',
        issuer: id.issuer || '',
      })),
    );
  }

  function handleNonPrimaryEmailsChange(nonPrimaryEmails) {
    setNonPrimaryEmailConfigs(nonPrimaryEmails.map((email) => email.emailAddress || ''));
  }

  function handleNonPrimaryPhoneNumbersChange(nonPrimaryPhoneNumbers) {
    setNonPrimaryPhoneNumberConfigs(nonPrimaryPhoneNumbers.map((phone) => phone.phoneNumber || ''));
  }

  function handleNonPrimaryAddressesChange(nonPrimaryAddresses) {
    setNonPrimaryAddressConfigs(
      nonPrimaryAddresses.map((address) => ({
        address1: address.address1 || '',
        address2: address.address2 || '',
        city: address.city || '',
        province: address.province || '',
        country: address.country || '',
        postalCode: address.postalCode || '',
      })),
    );
  }

  function handleNonPrimaryIdsChange(nonPrimaryIds) {
    setNonPrimaryIdConfigs(
      nonPrimaryIds.map((id) => ({
        type: id.type || '',
        value: id.value || '',
        issuer: id.issuer || '',
      })),
    );
  }

  useEffect(() => {
    if (clientId) {
      const result = fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${clientId}`);
      result
        .then((res) => res.json())
        .then((data) => {
          if (data === 'ERR_BAD_RESPONSE' || data === 'ERR_BAD_REQUEST') {
            throw new Error();
          }
          updateFormData(data);
        })
        .catch(() => {
          setClientNotFoundError(true);
        });
    }
  }, [clientId]);

  useEffect(() => {
    if (editModeRef.current === true && !editMode) {
      if (clientId) {
        const result = fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${clientId}`);
        result
          .then((res) => res.json())
          .then((data) => {
            if (data === 'ERR_BAD_RESPONSE' || data === 'ERR_BAD_REQUEST') {
              throw new Error();
            }
            updateFormData(data);
          })
          .catch(() => {
            setClientNotFoundError(true);
          });
      }
    }
    editModeRef.current = editMode;
  }, [editMode]);

  const [firstNameConfigs, setFirstNameConfigs] = useState({
    value: '',
  });

  const [lastNameConfigs, setLastNameConfigs] = useState({
    value: '',
  });

  const [middleNameConfigs, setMiddleNameConfigs] = useState({
    value: '',
  });

  const [dateOfBirthConfigs, setDateOfBirthConfigs] = useState({
    value: '',
  });

  const [preferredContactMechanismConfigs, setPreferredContactMechanismConfigs] = useState({
    value: '',
  });

  const [subscribeToNotificationsConfigs, setSubscribeToNotificationsConfigs] = useState({
    value: '',
  });

  const [emailConfigs, setEmailConfigs] = useState({
    allowed: false,
    multiple: false,
  });

  const [primaryEmailConfigs, setPrimaryEmailConfigs] = useState({
    value: '',
  });

  const [phoneNumberConfigs, setPhoneNumberConfigs] = useState({
    allowed: false,
    multiple: false,
  });

  const [primaryPhoneNumberConfigs, setPrimaryPhoneNumberConfigs] = useState({
    value: '',
  });

  const [addressConfigs, setAddressConfigs] = useState({
    allowed: false,
    multiple: false,
  });

  const [primaryAddressLineOneConfigs, setPrimaryAddressLineOneConfigs] = useState({
    value: '',
  });

  const [primaryAddressLineTwoConfigs, setPrimaryAddressLineTwoConfigs] = useState({
    value: '',
  });

  const [primarySelectedCityConfigs, setPrimarySelectedCityConfigs] = useState({
    value: '',
  });

  const [primaryPostalCodeConfigs, setPrimaryPostalCodeConfigs] = useState({
    value: '',
  });

  const [primaryProvinceConfigs, setPrimaryProvinceConfigs] = useState({
    value: '',
  });

  const [primarySelectedCountryConfigs, setPrimarySelectedCountryConfigs] = useState({
    value: '',
  });

  const [idConfigs, setIdConfigs] = useState({
    type: '',
    value: '',
    issuer: '',
    allowed: false,
    multiple: false,
  });

  const [genderConfigs, setGenderConfigs] = useState({
    value: '',
  });
  const [nonPrimaryEmailConfigs, setNonPrimaryEmailConfigs] = useState([]);
  const [nonPrimaryPhoneNumberConfigs, setNonPrimaryPhoneNumberConfigs] = useState([]);
  const [nonPrimaryAddressConfigs, setNonPrimaryAddressConfigs] = useState([]);
  const [nonPrimaryIdConfigs, setNonPrimaryIdConfigs] = useState([]);

  useEffect(() => {
    if (orgMetaDataQuery.isSuccess && orgDataValues) {
      Object.values(orgDataValues).forEach((field) => {
        switch (field.attribute) {
          case 'MIDDLE_NAME':
            if (field.allowed !== middleNameConfigs.allowed) {
              setMiddleNameConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'DATE_OF_BIRTH':
            if (field.allowed !== dateOfBirthConfigs.allowed) {
              setDateOfBirthConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'GENDER':
            if (field.allowed !== genderConfigs.allowed) {
              setGenderConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'EMAIL':
            if (field.allowed !== emailConfigs.allowed) {
              setEmailConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            if (field.multiple !== emailConfigs.multiple) {
              setEmailConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            break;
          case 'PHONE':
            if (field.allowed !== phoneNumberConfigs.allowed) {
              setPhoneNumberConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            if (field.multiple !== phoneNumberConfigs.multiple) {
              setPhoneNumberConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            break;
          case 'PREFERRED_CONTACT_METHOD':
            if (field.allowed !== preferredContactMechanismConfigs.allowed) {
              setPreferredContactMechanismConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          case 'ADDRESS':
            if (field.allowed !== addressConfigs.allowed) {
              setAddressConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            if (field.multiple !== addressConfigs.multiple) {
              setAddressConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            break;
          case 'IDENTIFICATION':
            if (field.allowed !== idConfigs.allowed) {
              setIdConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            if (field.multiple !== idConfigs.multiple) {
              setIdConfigs((prev) => ({
                ...prev,
                multiple: field.multiple,
              }));
            }
            break;
          case 'NOTIFICATIONS':
            if (field.allowed !== subscribeToNotificationsConfigs.allowed) {
              setSubscribeToNotificationsConfigs((prev) => ({
                ...prev,
                allowed: field.allowed,
              }));
            }
            break;
          default:
            break;
        }
      });
    }
  }, [
    orgMetaDataQuery.isSuccess,
    orgDataValues,
    middleNameConfigs.allowed,
    dateOfBirthConfigs.allowed,
    genderConfigs.allowed,
    emailConfigs.allowed,
    emailConfigs.multiple,
    phoneNumberConfigs.allowed,
    phoneNumberConfigs.multiple,
    preferredContactMechanismConfigs.allowed,
    subscribeToNotificationsConfigs.allowed,
    addressConfigs.allowed,
    addressConfigs.multiple,
    idConfigs.allowed,
    idConfigs.multiple,
  ]);

  return (
    <>
      {loading && <Loader active={loading} />}
      <Grid container>
        <Grid item xs={12} sm={12} sx={{ pl: 2, pr: 2 }}>
          {!clientNotFoundError && (
            <CoordinatorDetailsReadOnlyAndEditMode
              firstNameConfigs={firstNameConfigs}
              middleNameConfigs={middleNameConfigs}
              lastNameConfigs={lastNameConfigs}
              genderConfigs={genderConfigs}
              dateOfBirthConfigs={dateOfBirthConfigs}
              preferredContactMechanismConfigs={preferredContactMechanismConfigs}
              subscribeToNotificationsConfigs={subscribeToNotificationsConfigs}
              emailConfigs={emailConfigs}
              primaryEmailConfigs={primaryEmailConfigs}
              phoneNumberConfigs={phoneNumberConfigs}
              primaryPhoneNumberConfigs={primaryPhoneNumberConfigs}
              addressConfigs={addressConfigs}
              primaryAddressLineOneConfigs={primaryAddressLineOneConfigs}
              primaryAddressLineTwoConfigs={primaryAddressLineTwoConfigs}
              primarySelectedCountryConfigs={primarySelectedCountryConfigs}
              primarySelectedCityConfigs={primarySelectedCityConfigs}
              primaryProvinceConfigs={primaryProvinceConfigs}
              primaryPostalCodeConfigs={primaryPostalCodeConfigs}
              idConfigs={idConfigs}
              nonPrimaryEmailConfigs={nonPrimaryEmailConfigs}
              nonPrimaryPhoneNumberConfigs={nonPrimaryPhoneNumberConfigs}
              nonPrimaryAddressConfigs={nonPrimaryAddressConfigs}
              nonPrimaryIdConfigs={nonPrimaryIdConfigs}
              clientId={clientId}
              editMode={editMode}
              setEditMode={setEditMode}
              clientPage={clientPage}
              CreateAndEditClientComponent={CreateAndEditClientComponent}
            />
          )}
          {clientNotFoundError && (
            <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" mt={3}>
              <Alert severity="error">Client not found. Please try again</Alert>
              <Box mt={3}>
                <Button onClick={() => router.push('/clients')} variant="contained" size={Constants.buttonSize}>
                  Go Back
                </Button>
              </Box>
            </Box>
          )}

          {error &&
            openSnackbar({
              variant: 'error',
              msg: 'An error occurred. Please try again!',
            })}
          {success && console.log(clientId ? 'The client was successfully saved' : 'Client Added Successfully!')}
        </Grid>
      </Grid>
    </>
  );
}

export default CreateAndEditClient;
