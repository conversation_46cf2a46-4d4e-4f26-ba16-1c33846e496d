'use client';

import {
  QuestionnaireEditor,
  headerTemplate,
  questionnaireDetailsTemplate,
  htmlDefaultTemplate as htmlReportDefaultTemplate,
} from '@cambianrepo/questionnaire-editor-v2';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useParams } from 'next/navigation';
import useNotification from '@/lib/hooks/useNotification';
import { deleteArtifact, getArtifact, putQuestionnaire } from '@/lib/api/artifactRepository';
import { CREATE, PUBLIC, PRIVATE, QUESTIONNAIRES, FINAL, NO, BOTH, DRAFT } from '@/lib/constant';
import { useRouter, useSearchParams } from 'next/navigation';
import { isValidV4UUID } from '@/lib/utility';
import { downloadFileInJsonFormat } from '@/lib/utility';
import { useSession } from 'next-auth/react';
import { downloadPDF, removeURLParams } from '../codebookUtility';

const codebookDefaultTemplate = headerTemplate + questionnaireDetailsTemplate;
// TODO: Actions like finalize, duplicate can be done after making changes and before saving.
// TODO: I don't know if that is what we want.
export default function ClientPage() {
  const openSnackbar = useNotification();
  const queryClient = useQueryClient();
  const router = useRouter();
  const searchParams = useSearchParams();
  const visibility = searchParams.get('visibility');
  const { artifactId } = useParams();
  const { data: session } = useSession();
  const user = session?.user;

  const { data, isError, isLoading, error } = useQuery({
    queryKey: [QUESTIONNAIRES, artifactId],
    queryFn: () => getArtifact({ artifactType: QUESTIONNAIRES, artifactId, includeMetadata: true, visibility }),
    enabled: artifactId !== CREATE && isValidV4UUID(artifactId) && (visibility === PUBLIC || visibility === PRIVATE),
  });

  // TODO: This gets rendered twice. Should improve error handling for all our components.
  // Perhaps use ErrorBoundary.
  useEffect(() => {
    if (
      (artifactId !== CREATE && (!isValidV4UUID(artifactId) || (visibility !== PRIVATE && visibility !== PUBLIC))) ||
      isError
    ) {
      router.replace('/questionnaire-editor');
      openSnackbar({
        variant: 'error',
        msg: 'Failed to retrieve questionnaire',
      });
    }
  }, [artifactId, isError]);

  const { questionnaire = {}, pdfTemplate, publishStatus } = data || {};

  const handleFinalize = async (fhirQuestionnaire, pdfTemplate) => {
    if (artifactId === CREATE) {
      openSnackbar({
        variant: 'error',
        msg: 'Save questionnaire before finalizing.',
      });
      return;
    }
    const newFhirQuestionnaire = structuredClone(fhirQuestionnaire);
    newFhirQuestionnaire.publisher = 'Cambian';
    newFhirQuestionnaire.status = FINAL;
    newFhirQuestionnaire.modifiedDate = new Date().toISOString();

    try {
      openSnackbar({ msg: `Finalizing ${newFhirQuestionnaire.name}...` });
      const res = await putQuestionnaire({
        artifactType: QUESTIONNAIRES,
        visibility: PRIVATE,
        requestBody: {
          contentStatus: FINAL,
          publishStatus: NO,
          questionnaire: newFhirQuestionnaire,
          pdfTemplate,
        },
        artifactId: newFhirQuestionnaire.id,
      });
      if (res.status != 200) {
        throw new Error();
      }
      console.log(`Finalized ${newFhirQuestionnaire.name}.`);
      Promise.all([
        queryClient.invalidateQueries([QUESTIONNAIRES, artifactId]),
        queryClient.invalidateQueries([QUESTIONNAIRES, PRIVATE]),
      ]);
      router.push('/questionnaire-editor');
      return { success: true };
    } catch (err) {
      openSnackbar({
        variant: 'error',
        msg: `Finalizing questionnaire ${newFhirQuestionnaire.name} failed.`,
      });
      return { success: false };
    }
  };
  const handleSave = async (fhirQuestionnaire, pdfTemplate) => {
    const newFhirQuestionnaire = structuredClone(fhirQuestionnaire);
    newFhirQuestionnaire.publisher = 'Cambian';
    newFhirQuestionnaire.modifiedDate = new Date().toISOString();
    if (publishStatus != NO && newFhirQuestionnaire.status == FINAL) {
      console.log("'Publish' instead of save");
      return handlePublish(artifactId, publishStatus, {
        triggeredBySave: true,
        newFhirQuestionnaire: newFhirQuestionnaire,
      });
    }
    try {
      openSnackbar({ msg: `Saving ${newFhirQuestionnaire.name}...` });
      const res = await putQuestionnaire({
        artifactType: QUESTIONNAIRES,
        visibility: 'private',
        requestBody: {
          contentStatus: newFhirQuestionnaire.status,
          publishStatus: NO,
          questionnaire: newFhirQuestionnaire,
          pdfTemplate,
        },
        artifactId: artifactId === CREATE ? undefined : artifactId,
      });
      if (res.status != 200) {
        throw new Error();
      }
      console.log(`Saved ${newFhirQuestionnaire.name} as draft.`);
      if (artifactId === CREATE) {
        const data = await res.json();
        const { artifactId: newArtifactId } = data.responseBody;
        router.push(`/questionnaire-editor/${newArtifactId}?visibility=private`);
      } else {
        Promise.all([
          queryClient.invalidateQueries([QUESTIONNAIRES, artifactId]),
          queryClient.invalidateQueries([QUESTIONNAIRES, PRIVATE]),
        ]);
        return { success: true };
      }
    } catch (err) {
      openSnackbar({
        variant: 'error',
        msg: `Saving questionnaire ${newFhirQuestionnaire.name} as draft failed.`,
      });
      return { success: false };
    }
  };

  const handlePublish = async (questionnaireId, newPublishStatus, options = {}) => {
    const { triggeredBySave = false, newFhirQuestionnaire = null } = options;
    console.log({ options });
    let questionnaireDataToPublish = newFhirQuestionnaire || questionnaire;
    try {
      // Assume that all questionnaires here are final
      openSnackbar({
        msg: `Publishing ${questionnaireDataToPublish.name} to ${newPublishStatus}...`,
      });
      const currentPublishStatus = publishStatus;

      if (currentPublishStatus === newPublishStatus && !triggeredBySave) {
        openSnackbar({
          variant: 'warning',
          msg: 'There is nothing to publish. Aborting...',
        });
        return { success: false };
      }

      const questionnaireActionPromises = [];
      if (newPublishStatus === BOTH || newPublishStatus === PUBLIC) {
        questionnaireActionPromises.push(
          putQuestionnaire({
            artifactType: QUESTIONNAIRES,
            visibility: PUBLIC,
            requestBody: {
              contentStatus: FINAL,
              publishStatus: newPublishStatus,
              questionnaire: questionnaireDataToPublish,
              pdfTemplate,
            },
            artifactId: questionnaireId,
          }),
        );
      } else if (currentPublishStatus === BOTH || currentPublishStatus === PUBLIC) {
        questionnaireActionPromises.push(
          deleteArtifact({
            artifactType: QUESTIONNAIRES,
            visibility: PUBLIC,
            artifactId: questionnaireId,
          }),
        );
      }

      questionnaireActionPromises.push(
        putQuestionnaire({
          artifactType: QUESTIONNAIRES,
          visibility: PRIVATE,
          requestBody: {
            contentStatus: FINAL,
            publishStatus: newPublishStatus,
            questionnaire: questionnaireDataToPublish,
            pdfTemplate,
          },
          artifactId: questionnaireId,
        }),
      );
      const results = await Promise.all(questionnaireActionPromises);

      // TODO: What if one is deleted but another fails..? We really need a better error handling.
      // Right now, just check status for one
      if (results[0].status === 200) {
        queryClient.invalidateQueries({ queryKey: [QUESTIONNAIRES, PRIVATE] });
        queryClient.invalidateQueries({ queryKey: [QUESTIONNAIRES, questionnaireId] });
        router.push('/questionnaire-editor');
        if (triggeredBySave) {
          console.log(`Successfully saved and published finalized questionnaire ${questionnaireDataToPublish.name}`);
        } else {
          console.log(`Successfully published questionnaire ${questionnaireDataToPublish.name}`);
        }
        return { success: true };
      } else {
        throw Error(JSON.stringify(results[0]));
      }
    } catch (err) {
      console.error(err);
      openSnackbar({
        variant: 'error',
        msg: `Failed to publish questionnaire, ${questionnaireDataToPublish.name}`,
      });
      return { success: false };
    }
  };

  const handleDelete = async (selectedQuestionnaire) => {
    try {
      console.log(selectedQuestionnaire);
      if (artifactId === CREATE) {
        router.push('/questionnaire-editor');
        return { success: true };
      }
      openSnackbar({
        msg: `Deleting ${selectedQuestionnaire.shortName}...`,
      });
      const deletePromises = [];
      if (publishStatus === PUBLIC || publishStatus === BOTH) {
        deletePromises.push(deleteArtifact({ artifactId, artifactType: QUESTIONNAIRES, visibility: PUBLIC }));
      }
      deletePromises.push(deleteArtifact({ artifactId, artifactType: QUESTIONNAIRES, visibility: PRIVATE }));

      const results = await Promise.all(deletePromises);

      // TODO: What if one is deleted but another fails..? We really need a better error handling.
      // Right now, just check status for one
      if (results[0].status === 200) {
        queryClient.setQueryData([QUESTIONNAIRES, PRIVATE], (oldData = []) =>
          oldData?.filter((questionnaire) => questionnaire.artifactId !== artifactId),
        );
        console.log(`Successfully deleted questionnaire, ${selectedQuestionnaire.shortName}`);
        queryClient.removeQueries({ queryKey: [QUESTIONNAIRES, artifactId], exact: true });
        router.push('/questionnaire-editor');
        return { success: true };
      } else {
        throw Error(JSON.stringify(results[0]));
      }
    } catch (err) {
      console.error(err);
      openSnackbar({
        variant: 'error',
        msg: `Failed to delete questionnaire, ${selectedQuestionnaire.shortName}`,
      });
      return { success: false };
    }
  };

  const handleExport = async (questionnaireId) => {
    try {
      if (artifactId === CREATE) {
        openSnackbar({
          variant: 'error',
          msg: 'Save questionnaire before exporting.',
        });
        return { success: false };
      }
      openSnackbar({
        msg: 'Exporting a questionnaire...',
      });

      if (pdfTemplate) {
        const hasPdfTemplate = questionnaire.extension.some((ext) => ext.url === 'pdftemplate-base64');
        if (!hasPdfTemplate) {
          questionnaire.extension.push({
            url: 'pdftemplate-base64',
            valueString: pdfTemplate,
          });
        }
      }

      if (!questionnaire) {
        throw Error('Something went wrong');
      }

      downloadFileInJsonFormat(JSON.stringify(questionnaire), questionnaire.name);

      console.log(`Successfully Exported ${questionnaire.name}. Check your downloads!`);
    } catch (err) {
      console.log(err);
      openSnackbar({
        variant: 'error',
        msg: 'Error in exporting questionnaire.',
      });
    }
  };

  const handleDuplicate = async (questionnaireId) => {
    // TODO: We should think about whether to allow duplicate before saving.
    if (artifactId === CREATE) {
      openSnackbar({
        variant: 'error',
        msg: 'Save questionnaire before duplicating.',
      });
      return { success: false };
    }
    try {
      const duplicatedQuestionnaire = structuredClone(questionnaire);

      openSnackbar({ msg: `Duplicating ${duplicatedQuestionnaire.name} questionnaire...` });

      // TODO: This causes the copy versions to have the same questionnaire name
      // Perhaps the put artifact endpoint in Artifact Repository, perhaps it should append a number if the same name questionnaire is created.
      duplicatedQuestionnaire.name = `${duplicatedQuestionnaire.name} COPY`;
      duplicatedQuestionnaire.title = `${duplicatedQuestionnaire.title} COPY`;
      duplicatedQuestionnaire.status = DRAFT;
      const res = await putQuestionnaire({
        artifactType: QUESTIONNAIRES,
        visibility: PRIVATE,
        requestBody: {
          contentStatus: DRAFT,
          publishStatus: NO,
          questionnaire: duplicatedQuestionnaire,
          pdfTemplate,
        },
      });
      if (res.status === 200) {
        queryClient.invalidateQueries({ queryKey: [QUESTIONNAIRES, PRIVATE] });
        console.log(`Duplicated "${duplicatedQuestionnaire.name} as draft"`);
        router.replace('/questionnaire-editor');
        return { success: true };
      } else {
        throw new Error('putQuestionnaire failed.');
      }
    } catch (err) {
      console.log(err);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to duplicate the questionnaire',
      });
      return { success: false };
    }
  };

  const handlePreview = (questionnaireId, publishStatus) => {
    const orgId = user?.orgId;
    if (!orgId) {
      openSnackbar({
        variant: 'error',
        msg: 'Technical error. Please contact the administrator.',
      });
      console.log('org id does not exists in user session');
      return;
    }
    const publishStatusParam = `repository=${publishStatus}`;
    const WIDGET_URL = `${process.env.NEXT_PUBLIC_WIDGET_BASE_URL}/widget/organizations/${orgId}/questionnaireWidget/1?qid=${questionnaireId || artifactId}&${publishStatusParam}`;
    window.open(WIDGET_URL, '_blank');
  };

  const handleCodebookDownload = async (questionnaireId, publishStatus, htmlString) => {
    if (!htmlString) {
      openSnackbar({
        variant: 'error',
        msg: 'Error in downloading questionnaire codebook.',
      });
      return;
    }
    // TODO: @Prashant we need to pass parsedHtmlString here (i.e., all the syntax variable in HTML should be replaced by their corresponding their values)
    try {
      openSnackbar({
        msg: 'Downloading questionnaire codebook...',
      });
      const appUrl =
        process.env.NODE_ENV === 'development' ? 'https://www.google.com' : removeURLParams(window.location.href);

      const widgetServicesDownloadResponse = await fetch(
        `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/widgetServices`,
        {
          method: 'POST',
          body: JSON.stringify({
            appUrl,
            htmlString,
          }),
        },
      );
      if (!widgetServicesDownloadResponse.ok) {
        openSnackbar({
          variant: 'error',
          msg: `Error while downloading code book for ${questionnaire.name}`,
        });
        return;
      }
      const pdfData = await widgetServicesDownloadResponse.json();
      downloadPDF(pdfData, `codebook_${questionnaireId}`);
      console.log(`Successfully downloaded code book for ${questionnaire.name}. Check your downloads!`);
    } catch (error) {
      console.log(error);
      openSnackbar({
        variant: 'error',
        msg: 'Failed to download questionnaire codebook.',
      });
    }
  };

  // TODO: Better loading screen
  if (isLoading) {
    return <>LOADING...</>;
  }
  return (
    <>
      <QuestionnaireEditor
        publishedRepository={publishStatus}
        handleNavigation={() => {
          router.push('/questionnaire-editor');
        }}
        onSaveDraftCallback={handleSave}
        onPublishCallback={handlePublish}
        onPreviewCallback={handlePreview}
        onDuplicateCallback={handleDuplicate}
        onDeleteCallback={handleDelete}
        onExportCallback={handleExport}
        onFinalizeCallback={handleFinalize}
        existingQuestionnaireData={questionnaire}
        setExistingQuestionnaire={() => {}} // This is supplied by the QuestionnaireEditorKit component if we decide to combine list and editor page. We don't need this callback as we have separated the two.
        existingPdfTemplate={pdfTemplate}
        setExistingPdfTemplate={() => {}} // This is supplied by the QuestionnaireEditorKit component if we decide to combine list and editor page. We don't need this callback as we have separated the two.
        handleDownloadCodebookCallback={handleCodebookDownload}
        htmlReportDefaultTemplate={htmlReportDefaultTemplate}
        codebookDefaultTemplate={codebookDefaultTemplate}
      />
    </>
  );
}
