import { MD5 } from 'crypto-js';

export const ORGANIZATION_ID = 'ORGANIZATION_ID';
export const WIDGET_ID = 'WIDGET_ID';
export const BOOKING_WIDGET_ID = 'BOOKING_WIDGET_ID';
export const QUESTIONNAIRE_ID = 'QUESTIONNAIRE_ID';
export const QUESTIONNAIRE_WIDGET_ID = 'QUESTIONNAIRE_WIDGET_ID';
export const REGISTRATION_ID = 'REGISTRATION_ID';
export const REGISTRATION_WIDGET_ID = 'REGISTRATION_WIDGET_ID';
export const BASE64_IMAGE = 'data:image/jpg;base64,';
export const EMAIL = 'EMAIL';
export const PHONE = 'PHONE';
export const PHN = 'PHN';
export const HEALTH_CARD = 'HEALTH_CARD';
export const COMPARE_IMAGE_URL = 'http://cambian.com/Location/location-image';
export const QUESTIONNAIRE = 'Questionnaire';
export const BOOKING = 'Booking';
export const REGISTRATION = 'Registration';
export const QUESTIONNAIRE_CAPS = 'QUESTIONNAIRE';
export const BOOKING_CAPS = 'BOOKING';
export const REGISTRATION_CAPS = 'REGISTRATION';
export const QUESTIONNAIRE_WIDGET_QUERY_KEY = 'QUESTIONNAIRE_WIDGET';
export const BOOKING_WIDGET_QUERY_KEY = 'BOOKING_WIDGET';
export const REGISTRATION_WIDGET_QUERY_KEY = 'REGISTRATION_WIDGET';
export const BOOKING_WIDGET_PREVIEW_URL = `/widget/organizations/${ORGANIZATION_ID}/bookingWidget/${WIDGET_ID}`;
export const QUESTIONNAIRE_WIDGET_PREVIEW_URL = `/widget/organizations/${ORGANIZATION_ID}/questionnaireWidget/${WIDGET_ID}`;
export const REGISTRATION_WIDGET_PREVIEW_URL = `/widget/organizations/${ORGANIZATION_ID}/registrationWidget/${WIDGET_ID}`;
export const CODE = 'code';
export const IDENTIFIED = 'IDENTIFIED';
export const DEIDENTIFIED = 'DEIDENTIFIED';
export const UNIDENTIFIED = 'UNIDENTIFIED';
export const CLINICAL = 'ORGANIZATION_USER';
export const PATIENT = 'INDIVIDUAL';
export const WIDGET_TYPE = 'WIDGET_TYPE';
export const IFRAME_CDN_LINK_NEW = '/iframeResizerNew.js';
export const SCORE_DEFINITIONS_URL = 'http://cambian.com/Questionnaire/list-of-score-definitions';
export const SCORE_NAME_URL = 'http://cambian.com/Questionnaire/score-name';
export const REMINDER_PREFERENCE = 'REMINDER_PREFERENCE';
export const COMMUNICATION = 'COMMUNICATION';

export const repositoryTypes = {
  public: 'PUBLIC',
  private: 'PRIVATE',
};

export const organizationAllFields = [
  {
    SK: 0,
    systemRequired: false,
    defaultPosition: 1,
    code: 'FIRST_NAME',
    display: 'First Name',
    allowMultiple: false,
  },
  {
    SK: 1,
    systemRequired: false,
    defaultPosition: 2,
    code: 'MIDDLE_NAME',
    display: 'Middle Name',
    allowMultiple: false,
  },
  {
    SK: 2,
    systemRequired: false,
    defaultPosition: 3,
    code: 'LAST_NAME',
    display: 'Last Name',
    allowMultiple: false,
  },
  {
    SK: 3,
    systemRequired: false,
    defaultPosition: 4,
    code: 'DATE_OF_BIRTH',
    display: 'Date of Birth',
    allowMultiple: false,
  },
  {
    SK: 4,
    systemRequired: false,
    defaultPosition: 5,
    code: 'GENDER',
    display: 'Gender',
    allowMultiple: false,
  },
  {
    SK: 5,
    systemRequired: false,
    defaultPosition: 6,
    code: 'EMAIL',
    display: 'Email',
    allowMultiple: false,
  },
  {
    SK: 6,
    systemRequired: false,
    defaultPosition: 7,
    code: 'PHONE',
    display: 'Phone',
    allowMultiple: false,
  },
  {
    SK: 7,
    systemRequired: false,
    defaultPosition: 8,
    code: 'PREFERRED_CONTACT_METHOD',
    display: 'Preferred Contact Method',
    allowMultiple: false,
  },
  {
    SK: 8,
    systemRequired: false,
    defaultPosition: 9,
    code: 'NOTIFICATIONS',
    display: 'Notifications',
    allowMultiple: false,
  },
  {
    SK: 9,
    systemRequired: false,
    defaultPosition: 10,
    code: 'ADDRESS',
    display: 'Address',
    allowMultiple: false,
  },
  {
    SK: 10,
    systemRequired: false,
    defaultPosition: 11,
    code: 'IDENTIFICATION',
    display: 'Identification',
    allowMultiple: false,
  },
];

const generateImageHash = (base64String) => {
  return MD5(base64String).toString();
};

export const prepareMapPlaceholderImage = (widgetData) => {
  const updatedWidgetData = { ...widgetData };

  if (updatedWidgetData.mapPlaceholderImage?.base64 && !updatedWidgetData.enableMap) {
    if (!updatedWidgetData.mapPlaceholderImage.imageHash) {
      updatedWidgetData.mapPlaceholderImage.imageHash = generateImageHash(updatedWidgetData.mapPlaceholderImage.base64);
    }
  } else if (updatedWidgetData.enableMap) {
    updatedWidgetData.mapPlaceholderImage = {
      filename: '',
      base64: '',
    };
  }

  return updatedWidgetData;
};
