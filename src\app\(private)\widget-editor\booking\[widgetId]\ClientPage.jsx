'use client';
import React, { useEffect, useState } from 'react';
import { AddNewBookingWidget } from '@cambianrepo/widget-editor-v2';
import {
  organizationAllFields,
  prepareMapPlaceholderImage,
  BOOKING_WIDGET_QUERY_KEY,
  QUESTIONNAIRE_WIDGET_QUERY_KEY,
  REGISTRATION_WIDGET_QUERY_KEY,
} from '@/lib/widget-editor/utils/constants';
import { extractGUID, isValidV4UUID, sortWidgets } from '@/lib/utility';
import useNotification from '@/lib/hooks/useNotification';
import { useParams, useRouter } from 'next/navigation';
import { CREATE, LOCATIONS, ORGANIZATION_METADATA, SERVICES, ALL_ID_TYPES_DATA } from '@/lib/constant';
import { useMutation, useQueries, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  server_createBookingWidget,
  server_fetchBookingWidgetById,
  server_updateBookingWidget,
  server_fetchBookingWidgetsList,
  server_fetchQuestionnaireWidgetsList,
  server_fetchRegistrationWidgetsList,
} from '@/actions/widgetConfig';
import { server_fetchLocationsList, server_fetchServicesList } from '@/actions/schedulerBooking';
import { getAllIdTypes, loadOrganization } from '@/lib/api/orgData';

export default function ClientPage() {
  const openSnackbar = useNotification();
  const router = useRouter();
  const { widgetId } = useParams();
  const queryClient = useQueryClient();

  const widgetDataQuery = useQuery({
    queryKey: [BOOKING_WIDGET_QUERY_KEY, widgetId],
    queryFn: () => server_fetchBookingWidgetById(widgetId),
    enabled: widgetId !== CREATE && isValidV4UUID(widgetId),
    meta: {
      errorMessage: 'Failed to retrieve requested booking widget.',
    },
  });
  const [currentWidgetData, setCurrentWidgetData] = useState();

  useEffect(() => {
    if (widgetDataQuery?.data) {
      setCurrentWidgetData(widgetDataQuery.data);
    }
  }, [widgetDataQuery?.data]);

  const allWidgetsQueries = useQueries({
    queries: [
      {
        queryKey: [QUESTIONNAIRE_WIDGET_QUERY_KEY],
        queryFn: () => server_fetchQuestionnaireWidgetsList(),
        meta: {
          errorMessage: 'Failed to retrieve questionnaire widgets',
        },
      },
      {
        queryKey: [REGISTRATION_WIDGET_QUERY_KEY],
        queryFn: () => server_fetchRegistrationWidgetsList(),
        meta: {
          errorMessage: 'Failed to retrieve registration widgets',
        },
      },
      {
        queryKey: [BOOKING_WIDGET_QUERY_KEY],
        queryFn: () => server_fetchBookingWidgetsList(),
        meta: {
          errorMessage: 'Failed to retrieve booking widgets',
        },
      },
    ],
    combine: (results) => {
      const mergedWidgets = results.map((result) => result.data).flat(1);
      const sortedWidgets = sortWidgets(mergedWidgets, 'name');
      return {
        data: sortedWidgets,
      };
    },
  });

  const organizationMetadataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => loadOrganization(),
    meta: {
      errorMessage: 'Failed to retrieve organization metadata.',
    },
  });

  const allIdTypesQuery = useQuery({
    queryKey: [ALL_ID_TYPES_DATA],
    queryFn: () => getAllIdTypes(),
    meta: {
      errorMessage: 'Failed to retrieve all id types.',
    },
  });

  const idTypes = organizationMetadataQuery.data?.idTypes || [];
  const organizationRequiredFields = organizationMetadataQuery.data?.clientInformation;
  const demographicFields = organizationAllFields.map((field) => {
    const requiredField = organizationRequiredFields?.find((orgField) => orgField.attribute === field.code);
    const allowed = requiredField ? requiredField.allowed : false;
    const mandatory = requiredField ? requiredField.required : false;
    const multiple = requiredField && requiredField?.multiple ? requiredField?.multiple : false;
    const match = requiredField ? requiredField.match : false;

    return {
      ...field,
      systemRequired: allowed,
      systemMandatory: mandatory,
      isMandatory: mandatory,
      idTypes: field.code === 'IDENTIFICATION' ? idTypes || [] : undefined,
      systemAllowMultiple: multiple,
      match,
    };
  });

  const servicesQuery = useQuery({
    queryKey: [SERVICES],
    queryFn: () => server_fetchServicesList(),
    meta: {
      errorMessage: 'Failed to retrieve services list.',
    },
  });
  const servicesList = servicesQuery.data || [];

  const locationsQuery = useQuery({
    queryKey: [LOCATIONS],
    queryFn: () => server_fetchLocationsList(),
    meta: {
      errorMessage: 'Failed to retrieve locations list.',
    },
  });
  const locationsList = locationsQuery.data || [];

  const createBookingWidgetMutation = useMutation({
    mutationFn: (widgetData) => {
      const preparedWidgetData = prepareMapPlaceholderImage(widgetData);
      return server_createBookingWidget(preparedWidgetData);
    },
    onSuccess: (data, _variables, _context) => {
      queryClient.setQueryData([BOOKING_WIDGET_QUERY_KEY], (oldData = []) => [...oldData, data]);
      queryClient.setQueryData([BOOKING_WIDGET_QUERY_KEY, extractGUID(data.SK)], data);
      router.push('/widget-editor');
      console.log('Booking widget created successfully');
      return { success: true };
    },
    meta: {
      errorMessage: 'Failed to create booking widget',
    },
  });

  const updateBookingWidgetMutation = useMutation({
    mutationFn: (widgetData) => {
      const preparedWidgetData = prepareMapPlaceholderImage(widgetData);
      return server_updateBookingWidget(preparedWidgetData);
    },
    onSuccess: (data, _widgetData, _context) => {
      queryClient.setQueryData([BOOKING_WIDGET_QUERY_KEY], (prevWidgetsList = []) =>
        prevWidgetsList.map((existingWidget) => {
          if (existingWidget.SK === data.SK) {
            return data;
          }
          return existingWidget;
        }),
      );
      queryClient.setQueryData([BOOKING_WIDGET_QUERY_KEY, extractGUID(data.SK)], data);
      router.push('/widget-editor');
      console.log('Booking widget updated successfully');

      return { success: true };
    },
    meta: {
      errorMessage: 'Failed to update booking widget',
    },
  });

  const handleSaveOrUpdateWidgetCallback = async (widgetData, widgetType) => {
    console.log('save/update clicked', widgetData, widgetType);
    const processedData =
      widgetData.SK && widgetData.PK
        ? widgetData
        : {
            ...widgetData,
            SK: currentWidgetData?.SK,
            PK: currentWidgetData?.PK,
            createdAt: widgetData.createdAt || new Date().toISOString(),
          };

    console.log('save/update clicked', widgetData, widgetType);
    if (widgetData?.SK) {
      openSnackbar({ msg: 'Updating Widget...' });
      return await updateBookingWidgetMutation.mutateAsync(processedData);
    }
    openSnackbar({ msg: 'Creating Widget...' });
    return await createBookingWidgetMutation.mutateAsync(widgetData);
  };

  const fetchWidgetWithLanguage = async (params, callback) => {
    const { widgetId, widgetType, language } = params;
    console.log('fetchWidgetWithLanguage called with params:', params);
    try {
      let widgetData = await server_fetchBookingWidgetById(widgetId, language);
      if (callback) {
        callback(widgetData); // May be null if 404 occurred
      }
      if (widgetData) {
        setCurrentWidgetData(widgetData);
      }
    } catch (error) {
      console.error(`Error fetching ${widgetType} for language ${language}:`, error);
      if (callback) {
        console.log('Calling callback with null due to error');
        callback(null);
      }
    }
  };
  const isLoading = widgetId !== CREATE && !currentWidgetData?.currentLanguage && isValidV4UUID(widgetId);
  //Show loading screen until widget data has been fetched so that configuration is not fetched with empty fields
  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <AddNewBookingWidget
        handleNavigationCallback={() => {
          router.push('/widget-editor');
        }}
        widgetsList={allWidgetsQueries.data || []}
        existingWidgetData={currentWidgetData}
        handleSaveOrUpdateWidgetCallback={handleSaveOrUpdateWidgetCallback}
        bookingWidgetFields={demographicFields}
        servicesList={servicesList}
        locationsList={locationsList}
        allIdTypes={allIdTypesQuery.data?.idTypes}
        orgRequiredIdTypes={idTypes}
        fetchWidgetWithLanguage={fetchWidgetWithLanguage}
      />
    </>
  );
}
