import { NextResponse } from 'next/server';

export default middleware = async (request) => {
  if (request.nextUrl.pathname === '/api/auth/callback/cognito-mfa' && request.method === 'GET') {
    console.log('Redirecting unexpected GET on /api/auth/callback/cognito-mfa');
    request.nextUrl.pathname = '/home';
    return NextResponse.redirect(request.nextUrl);
  }

  return NextResponse.next();
};

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - Exclude any paths that end with /icon.ico (favicon file)
     * - Exclude any paths that end with /logo.png (not sure if this is needed)
     * - manifest.webmanifest (web manifest file)
     * - Exclude "/" route as it's a public route
     */
    '/api/auth/callback/cognito-mfa',
    '/((?!api|_next/static|_next/image|.*/icon.ico|.*/*.png|manifest.webmanifest|s*$).*)',
  ],
};
