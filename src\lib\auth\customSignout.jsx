'use-client';
import { signOut } from 'next-auth/react';
import { addCsrfToken, clientFetch } from '@/lib/fetch/client';

export async function customSignOut({ callbackUrl }) {
  // Revoke cognito tokens
  const response = await clientFetch(addCsrfToken)(`${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/auth/revokeTokens`, {
    method: 'POST',
  });
  console.log('cognito tokens revoked', response.status === 200);

  // Clear cookies from browser
  document.cookie.split(';').forEach((cookie) => {
    const cookieName = cookie.split('=')[0].trim();
    document.cookie = `${cookieName}=; max-age=0; path=/;`;
  });
  console.log('cookies cleared');

  // This will clear cookies from next-auth as well
  return signOut({ callbackUrl });
}
