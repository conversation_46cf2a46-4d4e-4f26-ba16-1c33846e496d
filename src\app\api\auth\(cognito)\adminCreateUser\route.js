import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { handler } from '@/lib/middleware/handler';
import { adminCreateUser } from '@/lib/auth/cognito';
import { csrfProtected } from '@/lib/middleware/csrfProtected';
import AwsError from '@/lib/error/AwsError';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  try {
    const token = await getToken({ req });

    if (!token || !token.idToken) {
      console.error('Unable to get authentication token');
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }
    const { username, emailAddress, phoneNumber } = await req.json();

    const [signUpResult, signUpError] = await adminCreateUser({
      username,
      emailAddress: emailAddress || '',
      phoneNumber: phoneNumber || '',
      orgId: token.orgId,
    });

    if (signUpError) {
      console.log(signUpError);
      return signUpError.toNextResponse();
    }

    return NextResponse.json({}, { status: signUpResult.status });
  } catch (err) {
    console.error('Error in getEndpoint:', err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const POST = handler(csrfProtected(['POST']), postEndpoint);
