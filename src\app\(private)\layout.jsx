'use client';
import PrivateRoute from './PrivateRoute';
import { PermissionsProvider } from '@/context/UserPermissions';
import { ClientSearchResultsProvider } from '@/context/ClientSearchResults';
import { QuestionnaireProvider } from '@/context/QuestionnaireContext';
import { UserIconProvider } from '@/context/UserDetailsContext';
import { OrgDataProvider } from '@/context/OrgDataContext';
import * as React from 'react';
import MainLayout from './MainLayout';
import { BreadcrumbsProvider } from '@cambianrepo/breadcrumbs';
import { breadcrumbConfig } from '@/lib/constant';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n';
import { ErrorDetailsModalProvider } from '@/context/ErrorDetailsModalProvider';

export default function PrivateLayout({ children }) {
  return (
    <PrivateRoute>
      <PermissionsProvider>
        <UserIconProvider>
          <OrgDataProvider>
            <ClientSearchResultsProvider>
              <QuestionnaireProvider>
                <BreadcrumbsProvider breadcrumbConfig={breadcrumbConfig}>
                  <I18nextProvider i18n={i18n}>
                    <ErrorDetailsModalProvider>
                      <MainLayout>{children}</MainLayout>
                    </ErrorDetailsModalProvider>
                  </I18nextProvider>
                </BreadcrumbsProvider>
              </QuestionnaireProvider>
            </ClientSearchResultsProvider>
          </OrgDataProvider>
        </UserIconProvider>
      </PermissionsProvider>
    </PrivateRoute>
  );
}
