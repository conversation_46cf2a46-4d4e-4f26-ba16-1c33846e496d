import { NO_STORE, ORGANIZATION_ID } from '../constant';
import { fetchNextRoute } from './services/clientFetchRoutes';

export const getOrgMetaData = async () => {
  try {
    const res = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}`);
    const data = await res.json();
    return data;
  } catch (error) {
    throw error;
  }
};

export const getOrgReports = async () => {
  try {
    const res = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/report-settings`);
    const data = await res.json();
    return data;
  } catch (error) {
    throw error;
  }
};

export const getOrganizationConsentAgreement = async () => {
  try {
    const response = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/consent-agreement`, {
      errorsToIgnore: [404],
    });
    if (response.status === 404) {
      return { consentAgreementUrl: null };
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};

export const getOrganizationIcon = async () => {
  try {
    const response = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/icon`, {
      errorsToIgnore: [404],
    });
    if (response.status === 404) {
      return { iconUrl: null };
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};
