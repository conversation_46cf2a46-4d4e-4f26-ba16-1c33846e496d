import { useState, useEffect } from 'react';
import * as React from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TablePagination,
  TableRow,
  TableContainer,
  Typography,
  Snackbar,
  Alert,
  Button,
  Link,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CircularProgress from '@mui/material/CircularProgress';
import TableSortLabel from '@mui/material/TableSortLabel';
import PropTypes from 'prop-types';
import { visuallyHidden } from '@mui/utils';
import { getComparator, stableSort } from './utility/tableSortingUtil';
import Paper from '@mui/material/Paper';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import Loader from '@/components/Loader';
import { useRouter } from 'next/navigation';
import { useQuestionnaire } from '@/context/QuestionnaireContext';
import AddQuestionnaireResponse from '@/components/questionnaireResponse/AddQuestionnaireResponse';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID } from '@/lib/constant';

const headCells = [
  {
    id: 'resource.name',
    disablePadding: false,
    label: 'Title',
  },
  {
    id: 'resource.authored',
    disablePadding: false,
    label: 'Date',
  },
  {
    id: 'score',
    disablePadding: false,
    label: 'Score',
    skipSorting: true,
  },
  {
    id: 'actions',
    disablePadding: false,
    label: '',
    skipSorting: true,
  },
];

function EnhancedTableHead(props) {
  const { order, orderBy, onRequestSort } = props;
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead>
      <TableRow>
        {headCells.map((headCell) => (
          <TableCell
            key={headCell.id}
            align="left"
            padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? order : false}
          >
            {headCell.skipSorting ? (
              headCell.label
            ) : (
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : 'asc'}
                onClick={createSortHandler(headCell.id)}
              >
                {headCell.label}
                {orderBy === headCell.id ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            )}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

EnhancedTableHead.propTypes = {
  onRequestSort: PropTypes.func.isRequired,
  order: PropTypes.oneOf(['asc', 'desc']).isRequired,
  orderBy: PropTypes.string.isRequired,
};

const ResponsesTable = ({ clientId, cdrIdentifier }) => {
  const [limit, setLimit] = useState(5);
  const [page, setPage] = useState(0);
  const [order, setOrder] = React.useState('desc');
  const [orderBy, setOrderBy] = React.useState('resource.authored');
  const [filteredRecords, setFilteredRecords] = React.useState([]);
  const [fetching, setFetching] = React.useState(true);
  const { t } = useTranslation();
  const [noQRfound, setNoQRFound] = React.useState('');
  const [errorSnackbarOpen, setErrorSnackbarOpen] = useState(false);
  const [errorSnackbarMessage, setErrorSnackbarMessage] = useState('');
  const router = useRouter();
  const [cdrPatientId, setCdrPatientId] = useState('');
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const { showAddDuplicateResponse, setShowAddDuplicateResponse } = useQuestionnaire();
  const [initialResponseData, setInitialResponseData] = useState({
    questionnaireId: '',
    response: null,
  });
  const open = Boolean(anchorEl);

  const handleMenuClick = (event, row) => {
    setAnchorEl(event.currentTarget);
    setSelectedRow(row);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRow(null);
  };

  const refreshData = async () => {
    try {
      setFetching(true);
      if (cdrIdentifier) {
        await getQuestionnaireResponses(cdrIdentifier);
      } else if (clientId && cdrPatientId) {
        await getQuestionnaireResponses(cdrPatientId);
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setFetching(false);
    }
  };

  const handleDuplicate = async () => {
    try {
      setLoading(true);
      if (!selectedRow || !selectedRow.resource || !selectedRow.resource.id) {
        throw new Error('No valid row selected for duplication');
      }
      const response = await fetchNextRoute(
        'organizationCDR',
        `/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse/${selectedRow.resource.id}`,
      );

      if (!response.ok) throw new Error('Failed to fetch questionnaire response');
      const data = await response.json();
      const questionnaireResponse = data.dataResponse || data;

      let questionnaireId = questionnaireResponse.questionnaire;
      if (questionnaireId.includes('/')) {
        questionnaireId = questionnaireId.split('/').pop();
      }

      if (!questionnaireId) throw new Error('Could not extract questionnaire ID');

      setInitialResponseData({
        questionnaireId,
        response: questionnaireResponse,
      });
      setShowAddDuplicateResponse(true);
      handleMenuClose();
    } catch (error) {
      console.error('Error duplicating questionnaire response:', error);
      displayErrorMessage(t('Error: Could not duplicate questionnaire response - ' + error.message));
    } finally {
      setLoading(false);
    }
  };

  const onCancel = () => {
    setShowAddDuplicateResponse(false);
    setInitialResponseData({
      questionnaireId: '',
      response: null,
    });
  };

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };
  const getQuestionnaireResponses = async (cdrId) => {
    const cdrIdValue = cdrId.value;
    const responsesRes = await fetchNextRoute(
      'organizationCDR',
      `/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse?subject=${cdrIdValue}&_count=1000`,
    );
    const responses = await responsesRes.json();
    if (responses.entry) {
      setFilteredRecords(responses.entry);
    } else {
      setNoQRFound(true);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!clientId && !cdrIdentifier) {
        return;
      }

      setFetching(true);
      setNoQRFound(false);
      try {
        if (cdrIdentifier) {
          // handle Location or Practitioner case
          await getQuestionnaireResponses(cdrIdentifier);
        } else if (clientId) {
          // handle client case
          const res = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${clientId}`);
          if (!res.ok) throw new Error('Failed to fetch patient data');
          const data = await res.json();
          const clientData = data;
          const cdrPatientId = clientData.identifiers.find((identifier) => identifier.type === 'CDR_PATIENT_ID');
          if (cdrPatientId) {
            setCdrPatientId(cdrPatientId);
            await getQuestionnaireResponses(cdrPatientId);
          } else {
            setNoQRFound(true);
            displayErrorMessage(t('Error : Could not fetch Patient Id for given client'));
          }
        }
      } catch (error) {
        setNoQRFound(true);
        displayErrorMessage(t('Error : Could not fetch data from API'));
      } finally {
        setFetching(false);
      }
    };
    fetchData();
  }, [clientId]);

  function displayErrorMessage(message) {
    setErrorSnackbarOpen(true);
    setErrorSnackbarMessage(message);
  }

  useEffect(() => {
    setPage(0);
  }, [filteredRecords]);

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const getName = (row) => {
    for (const extension of row.resource.extension) {
      if (extension.url.includes('questionnaire-name')) {
        if (extension.valueString) {
          return extension.valueString;
        } else {
          return '-';
        }
      }
    }
    return '-';
  };

  const transformRecordsWithTitle = (records) => {
    return records.map((record) => ({
      ...record,
      resource: {
        ...record.resource,
        name: getName(record),
      },
    }));
  };

  const visibleRows = React.useMemo(() => {
    const transformed = transformRecordsWithTitle(filteredRecords);
    return stableSort(transformed, getComparator(order, orderBy)).slice(page * limit, page * limit + limit);
  }, [order, orderBy, page, limit, filteredRecords]);

  const getFormattedDateTime = (dateField) => {
    return format(new Date(dateField), 'yyyy-MM-dd h:mm a');
  };

  const getScore = (row) => {
    let scores = [];
    for (const extension of row.resource.extension) {
      if (extension.url.includes('calculated-scores')) {
        if (extension.extension.length > 0) {
          extension.extension.forEach((scoreExtension) => {
            if (scoreExtension.valueString) {
              scores.push(scoreExtension.valueString);
            }
          });
        }
      }
    }
    return scores.length > 0 ? scores.join(', ') : '-';
  };

  const handleClick = (row) => {
    sessionStorage.setItem('selectedRowResource', JSON.stringify(row.resource));
    if (clientId) {
      router.push(`/clients/questionnaire/questionnaire-report?identifier=${clientId}`);
    } else if (cdrIdentifier) {
      router.push(`/questionnaire-responses/questionnaire-report?identifier=${cdrIdentifier.value}`);
    } else {
      displayErrorMessage(t('Error : No identifier found for this subject.'));
    }
  };

  return (
    <>
      {fetching && (
        <div>
          <Box sx={{ display: 'flex' }} display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
            <CircularProgress />
          </Box>
        </div>
      )}
      {noQRfound && (
        <Box sx={{ width: '100%', p: 2 }}>
          <Typography>{'No questionnaire response data'}</Typography>
        </Box>
      )}
      {loading && <Loader active={loading} />}
      {showAddDuplicateResponse ? (
        <AddQuestionnaireResponse
          clientId={clientId}
          cdrIdentifier={cdrPatientId}
          targetSubjectType={'Patient'}
          onCancel={onCancel}
          initialQuestionnaireId={initialResponseData?.questionnaireId}
          initialResponse={initialResponseData?.response}
          onComplete={refreshData}
        />
      ) : (
        !fetching &&
        !noQRfound && (
          <div>
            <>
              <Box sx={{ width: '100%', p: 2 }}>
                <div>
                  <TableContainer component={Paper}>
                    <Table>
                      <EnhancedTableHead
                        order={order}
                        orderBy={orderBy}
                        onRequestSort={handleRequestSort}
                        rowCount={filteredRecords.length}
                      />
                      <TableBody>
                        {visibleRows.slice(0, limit).map((row) => {
                          return (
                            <TableRow key={row.resource.id}>
                              <TableCell align="left">
                                <Link
                                  component="button"
                                  variant="body2"
                                  sx={{ textAlign: 'left' }}
                                  onClick={() => handleClick(row)}
                                >
                                  {getName(row)}
                                </Link>
                              </TableCell>
                              <TableCell align="left">{getFormattedDateTime(row.resource.authored)}</TableCell>
                              <TableCell align="left">{getScore(row)}</TableCell>
                              <TableCell align="center" style={{ width: '100px' }}>
                                <IconButton onClick={(e) => handleMenuClick(e, row)}>
                                  <MoreVertIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>

                    <TablePagination
                      component="div"
                      count={filteredRecords.length}
                      onPageChange={handlePageChange}
                      onRowsPerPageChange={handleLimitChange}
                      page={page}
                      rowsPerPage={limit}
                      rowsPerPageOptions={[5, 10, 25]}
                    />
                  </TableContainer>
                </div>
              </Box>
            </>
          </div>
        )
      )}

      <Menu id="actions-menu" anchorEl={anchorEl} keepMounted open={open} onClose={handleMenuClose}>
        <MenuItem onClick={handleDuplicate}>Duplicate</MenuItem>
      </Menu>

      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        open={errorSnackbarOpen}
        autoHideDuration={6000}
        onClose={() => setErrorSnackbarOpen(false)}
      >
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => setErrorSnackbarOpen(false)}>
              {t('Dismiss')}
            </Button>
          }
        >
          {errorSnackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ResponsesTable;
