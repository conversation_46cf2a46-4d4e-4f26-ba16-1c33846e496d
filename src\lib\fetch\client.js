import { getCsrfToken } from 'next-auth/react';

// All our Next.js Route handlers expects jwt token from request's Cookie header.
// Sometimes, the API request is made from Server Component. For some reason, the cookie is not automatically passed on.
// Thus, in each Server Component, manually pass the cookie if it exist.
// When the API request is made from Client Component, cookie is automatically passed on, so let the application handles that.
// Similar problem existed for page router and calling API from getServerSideProps
// https://stackoverflow.com/questions/76279526/next-auth-gettoken-dont-working-on-serverside
export async function addCookie(_url, _options, next) {
  if (typeof window === 'undefined') {
    // DO not throw an error here. It's an exception and caveat of this client and server module design.
    // Sometimes, we just can't know whether an api is called from server component or client component. Needs to improve.
    // throw new Error('Use a fetch/server module instead.');
  }

  // Cookie is already added if requests are made from browser
  next();
}
// Only needs this if you make a request to csrf protected next router.
// If you call AWS resrouces directly from client component or server componet, this is not needed.
export async function addCsrfToken(_url, options, next) {
  try {
    if (typeof window === 'undefined') {
      throw new Error('Should not use this in server');
    }
    const token = await getCsrfToken();
    if (!token) {
      throw new Error('CSRF token not found');
    }

    options.headers ??= {};
    options.headers['X-CSRF-Token'] = token;
    next();
  } catch (error) {
    console.log('addCsrfToken/client failed');
    throw error;
  }
}

let openSnackbar;

export const setOpenSnackbar = (fn) => {
  openSnackbar = fn;
};

export const clientFetch =
  (...middleware) =>
  async (url, options = {}, errorMsgForClient = 'Request failed') => {
    try {
      for (let i = 0; i < middleware.length; i++) {
        let nextInvoked = false;

        /**
         * Move to the next middleware in the chain.
         * @function
         */
        const next = async () => {
          nextInvoked = true;
        };

        const changedUrl = await middleware[i](url, options, next);

        if (typeof changedUrl === 'function') {
          throw new Error('One of your closure middlewares was not properly initialized.');
        }
        if (changedUrl) {
          url = changedUrl;
        }
        if (!nextInvoked) {
          throw new Error('Middleware/client failed. next() not invoked.');
        }
      }
    } catch (error) {
      console.log(
        'fetchWithMiddleware/client FAILED:',
        options.method?.toUpperCase() || 'GET',
        url,
        'options',
        options,
      );
      console.log(error);
      throw error;
    }

    const res = await fetch(url, options);
    if (!res.ok) {
      if (openSnackbar) {
        const data = await res.json();
        openSnackbar({
          variant: 'error',
          msg: errorMsgForClient,
          details: {
            apiRouteUrl: url,
            options,
            apiErrorMsg: data,
            timestamp: new Date(Date.now()).toString(),
          },
        });
      }
    }
    return res;
  };
