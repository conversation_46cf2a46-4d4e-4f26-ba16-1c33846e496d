import { AppBar, Toolbar, Typography, Box, Link } from '@mui/material';
import NotListedLocationIcon from '@mui/icons-material/NotListedLocation';
import { Branding } from '@/components/header/Branding';

export default async function NotFound() {
  return (
    <>
      <AppBar position="fixed" color="default" sx={{ height: '75px' }}>
        <Toolbar>
          <Branding />
        </Toolbar>
      </AppBar>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: 'calc(100vh - 75px)',
          textAlign: 'center',
        }}
      >
        <NotListedLocationIcon sx={{ fontSize: 100, opacity: 0.1 }} />
        <Typography variant="h1" sx={{ mb: 2, mt: 2 }}>
          Not Found
        </Typography>
        <Typography variant="subtitle1">Could not find requested resource.</Typography>
        <Link href="/" style={{ marginTop: 20 }}>
          Back to Home
        </Link>
      </Box>
    </>
  );
}
