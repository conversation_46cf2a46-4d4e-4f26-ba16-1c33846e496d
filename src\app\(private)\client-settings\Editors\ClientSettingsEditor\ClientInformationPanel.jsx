import {
  <PERSON><PERSON>,
  <PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { FormContainer } from 'react-hook-form-mui';
import React, { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import useNotification from '@/lib/hooks/useNotification';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { MultiSelect } from '@/components/MultiSelect';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID } from '@/lib/constant';

function ClientInformationPanel({ orgClientInformation, allIdTypes, orgIdTypes, handleFormSaveCallback }) {
  const formContext = useForm(
    {
      defaultValues: {
        clientInformation: orgClientInformation || [],
        idTypes: orgIdTypes || [],
      },
    },
    [orgIdTypes, orgClientInformation],
  );

  const [initialIdTypes, setInitialIdTypes] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [openMultipleDialog, setOpenMultipleDialog] = useState(false);
  const [selectedCheckbox, setSelectedCheckbox] = useState(null);
  const [multipleCheckbox, setMultipleCheckbox] = useState(null);
  const [allowedCheckbox, setAllowedCheckbox] = useState(null);
  const [dialogMessage, setDialogMessage] = useState('');
  const [dialogHeading, setDialogHeading] = useState('');

  useEffect(() => {
    formContext.setValue('clientInformation', orgClientInformation);
  }, [orgClientInformation]);

  useEffect(() => {
    setInitialIdTypes(
      orgIdTypes.map((idType) => ({
        idType: idType.idType,
        allowedIssuers: Array.isArray(idType.allowedIssuers)
          ? idType.allowedIssuers.map((issuer) => ({
              issuer: issuer.issuer,
              displayName: issuer.displayName,
            }))
          : [],
        allowed: idType.allowed,
      })),
    );
  }, [orgIdTypes]);

  useEffect(() => {
    formContext.setValue('idTypes', orgIdTypes);
  }, [orgIdTypes]);

  const { formState } = formContext;
  const { isDirty } = formState;
  formContext.watch();
  const openSnackbar = useNotification();

  const handleSubmit = React.useCallback(
    async (data) => {
      if (data.clientInformation.find((attribute) => attribute.attribute === 'IDENTIFICATION')?.allowed === false) {
        for (const idType of data.idTypes) {
          const hasClients = await checkClientUsingIdTypeAndIssuer(idType.idType);
          if (hasClients) {
            openSnackbar({
              variant: 'error',
              msg: `IDENTIFICATION cannot be removed as it has clients linked to it.`,
            });
            return;
          }
        }
      }

      const currentIdTypes = data.idTypes.map((idType) => ({
        idType: idType.idType,
        allowedIssuers: Array.isArray(idType.allowedIssuers)
          ? idType.allowedIssuers.map((issuer) => ({
              issuer: issuer.issuer,
              displayName: issuer.displayName,
            }))
          : [],
        allowed: idType.allowed,
      }));

      const removedIdTypes = initialIdTypes
        .map((initialIdType) => {
          const currentIdType = currentIdTypes.find((idType) => idType.idType === initialIdType.idType);
          if (currentIdType && !currentIdType.allowed && initialIdType.allowed) {
            return { idType: initialIdType.idType, removedIssuers: null };
          }
          if (!currentIdType) {
            return {
              idType: initialIdType.idType,
              removedIssuers: initialIdType.allowedIssuers.length > 0 ? initialIdType.allowedIssuers : null,
            };
          }
          if (initialIdType.allowedIssuers.length > 0) {
            const removedIssuers = initialIdType.allowedIssuers.filter(
              (initialIssuer) =>
                !currentIdType.allowedIssuers.find((currentIssuer) => currentIssuer.issuer === initialIssuer.issuer),
            );
            if (removedIssuers.length > 0) {
              return { idType: initialIdType.idType, removedIssuers };
            }
          }
          if (initialIdType.allowedIssuers.length === 0 && !currentIdType?.allowed) {
            return null;
          }

          return null;
        })
        .filter(Boolean);

      for (const { idType, removedIssuers } of removedIdTypes) {
        if (removedIssuers) {
          for (const { issuer, displayName } of removedIssuers) {
            const hasClients = await checkClientUsingIdTypeAndIssuer(idType, issuer);
            if (hasClients) {
              openSnackbar({
                variant: 'error',
                msg: `There are clients linked to the Type "${idType}" with the Issuer "${displayName}"`,
              });
              return;
            }
          }
        } else {
          const hasClients = await checkClientUsingIdTypeAndIssuer(idType);
          if (hasClients) {
            openSnackbar({
              variant: 'error',
              msg: `There are clients linked to the Type "${idType}"`,
            });
            return;
          }
        }
      }

      const requiredAttributes = ['FIRST_NAME', 'LAST_NAME', 'DATE_OF_BIRTH'];
      data.clientInformation = data.clientInformation.map((attribute) => {
        if (requiredAttributes.includes(attribute.attribute)) {
          return {
            ...attribute,
            allowed: true,
            required: true,
          };
        }
        return attribute;
      });
      // Validation for PREFERRED_CONTACT_METHOD:
      const preferred_contact_method_field = data.clientInformation.find(
        (attribute) => attribute.attribute === 'PREFERRED_CONTACT_METHOD',
      );
      const subscribe_to_notifications_field = data.clientInformation.find(
        (attribute) => attribute.attribute === 'NOTIFICATIONS',
      );
      const email_field = data.clientInformation.find((attribute) => attribute.attribute === 'EMAIL');
      const phone_field = data.clientInformation.find((attribute) => attribute.attribute === 'PHONE');
      const validateContactMethod = (field, emailField, phoneField, methodName) => {
        if (field?.allowed && !emailField.allowed && !phoneField.allowed) {
          openSnackbar({
            variant: 'error',
            msg: `Since ${methodName} is allowed, either EMAIL or PHONE must be allowed`,
          });
          return true;
        } else if (field?.required && !emailField.required && !phoneField.required) {
          openSnackbar({
            variant: 'error',
            msg: `Since ${methodName} is required, either EMAIL or PHONE must be required`,
          });
          return true;
        }
        return false;
      };
      if (validateContactMethod(preferred_contact_method_field, email_field, phone_field, 'PREFERRED_CONTACT_METHOD')) {
        return;
      }
      if (validateContactMethod(subscribe_to_notifications_field, email_field, phone_field, 'NOTIFICATIONS')) {
        return;
      }

      // Validation for IDENTIFICATION fields
      let minIdTypeRequirementSatisfied = true;
      if (data.clientInformation.find((attribute) => attribute.attribute === 'IDENTIFICATION')?.allowed) {
        minIdTypeRequirementSatisfied = false;
        for (const idType of data.idTypes) {
          if (idType.allowed) {
            minIdTypeRequirementSatisfied = true;
            if (idType.allowedIssuers?.length === 0 && idType.idType !== 'Other') {
              openSnackbar({
                variant: 'error',
                msg: `For the allowed identification type "${idType.idType}", 
                you must select at least one allowed issuer.`,
              });
              return;
            }
          }
          if (idType.required) {
            if (idType.requiredIssuers.length === 0 && idType.idType !== 'Other') {
              openSnackbar({
                variant: 'error',
                msg: `For the required identification type "${idType.idType}", 
                you must select at least one required issuer.`,
              });
              return;
            }
          }
        }

        if (!minIdTypeRequirementSatisfied) {
          openSnackbar({
            variant: 'error',
            msg: `Since you allowed IDENTIFICATION to be filled", 
            you must select at least one allowed identification type.`,
          });
          return;
        }
      } else {
        data.idTypes = [];
      }

      const currentDataIdTypes = data.idTypes;
      data.idTypes = data.idTypes.map((idType) => {
        const newIdType = {
          ...idType,
          issuers: allIdTypes
            .find((idT) => idT.idType === idType.idType)
            ?.issuers.map((issuer) => {
              return {
                ...issuer,
                allowed:
                  idType.allowedIssuers?.some((allowedIssuer) => allowedIssuer.issuer === issuer.issuer) || false,
                required:
                  idType.requiredIssuers?.some((requiredIssuer) => requiredIssuer.issuer === issuer.issuer) || false,
              };
            }),
        };
        delete newIdType.allowedIssuers;
        delete newIdType.requiredIssuers;
        return newIdType;
      });
      if (await handleFormSaveCallback(data)) {
        formContext.reset({
          clientInformation: data.clientInformation,
          idTypes: currentDataIdTypes,
        });
      }
    },
    [formState, initialIdTypes],
  );

  const checkClientUsingIdTypeAndIssuer = async (idType, issuer = null) => {
    const searchParams = {
      healthCareIdType: idType,
      healthCareIdIssuer: issuer,
    };

    try {
      let queryString = ``;
      for (const key in searchParams) {
        if (searchParams[key] !== null && searchParams[key] !== undefined && searchParams[key] !== '') {
          queryString += `${encodeURIComponent(key)}=${encodeURIComponent(searchParams[key])}&`;
        }
      }
      const response = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients?${queryString.slice(0, -1)}`);

      const dataResponse = await response.json();
      const totalElements = Number(dataResponse?.page?.totalElements);
      return totalElements > 0;
    } catch (error) {
      console.error('Error fetching client data:', error);
      return true;
    }
  };

  const handleIssuersChange = (indexToUpdate, allowedOrRequired, selectedValues) => {
    const newIdTypes = [...formContext.getValues('idTypes')];
    if (allowedOrRequired === 'allowed') {
      newIdTypes[indexToUpdate].allowedIssuers = selectedValues;
      newIdTypes[indexToUpdate].requiredIssuers =
        newIdTypes[indexToUpdate].requiredIssuers?.filter((issuer) =>
          selectedValues.some((selectedValue) => selectedValue.issuer === issuer.issuer),
        ) || [];
    } else {
      newIdTypes[indexToUpdate].requiredIssuers = selectedValues;
    }
    formContext.setValue('idTypes', newIdTypes);
  };

  const handleClientInformationRequiredChange = (index, field) => (event) => {
    field.onChange(event);
    if (event.target.checked) {
      formContext.setValue(`clientInformation[${index}].allowed`, true);
    }
    if (!event.target.checked) {
      formContext.setValue(`clientInformation[${index}].match`, false);
    }
  };

  const handleDialogConfirm = () => {
    if (multipleCheckbox) {
      multipleCheckbox.field.onChange({ target: { checked: false } });
      formContext.setValue(`clientInformation[${multipleCheckbox.index}].multiple`, false);
      setMultipleCheckbox(null);
      setOpenDialog(false);
      return;
    }
    if (allowedCheckbox) {
      allowedCheckbox.field.onChange({ target: { checked: false } });
      formContext.setValue(`clientInformation[${allowedCheckbox.index}].allowed`, false);
      formContext.setValue(`clientInformation[${allowedCheckbox.index}].required`, false);
      formContext.setValue(`clientInformation[${allowedCheckbox.index}].multiple`, false);
      formContext.setValue(`clientInformation[${allowedCheckbox.index}].match`, false);
      setAllowedCheckbox(null);
    }
    setOpenDialog(false);
  };

  const handleDialogCancel = () => {
    if (multipleCheckbox) {
      formContext.setValue(`clientInformation[${multipleCheckbox.index}].multiple`, true);
    }
    if (allowedCheckbox) {
      formContext.setValue(`clientInformation[${allowedCheckbox.index}].allowed`, true);
    }
    setOpenDialog(false);
  };

  const getSelectedIdTypeCount = () => {
    const idTypes = formContext.getValues('idTypes') || [];
    return idTypes.filter((idType) => idType.required).length;
  };

  const handleDialogOk = () => {
    formContext.setValue(`clientInformation[${multipleCheckbox.index}].multiple`, true);
    setOpenMultipleDialog(false);
    setOpenDialog(false);
  };

  const handleClientInformationMultipleChange = (index, field) => (event) => {
    const isChecked = event.target.checked;
    const idTypes = [...formContext.getValues('idTypes')];

    if (!isChecked) {
      if (hasMultipleRequiredIdTypesOrIssuers()) {
        setMultipleCheckbox({ index, field });
        setOpenMultipleDialog(true);
        setDialogHeading('Cannot disable multiple identification');
        setDialogMessage(
          'You cannot disable multiple identification when more than one required ID type or issuer is selected. Please remove the extra required ID types or issuers first.',
        );
        return;
      }
      setMultipleCheckbox({ index, field });
      setOpenDialog(true);
      setDialogHeading('Remove non-primary values?');
      setDialogMessage(
        'This action will remove any non-primary values set for this attribute when a client is updated. Do you wish to proceed?',
      );
    } else {
      setMultipleCheckbox(null);
      field.onChange(event);
      if (isChecked) {
        formContext.setValue(`clientInformation[${index}].allowed`, true);
      }
    }
    idTypes.forEach((idType, i) => {
      if (!isChecked) {
        if (idType.required) {
          formContext.setValue(`idTypes[${i}].required`, true);
          formContext.setValue(`idTypes[${i}].allowed`, true);

          if (idType.requiredIssuers && idType.requiredIssuers.length > 0) {
            formContext.setValue(`idTypes[${i}].requiredIssuers`, idType.requiredIssuers.slice(0, 1));
          }
        } else {
          formContext.setValue(`idTypes[${i}].required`, false);
          formContext.setValue(`idTypes[${i}].requiredIssuers`, []);
        }
      } else {
        formContext.setValue(`idTypes[${i}].allowed`, idType.allowed);
      }
    });
  };

  const handleClientInformationAllowedChange = (index, field) => (event) => {
    if (!event.target.checked) {
      setAllowedCheckbox({ index, field });
      setOpenDialog(true);
      setDialogHeading('Remove values?');
      setDialogMessage(
        'This action will remove any values set for this attribute when a client is updated. Do you wish to proceed?',
      );
    } else {
      setAllowedCheckbox(null);
      field.onChange(event);
    }
  };

  const handleClientInformationMatchChange = (index, field) => (event) => {
    field.onChange(event);
    if (event.target.checked) {
      formContext.setValue(`clientInformation[${index}].match`, true);
      formContext.setValue(`clientInformation[${index}].allowed`, true);
      formContext.setValue(`clientInformation[${index}].required`, true);
    } else {
      formContext.setValue(`clientInformation[${index}].match`, false);
    }
  };

  const handleIdentificationAllowedChange = (index, field) => (event) => {
    field.onChange(event);
    if (!event.target.checked) {
      formContext.setValue(`clientInformation[${index}].required`, false);
      formContext.setValue(`clientInformation[${index}].match`, false);
      if (formContext.getValues(`clientInformation[${index}].multiple`)) {
        formContext.setValue(`clientInformation[${index}].multiple`, false);
      }
    }
  };

  const handleIdTypeAllowedChange = (idType, index, field) => (event) => {
    field.onChange(event);
    if (event.target.checked) {
      const availableIssuers = allIdTypes.find((idT) => idT.idType === idType.idType)?.issuers;
      if (availableIssuers?.length === 1) {
        formContext.setValue(`idTypes[${index}].allowedIssuers`, availableIssuers);
      }
    }
    if (!event.target.checked) {
      formContext.setValue(`idTypes[${index}].required`, false);
      formContext.setValue(`idTypes[${index}].match`, false);
      formContext.setValue(`idTypes[${index}].allowedIssuers`, []);
      formContext.setValue(`idTypes[${index}].requiredIssuers`, []);
    }
  };

  const handleIdTypeMatchChange = (idType, index, field) => (event) => {
    field.onChange(event);
    if (event.target.checked) {
      formContext.setValue(`idTypes[${index}].match`, true);
      formContext.setValue(`idTypes[${index}].allowed`, true);
      formContext.setValue(`idTypes[${index}].required`, true);
      const availableIssuers = allIdTypes.find((idT) => idT.idType === idType.idType)?.issuers;
      if (availableIssuers?.length === 1) {
        formContext.setValue(`idTypes[${index}].allowedIssuers`, availableIssuers);
        formContext.setValue(`idTypes[${index}].requiredIssuers`, availableIssuers);
      }
    }
    if (!event.target.checked) {
      formContext.setValue(`idTypes[${index}].match`, false);
    }
  };

  const handleIdTypeRequiredChange = (idType, index, field) => (event) => {
    field.onChange(event);
    const isChecked = event.target.checked;
    const multipleIdentification =
      formContext.getValues('clientInformation').find((attr) => attr.attribute === 'IDENTIFICATION')?.multiple || false;

    if (!multipleIdentification) {
      if (isChecked) {
        setSelectedCheckbox(index);
        formContext.getValues('idTypes').forEach((_, i) => {
          if (i !== index) {
            formContext.setValue(`idTypes[${i}].required`, false);
            formContext.setValue(`idTypes[${i}].requiredIssuers`, []);
          }
        });
      } else {
        setSelectedCheckbox(null);
      }
    }

    if (isChecked) {
      formContext.setValue(`idTypes[${index}].allowed`, true);
      const availableIssuers = allIdTypes.find((idT) => idT.idType === idType.idType)?.issuers;
      if (availableIssuers?.length === 1) {
        formContext.setValue(`idTypes[${index}].allowedIssuers`, availableIssuers);
        formContext.setValue(`idTypes[${index}].requiredIssuers`, availableIssuers);
      }
    } else {
      formContext.setValue(`idTypes[${index}].requiredIssuers`, []);
      formContext.setValue(`idTypes[${index}].match`, false);
    }
  };

  const hasMultipleRequiredIdTypesOrIssuers = () => {
    const idTypes = formContext.getValues('idTypes');
    let requiredIdTypesCount = 0;
    let requiredIssuersCount = 0;

    for (const idType of idTypes) {
      if (idType.required) {
        requiredIdTypesCount++;
        requiredIssuersCount += idType.requiredIssuers?.length || 0;
      }
    }

    return requiredIdTypesCount > 1 || requiredIssuersCount > 1;
  };

  function convertToTitleCase(str) {
    return str
      .toLowerCase()
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
      .join(' ');
  }

  const checkboxCellStyles = () => ({
    minWidth: '65px',
    width: '65px',
    padding: 0,
  });

  return (
    <FormContainer onSuccess={handleSubmit} formContext={formContext}>
      <TableContainer className="clientInfoTables" sx={{ width: '510px', overflow: 'hidden' }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={{ minWidth: '255px', width: '255px' }}>Attribute</TableCell>
              <TableCell align="center" sx={checkboxCellStyles}>
                Display
              </TableCell>
              <TableCell align="center" sx={checkboxCellStyles}>
                Require
              </TableCell>
              <TableCell align="center" sx={checkboxCellStyles}>
                Multiple
              </TableCell>
              <TableCell align="center" sx={checkboxCellStyles}>
                Match
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {formContext.getValues('clientInformation')?.map((attribute, index) => {
              const readOnlyAttributes = ['FIRST_NAME', 'LAST_NAME', 'DATE_OF_BIRTH'].includes(attribute.attribute);
              return (
                <TableRow key={index}>
                  <TableCell>{convertToTitleCase(attribute.attribute)}</TableCell>
                  <TableCell align="center">
                    <Controller
                      name={`clientInformation[${index}].allowed`}
                      control={formContext.control}
                      render={({ field }) => (
                        <Checkbox
                          size="small"
                          sx={{ padding: 0 }}
                          {...field}
                          checked={readOnlyAttributes || field.value}
                          onChange={
                            ['IDENTIFICATION'].includes(attribute.attribute)
                              ? handleIdentificationAllowedChange(index, field)
                              : handleClientInformationAllowedChange(index, field)
                          }
                          disabled={readOnlyAttributes}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Controller
                      name={`clientInformation[${index}].required`}
                      control={formContext.control}
                      render={({ field }) => (
                        <Checkbox
                          {...field}
                          size="small"
                          sx={{ padding: 0 }}
                          checked={readOnlyAttributes || field.value}
                          onChange={handleClientInformationRequiredChange(index, field)}
                          disabled={readOnlyAttributes}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="center">
                    {['EMAIL', 'PHONE', 'ADDRESS', 'IDENTIFICATION'].includes(attribute.attribute) ? (
                      <Controller
                        name={`clientInformation[${index}].multiple`}
                        control={formContext.control}
                        render={({ field }) => (
                          <Checkbox
                            {...field}
                            size="small"
                            sx={{ padding: 0 }}
                            checked={attribute.allowed && field.value}
                            onChange={handleClientInformationMultipleChange(index, field)}
                          />
                        )}
                      />
                    ) : (
                      <></>
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Controller
                      name={`clientInformation[${index}].match`}
                      control={formContext.control}
                      render={({ field }) => (
                        <Checkbox
                          {...field}
                          size="small"
                          sx={{ padding: 0 }}
                          checked={field.value}
                          onChange={handleClientInformationMatchChange(index, field)}
                        />
                      )}
                    />
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      {formContext.getValues('clientInformation')?.find((attribute) => attribute.attribute === 'IDENTIFICATION')
        ?.allowed ? (
        <>
          <TableContainer
            className="clientInfoTables"
            sx={{ width: '900px', overflow: 'visible', paddingLeft: '20px' }}
          >
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  <TableCell sx={{ minWidth: '170px' }}>Type</TableCell>
                  <TableCell align="center" sx={checkboxCellStyles}>
                    Display
                  </TableCell>
                  <TableCell>Displayed Issuers</TableCell>
                  <TableCell align="center" sx={checkboxCellStyles}>
                    Require
                  </TableCell>
                  <TableCell sx={{ maxWidth: '500px', minWidth: '500px' }}>Required Issuers</TableCell>
                  {/* <TableCell align="center" sx={checkboxCellStyles}>
                    Match
                  </TableCell> */}
                </TableRow>
              </TableHead>
              <TableBody>
                {formContext.getValues('idTypes').map((idType, index) => (
                  <TableRow key={index} sx={{ minHeight: '52px', height: '52px' }}>
                    <TableCell>{idType.displayName}</TableCell>
                    <TableCell align="center">
                      <Controller
                        name={`idTypes[${index}].allowed`}
                        control={formContext.control}
                        render={({ field }) => (
                          <Checkbox
                            {...field}
                            size="small"
                            sx={{ padding: 0 }}
                            checked={field.value}
                            onChange={handleIdTypeAllowedChange(idType, index, field)}
                          />
                        )}
                      />
                    </TableCell>
                    <TableCell align="center" sx={{ maxWidth: '500px', minWidth: '500px' }}>
                      {idType.idType !== 'Other' ? (
                        <>
                          <MultiSelect
                            items={allIdTypes.find((idT) => idT.idType === idType.idType)?.issuers}
                            selectedValues={idType.allowedIssuers}
                            setSelectedValues={(selectedValues) =>
                              handleIssuersChange(index, 'allowed', selectedValues)
                            }
                            label={'Allowed Issuers'}
                            limitTags={2}
                            control={formContext.control}
                            controllerName={`idTypes[${index}].allowedIssuers`}
                            disabled={!idType.allowed}
                            getOptionLabel={(option) => option?.displayName || ''}
                            getOptionSelected={(option, anotherOption) => option.issuer === anotherOption.issuer}
                            isOptionEqualToValue={(option, value) => option.issuer === value.issuer}
                            multipleIdentification={true}
                          />
                        </>
                      ) : (
                        <></>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Controller
                        name={`idTypes[${index}].required`}
                        control={formContext.control}
                        render={({ field }) => {
                          const multipleIdentification =
                            formContext
                              .getValues('clientInformation')
                              .find((attr) => attr.attribute === 'IDENTIFICATION')?.multiple || false;
                          const selectedIdTypeCount = getSelectedIdTypeCount();
                          return (
                            <Checkbox
                              {...field}
                              size="small"
                              sx={{ padding: 0 }}
                              checked={idType.allowed && field.value}
                              onChange={handleIdTypeRequiredChange(idType, index, field)}
                              disabled={!multipleIdentification && selectedIdTypeCount === 1 && !field.value}
                            />
                          );
                        }}
                      />
                    </TableCell>
                    <TableCell align="center" sx={{ maxWidth: '500px', minWidth: '500px' }}>
                      {idType.idType !== 'Other' && (
                        <>
                          {(() => {
                            const multipleIdentification =
                              formContext
                                .getValues('clientInformation')
                                .find((attr) => attr.attribute === 'IDENTIFICATION')?.multiple || false;

                            return (
                              <MultiSelect
                                items={idType.allowedIssuers}
                                selectedValues={idType.requiredIssuers}
                                setSelectedValues={(selectedValues) =>
                                  handleIssuersChange(index, 'required', selectedValues)
                                }
                                label={'Required Issuers'}
                                limitTags={2}
                                control={formContext.control}
                                controllerName={`idTypes[${index}].requiredIssuers`}
                                disabled={!idType.allowed || !idType.required || idType.allowedIssuers?.length === 0}
                                getOptionLabel={(option) => option.displayName}
                                getOptionSelected={(option, anotherOption) => option.issuer === anotherOption.issuer}
                                isOptionEqualToValue={(option, value) => option.issuer === value.issuer}
                                multipleIdentification={multipleIdentification}
                              />
                            );
                          })()}
                        </>
                      )}
                    </TableCell>
                    {/* <TableCell align="center">
                      <Controller
                        name={`idTypes[${index}].match`}
                        control={formContext.control}
                        render={({ field }) => (
                          <Checkbox
                            {...field}
                            size="small"
                            sx={{ padding: 0 }}
                            checked={field.value}
                            onChange={handleIdTypeMatchChange(idType, index, field)}
                          />
                        )}
                      />
                    </TableCell> */}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      ) : (
        <></>
      )}
      <Dialog sx={{ '& .MuiDialog-paper': { width: 'auto' } }} maxWidth="xs" open={openDialog}>
        <DialogTitle>{dialogHeading}</DialogTitle>
        <DialogContent>
          <Typography>{dialogMessage}</Typography>
        </DialogContent>
        <DialogActions>
          <Button autoFocus variant="outlined" onClick={handleDialogCancel}>
            Cancel
          </Button>
          <Button onClick={handleDialogConfirm} variant="contained" color="error">
            Proceed
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog sx={{ '& .MuiDialog-paper': { width: 'auto' } }} maxWidth="xs" open={openMultipleDialog}>
        <DialogTitle>{dialogHeading}</DialogTitle>
        <DialogContent>
          <Typography>{dialogMessage}</Typography>
        </DialogContent>
        <DialogActions>
          <Button autoFocus variant="contained" onClick={handleDialogOk}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
      <Box width="auto">
        <Button sx={{ marginTop: '20px' }} variant="contained" type="submit" disabled={!isDirty}>
          Save
        </Button>
      </Box>
    </FormContainer>
  );
}

const tableTheme = createTheme({
  components: {
    MuiTableCell: {
      styleOverrides: {
        head: {
          fontWeight: 600,
          borderTop: '1px solid #0000001f',
        },
        body: {
          borderBottom: '0px solid black',
        },
      },
    },
  },
});

export default ClientInformationPanel;
