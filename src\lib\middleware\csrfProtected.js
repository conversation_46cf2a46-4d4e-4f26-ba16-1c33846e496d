import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { createHash } from 'crypto';

export const csrfProtected = (protectedMethods) => async (req, next) => {
  if (isProtected(req, protectedMethods)) {
    // Followed solution here to verify csrf token in Next 14
    // https://github.com/nextauthjs/next-auth/issues/717#issuecomment-1887451709
    // https://github.com/nextauthjs/next-auth/issues/717#issuecomment-760157910
    const useSecureCookies = process.env.NEXTAUTH_URL.startsWith('https://');
    const csrfProp = `${useSecureCookies ? '__Host-' : ''}next-auth.csrf-token`;
    const cookie = cookies().get(csrfProp);
    if (!cookie) {
      return NextResponse.json({ message: 'Access Denied. Cookie missing.' }, { status: 403 });
    }
    const parsedCsrfTokenAndHash = cookie.value;

    if (!parsedCsrfTokenAndHash) {
      return NextResponse.json({ message: 'Access Denied. CSRF token missing.' }, { status: 403 });
    }
    // delimiter could be either a '|' or a '%7C'
    const tokenHashDelimiter = parsedCsrfTokenAndHash.indexOf('|') !== -1 ? '|' : '%7C';

    const [requestToken, requestHash] = parsedCsrfTokenAndHash.split(tokenHashDelimiter);

    const secret = process.env.NEXTAUTH_SECRET;

    // compute the valid hash
    const validHash = createHash('sha256').update(`${requestToken}${secret}`).digest('hex');

    if (requestHash !== validHash) {
      return NextResponse.json({ message: 'Access Denied. Possible CSRF attack detected.' }, { status: 403 });
    }

    next();
  } else {
    // allow unrestricted method
    next();
  }
};

const isProtected = (req, methods) => {
  if (req.method) {
    return methods.includes(req.method);
  }
  return false;
};
