import {
  ORGANIZ<PERSON>ION_ID,
  BOOKING_WIDGET_ID,
  QUESTIONNAIRE_WIDGET_ID,
  WIDGET_TYPE,
  REGISTRATION_WIDGET_ID,
  QUESTIONNAIRE_ID,
  WIDGET_ID,
} from '../constants';
const params = {
  projectionExpression: 'created_at,updated_at,widget_type,SK,#n',
  expressionAttributeNames: '{"#n":"name"}',
};

// TODO: connect with <PERSON><PERSON><PERSON> to discuss more about the expressionAttributeValues change in widget-services-v2
const encodedProjectionExpression = encodeURIComponent(params.projectionExpression);
const encodedExpressionAttributeNames = encodeURIComponent(params.expressionAttributeNames);
const GET_ALL_WIDGET_QUERY_STRING = `projectionExpression=${encodedProjectionExpression}&expressionAttributeNames=${encodedExpressionAttributeNames}`;
export const GET_ORGANIZATION_DETAILS = `/organization/${ORGANIZATION_ID}`;
export const CREATE_BOOKING_WIDGET = `/${ORGANIZATION_ID}/bookingWidget`;
export const IMPORT_BOOKING_WIDGET = `/${ORGANIZATION_ID}/importBookingWidget`;
// export const GET_ALL_BOOKING_WIDGET = `/${ORGANIZATION_ID}/bookingWidgets?${GET_ALL_WIDGET_QUERY_STRING}`;
export const GET_ALL_BOOKING_WIDGET = `/${ORGANIZATION_ID}/bookingWidgets`;
export const BOOKING_WIDGET_ENDPOINT = `/${ORGANIZATION_ID}/bookingWidget/${BOOKING_WIDGET_ID}`;
export const CREATE_QUESTIONNAIRE_WIDGET = `/${ORGANIZATION_ID}/questionnaireWidget`;
export const IMPORT_QUESTIONNAIRE_WIDGET = `/${ORGANIZATION_ID}/importQuestionnaireWidget`;
export const GET_ALL_QUESTIONNAIRE_WIDGET = `/${ORGANIZATION_ID}/questionnaireWidgets`;
export const QUESTIONNAIRE_WIDGET_ENDPOINT = `/${ORGANIZATION_ID}/questionnaireWidget/${QUESTIONNAIRE_WIDGET_ID}`;
export const CREATE_REGISTRATION_WIDGET = `/${ORGANIZATION_ID}/registrationWidget`;
export const GET_ALL_REGISTRATION_WIDGET = `/${ORGANIZATION_ID}/registrationWidgets`;
export const GET_REGISTRATION_WIDGET = `/${ORGANIZATION_ID}/registrationWidget/${REGISTRATION_WIDGET_ID}`;

export const DELETE_WIDGET = `/${ORGANIZATION_ID}/${WIDGET_TYPE}/${WIDGET_ID}`;

// * Cambian APIs
export const WIDGET_V2_BASE_URL = process.env.NEXT_PUBLIC_WIDGET_BASE_URL;
export const WIDGET_LAMBDA_BASE_URL = process.env.NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL;

export const GET_QUESTIONNAIRE_LIST = `/organizations/${ORGANIZATION_ID}/questionnaires?contentStatus=final&publishStatus=private,public,both`;
export const GET_QUESTIONNAIRE_DETAIL = `/organizations/${ORGANIZATION_ID}/questionnaires/${QUESTIONNAIRE_ID}`;
export const GET_LOCATIONS_LIST = `/organizations/${ORGANIZATION_ID}/locations`;
export const GET_SERVICES_LIST = `/organizations/${ORGANIZATION_ID}/services`;
