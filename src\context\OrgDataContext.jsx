'use client';
import React, { createContext, useContext } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getOrgMetaData } from '@/lib/api/common';
import { ORGANIZATION_METADATA } from '@/lib/constant';

const OrgDataContext = createContext();

export const useOrgData = () => useContext(OrgDataContext);

export const OrgDataProvider = ({ children }) => {
  const orgMetaDataQuery = useQuery({
    queryKey: [ORGANIZATION_METADATA],
    queryFn: () => getOrgMetaData(),
  });

  const organizationName = orgMetaDataQuery.data?.organizationName || '';

  return <OrgDataContext.Provider value={{ organizationName }}>{children}</OrgDataContext.Provider>;
};
