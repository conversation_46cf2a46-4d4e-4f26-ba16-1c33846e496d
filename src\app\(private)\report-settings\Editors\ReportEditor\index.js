import React from 'react';
import ReportTabPanel from './ReportTabPanel';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';

export function ReportEditor({ orgMetaData, handleReportFormSaveCallback }) {
  return (
    <>
      <HeaderStyle>Report Settings</HeaderStyle>
      <PanelBorder sx={{ padding: 2 }}>
        <ReportTabPanel orgMetaData={orgMetaData} handleFormSaveCallback={handleReportFormSaveCallback} />
      </PanelBorder>
    </>
  );
}
