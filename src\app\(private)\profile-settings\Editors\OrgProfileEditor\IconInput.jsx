import { useEffect, useState, useMemo } from 'react';
import { useFormContext } from 'react-hook-form-mui';
import { useController } from 'react-hook-form';
import { Typography, Avatar, Grid, Box } from '@mui/material';
import { Icon<PERSON>utton, Tooltip } from '@mui/material';
import Compress from 'compress.js';
import CloseIcon from '@mui/icons-material/Close';
import EditIcon from '@mui/icons-material/Edit';
import './IconInput.css';

function IconInput({ inputName, accept, maxWidth, useControllerProps }) {
  const { control, setValue } = useFormContext();
  const {
    field: { value, onChange: fieldOnChange },
  } = useController({
    ...useControllerProps,
    name: inputName,
    control,
  });
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const valueImgSrc = useMemo(() => {
    return value ? (typeof value === 'string' ? value : URL.createObjectURL(value)) : null;
  }, [value]);

  useEffect(() => {
    return () => {
      if (valueImgSrc && typeof value !== 'string') {
        URL.revokeObjectURL(valueImgSrc);
      }
    };
  }, [valueImgSrc]);

  const getImageDimensions = (imageSrc) => {
    const img = new Image();
    img.onload = () => {
      setImageDimensions({
        width: img.width,
        height: img.height,
      });
    };
    img.onerror = () => {
      setImageDimensions({ width: 0, height: 0 });
    };
    img.src = imageSrc;
  };

  useEffect(() => {
    if (valueImgSrc) {
      getImageDimensions(valueImgSrc);
    } else {
      setImageDimensions({ width: 0, height: 0 });
    }
  }, [valueImgSrc]);

  const renderAvatar = (image) => (
    <Avatar
      alt="Profile image"
      src={image}
      sx={{
        width: 80,
        height: 80,
        borderRadius: '50%',
      }}
    />
  );

  async function resizeImageFn(file) {
    try {
      if (!file.type.match(/image\/(jpeg|jpg|png|gif)/)) {
        throw new Error('Unsupported file type. Please upload an image (JPEG, JPG, PNG, or GIF).');
      }

      const compress = new Compress();
      const resizedImage = await compress.compress([file], {
        size: 0.2,
        quality: 0.8,
        maxWidth: maxWidth || 300,
        maxHeight: maxWidth || 300,
        resize: true,
      });

      if (!resizedImage?.[0]) {
        throw new Error('Compression failed - no output image');
      }

      const img = resizedImage[0];
      return Compress.convertBase64ToFile(img.data, img.ext);
    } catch (error) {
      return file;
    }
  }

  const handleFileChange = async (event) => {
    if (!event.target.files?.[0]) return;

    const file = event.target.files[0];
    try {
      setValue(inputName, null, { shouldDirty: true });
      const tempUrl = URL.createObjectURL(file);
      getImageDimensions(tempUrl);
      URL.revokeObjectURL(tempUrl);

      const processedFile = file.size > 200000 ? await resizeImageFn(file) : file;

      fieldOnChange(processedFile);
    } catch (error) {
      event.target.value = '';
    }
  };

  const handleRemove = () => {
    setValue(inputName, null, { shouldDirty: true });
    setImageDimensions({ width: 0, height: 0 });
  };

  return (
    <div className="iconinput">
      <Grid item xs={12} sm={12}>
        <Grid container direction="row" justifyContent="space-between" sx={{ paddingBottom: 0, paddingTop: 2 }}>
          <Grid item>
            <Box display="flex" justifyContent="flex-start" style={{ position: 'relative' }}>
              <div style={{ position: 'relative' }}>
                {renderAvatar(valueImgSrc)}
                {valueImgSrc && (
                  <Tooltip title="Remove current file" arrow placement="right">
                    <IconButton
                      aria-label="close"
                      onClick={handleRemove}
                      sx={{
                        position: 'absolute',
                        top: -8,
                        right: -8,
                        bgcolor: 'white',
                        boxShadow: 2,
                        padding: '4px',
                        '&:hover': { bgcolor: 'grey.100' },
                      }}
                      size="small"
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                <label htmlFor={`${inputName}-file-input`} style={{ cursor: 'pointer' }}>
                  <IconButton
                    component="span"
                    sx={{
                      position: 'absolute',
                      bottom: -8,
                      right: -8,
                      bgcolor: 'white',
                      boxShadow: 2,
                      padding: '4px',
                      '&:hover': { bgcolor: 'grey.100' },
                    }}
                    size="small"
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </label>

                <input
                  id={`${inputName}-file-input`}
                  type="file"
                  accept={accept}
                  style={{ display: 'none' }}
                  onChange={handleFileChange}
                />
              </div>
            </Box>
          </Grid>
          <Grid item xs>
            <Box sx={{ pl: 2 }}>
              <Typography sx={{ color: 'text.secondary', fontSize: '12px' }}>Format: PNG, JPG, JPEG, GIF</Typography>
              <Typography sx={{ color: 'text.secondary', fontSize: '12px' }}>
                Recommended: 300 x 300 pixels; &lt; 200kb
              </Typography>
              {imageDimensions.width > 0 && (
                <Typography sx={{ color: 'text.secondary', fontSize: '12px' }}>
                  Current: {imageDimensions.width} x {imageDimensions.height} pixels
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
}

export default IconInput;
