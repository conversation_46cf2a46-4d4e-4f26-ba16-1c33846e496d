import { NextResponse } from 'next/server';
import { CognitoIdentityProviderClient, ConfirmForgotPasswordCommand } from '@aws-sdk/client-cognito-identity-provider';
import { handler } from '@/lib/middleware/handler';

import { generateSecretHash } from '@/lib/auth/cognito';
import AwsError from '@/lib/error/AwsError';
import { csrfProtected } from '@/lib/middleware/csrfProtected';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  const { COGNITO_REGION, COGNITO_ORG_APP_CLIENT_ID } = process.env;
  const { email, newPassword, verificationCode } = await req.json();

  if (!email) {
    return NextResponse.json(
      {
        message: 'email must be provided',
      },
      {
        status: 400,
      },
    );
  }

  const params = {
    ClientId: COGNITO_ORG_APP_CLIENT_ID,
    SecretHash: generateSecretHash(email),
    Username: email,
    ConfirmationCode: verificationCode,
    Password: newPassword,
  };

  const cognitoClient = new CognitoIdentityProviderClient({
    region: COGNITO_REGION,
  });

  const cognitoCommand = new ConfirmForgotPasswordCommand(params);

  try {
    const res = await cognitoClient.send(cognitoCommand);
    const status = res.$metadata.httpStatusCode;

    const response = NextResponse.json({}, { status });

    return response;
  } catch (err) {
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const POST = handler(csrfProtected(['POST']), postEndpoint);
