/**
 * Get messages for the current organization
 * @param {Object} params - Query parameters
 * @param {string} params.type - 'inbox' or 'sent'
 * @param {string} [params.startDate] - Start date in YYYY-MM-DD format
 * @param {string} [params.endDate] - End date in YYYY-MM-DD format
 * @param {string} [params.deliveryMechanism] - Filter by delivery mechanism (EMAIL, SMS, HIE)
 * @param {string} [params.deliveryStatus] - Filter by delivery status (SUCCESS, ERROR)
 * @param {boolean} [params.isRead] - Filter by read status
 * @param {number} [params.limit] - Number of items per page
 * @param {string} [params.lastEvaluatedKey] - Pagination token from previous request
 * @param {string} [params.sortOrder] - Sort order ('ASC' or 'DESC')
 * @param {string} [params.sortBy] - Sort by field ('date' or 'name')
 * @param {string} [params.searchText] - Text to search for in messages
 * @returns {Promise<Object>} Messages data with pagination info
 */
export async function getMessages(params = {}) {
  try {
    // Build query string from params
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });

    console.log('Fetching messages with params:', Object.fromEntries(queryParams.entries()));

    const response = await fetch(`/api/messages?${queryParams.toString()}`, {
      method: 'GET',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch messages');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching messages:', error);
    throw error;
  }
}

/**
 * Update the read status of a message
 * @param {string} messageId - The ID of the message to update
 * @param {boolean} isRead - Whether the message should be marked as read or unread
 * @returns {Promise<Object>} The updated message info
 */
export async function updateMessageReadStatus(messageId, isRead) {
  try {
    const response = await fetch(`/api/messages/${messageId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ isRead }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update message read status');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating message read status:', error);
    throw error;
  }
}
