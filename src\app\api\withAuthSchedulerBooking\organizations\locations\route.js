import { NextResponse } from 'next/server';
import AwsError from '@/lib/error/AwsError';
import { GET_LOCATIONS_LIST } from '@/lib/widget-editor/utils/constants/awsApiEndpoints';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import { NO_STORE } from '@/lib/constant';

const getEndpoint = async (req) => {
  try {
    const url = process.env.NEXT_PUBLIC_SCHEDULER_BOOKING_BASE_URL + GET_LOCATIONS_LIST;
    console.log(url);
    const response = await fetchWithMiddleware(addMachineAccessToken(req), addUserToken(req, { replaceOrgId: true }))(
      url,
      {
        cache: NO_STORE,
      },
    );
    console.log(response);
    if (!response.ok) {
      console.error('Request failed with status:', response.status);
      return NextResponse.json({ message: 'Unauthorized' }, { status: response.status });
    }

    const data = await response.json();

    return NextResponse.json({ status: 200, ...data });
  } catch (err) {
    console.error('Error in getEndpoint:', err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const GET = getEndpoint;
