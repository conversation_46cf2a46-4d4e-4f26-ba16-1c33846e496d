import { isServer } from '@tanstack/react-query';
import makeBrowserQueryClient from './browserClient';
import makeServerQueryClient from './serverClient';

let browserQueryClient = undefined;

export function getQueryClient() {
  if (isServer) {
    // Server: always make a new query client
    return makeServerQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeBrowserQueryClient();
    return browserQueryClient;
  }
}
