import AwsError from '@/lib/error/AwsError';
import { adminUpdateUserEmail } from '@/lib/auth/cognito';

const updateEmailEndpoint = async (req) => {
  try {
    console.log('Received request to update email');
    const { username, newEmail } = await req.json();

    if (!username || !newEmail) {
      console.error('Username and newEmail are required');
      return new Response(JSON.stringify({ success: false, message: 'Username and newEmail are required' }), {
        status: 400,
      });
    }

    console.log('Calling adminUpdateUserEmail with:', { username, newEmail });
    const [updateResult, updateError] = await adminUpdateUserEmail({ username, newEmail });

    if (updateError) {
      console.error('Error updating email in Cognito:', updateError);
      return updateError.toNextResponse();
    }

    console.log('Email successfully updated in Cognito:', updateResult);
    return new Response(JSON.stringify({ success: true }), { status: 200 });
  } catch (err) {
    console.error('Error in updateEmailEndpoint:', err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const POST = updateEmailEndpoint;
