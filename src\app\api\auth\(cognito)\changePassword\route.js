import { NextResponse } from 'next/server';
import { CognitoIdentityProviderClient, ChangePasswordCommand } from '@aws-sdk/client-cognito-identity-provider';
import { handler } from '@/lib/middleware/handler';
import { getToken } from 'next-auth/jwt';
import AwsError from '@/lib/error/AwsError';
import { csrfProtected } from '@/lib/middleware/csrfProtected';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  const { COGNITO_REGION } = process.env;
  const { previousPassword, newPassword } = await req.json();

  const { accessToken } = await getToken({ req });

  if (!accessToken) {
    return NextResponse.json({ message: 'An accessToken is not provided' }, { status: 404 });
  }

  if (!previousPassword || !newPassword) {
    return NextResponse.json(
      {
        message: 'previousPassword, and newPassword must be provided',
      },
      {
        status: 400,
      },
    );
  }

  const params = {
    AccessToken: accessToken,
    PreviousPassword: previousPassword,
    ProposedPassword: newPassword,
  };

  const cognitoClient = new CognitoIdentityProviderClient({
    region: COGNITO_REGION,
  });

  const cognitoCommand = new ChangePasswordCommand(params);

  try {
    const res = await cognitoClient.send(cognitoCommand);
    const status = res.$metadata.httpStatusCode;

    const response = NextResponse.json({}, { status });

    return response;
  } catch (err) {
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

export const POST = handler(csrfProtected(['POST']), postEndpoint);
