NEXT_PUBLIC_BUILD_ENV=dev

NEXT_PUBLIC_NOTIFICATION_MESSAGE=

NEXT_PUBLIC_DEPLOYMENT_HOST=https://coordinator.dev.cambianservices.ca

COGNITO_REGION=ca-central-1

COGNITO_ORG_USER_POOL_ID=ca-central-1_XWHYtgl8d
COGNITO_ORG_APP_CLIENT_ID=40soa9sgis82rpt3ogt59ce2bb
# COGNITO_ORG_APP_CLIENT_SECRET= # Add during build time

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_q6GxwLIsN
COGNITO_ORG_MACHINE_APP_CLIENT_ID=7facb38c9pbms1pfnma7vqugmn
# COGNITO_ORG_MACHINE_APP_CLIENT_SECRET= # Add during build time

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_rPnRLwrid
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=37fo91agquv79k0j7tj66pqffd
# COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET= # Add during build time

# COORDINATOR_ALLOW_COGNITO_ACCESS_IAM_ACCESS_KEY= # Add during build time
# COORDINATOR_ALLOW_COGNITO_ACCESS_IAM_SECRET_KEY= # Add during build time


NEXTAUTH_URL=$NEXT_PUBLIC_DEPLOYMENT_HOST
# NEXTAUTH_SECRET= # Add during build time

#
# AWS Config
#
NEXT_PUBLIC_ORG_REGISTRY_BASE_URL=https://dev.cambianservices.ca/org-registry
NEXT_PUBLIC_PUBLIC_ARTIFACT_REPOSITORY_BASE_URL=https://dev.cambianservices.ca/net-artifact-repo
NEXT_PUBLIC_PRIVATE_ARTIFACT_REPOSITORY_BASE_URL=https://dev.cambianservices.ca/org-artifact-repo
NEXT_PUBLIC_ORGANISATION_DATA_BASE_URL=https://dev.cambianservices.ca/org-data
NEXT_PUBLIC_ORGANISATION_CDR_BASE_URL=https://dev.cambianservices.ca/org-pre-cdr
NEXT_PUBLIC_ORGANISATION_MESSAGING_BASE_URL=https://dev.cambianservices.ca/org-messaging
NEXT_PUBLIC_ORGANISATION_REQUESTS_BASE_URL=https://dev.cambianservices.ca/org-requests


NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://dev.cambianservices.ca/client-index
NEXT_PUBLIC_DOC_GEN_BASE_URL=https://dev.cambianservices.ca/org-doc-gen

NEXT_PUBLIC_SCHEDULER_BOOKING_BASE_URL=https://dev.cambianservices.ca/scheduler-booking

#Activity Tracker
NEXT_PUBLIC_ACTIVITY_TRACKER_SERVICE_BASE_URL=

# Practitioner Index
NEXT_PUBLIC_PRACTITIONER_INDEX_BASE_URL=https://dev.cambianservices.ca/practitioner-index

# Location Index
NEXT_PUBLIC_LOCATION_INDEX_BASE_URL=https://dev.cambianservices.ca/location-index

NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://dev.cambianservices.ca/widget-config
NEXT_PUBLIC_WIDGET_BASE_URL=https://widget.dev.cambianservices.ca
