import * as React from 'react';
import {
  Box,
  Table,
  TableBody,
  TablePagination,
  TableContainer,
  Paper,
  TableRow,
  TableCell,
  Link,
  Snackbar,
  Alert,
  Button,
} from '@mui/material';
import { EnhancedTableHead } from './EnhancedTableHead/EnhancedTableHead';
import { format } from 'date-fns';
import { useClientSearchResults } from '@/context/ClientSearchResults';
import { useRouter } from 'next/navigation.js';
import CircularProgress from '@mui/material/CircularProgress';
import { useTranslation } from 'react-i18next';
import Backdrop from '@mui/material/Backdrop';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID } from '@/lib/constant';

const ResultsTable = (props) => {
  const {
    height,
    queriedTableRecords,
    patientInfo,
    headCells,
    emptyHeadCellCount,
    totalRecords,
    nextPage,
    setNextPage,
    previousPage,
    setPreviousPage,
    selfPage,
    setSelfPage,
    page,
    setPage,
    setQueriedTableRecords,
    setPatientInfo,
    selectedResultsPerPage,
    startDate,
    endDate,
  } = props;
  const [open, setOpen] = React.useState(false);
  const [order, setOrder] = React.useState('desc');
  const [orderBy, setOrderBy] = React.useState('resource.authored');
  const { setViewMode, setSelectedClientId } = useClientSearchResults();
  const [loading, setLoading] = React.useState(false);
  const router = useRouter();
  const [errorSnackbarOpen, setErrorSnackbarOpen] = React.useState(false);
  const [errorSnackbarMessage, setErrorSnackbarMessage] = React.useState('');
  const { t } = useTranslation();
  const [reportsPageLoading, setReportsPageLoading] = React.useState(false);

  function displayErrorMessage(message) {
    setErrorSnackbarOpen(true);
    setErrorSnackbarMessage(message);
  }

  const handleRequestSort = async (event, property) => {
    let newOrder = 'asc';
    if (orderBy === property) {
      newOrder = order === 'asc' ? 'desc' : 'asc';
    } else {
      newOrder = 'asc';
    }

    if (property === 'resource.name') {
      setLoading(true);
      const sortedRecords = [...queriedTableRecords].sort((a, b) => {
        const aValue = headCells[0].getRowValue(property, a) || '';
        const bValue = headCells[0].getRowValue(property, b) || '';
        return newOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      });
      setQueriedTableRecords(sortedRecords);
      setOrder(newOrder);
      setOrderBy(property);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      var start = format(new Date(startDate), 'yyyy-MM-dd');
      var end = format(new Date(endDate), 'yyyy-MM-dd');
      var newOffset = page * selectedResultsPerPage;
      var patientInfoRecords = [];
      var sorting = newOrder === 'asc' ? 'authored' : '-authored';
      let resultsPerPageCount;
      if (selectedResultsPerPage.label && selectedResultsPerPage.label === 'All') {
        resultsPerPageCount = 1000;
      } else {
        resultsPerPageCount = selectedResultsPerPage;
      }
      let queryString =
        '' +
        'authored=ge' +
        start +
        '&' +
        'authored=le' +
        end +
        '&status=completed&_include=QuestionnaireResponse:subject&_include=QuestionnaireResponse:author&_include=QuestionnaireResponse:source&_count=' +
        resultsPerPageCount +
        '&_total=accurate';
      if (sorting) {
        queryString = queryString + '&_sort=' + sorting;
      }
      if (newOffset) {
        queryString = queryString + '&_offset=' + newOffset;
      }

      const response = await fetchNextRoute(
        'organizationCDR',
        `/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse?${queryString}`,
      );
      const dataResponse = await response.json();
      const data = dataResponse;
      var filteredTableRecords = data.entry.filter((record) => {
        if (record.resource.resourceType === 'QuestionnaireResponse') {
          return true;
        } else {
          patientInfoRecords.push(record);
        }
      });

      for (const link of data.link) {
        if (link.relation === 'next') {
          setNextPage(link.url);
        } else if (link.relation === 'previous') {
          setPreviousPage(link.url);
        } else if (link.relation === 'self') {
          setSelfPage(link.url);
        }
      }
      setOrder(newOrder);
      setOrderBy(property);
      setQueriedTableRecords(filteredTableRecords);
      setPatientInfo(patientInfoRecords);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      displayErrorMessage(t('Error : Could not fetch sorted Questionnaire Responses'));
    }
  };

  const handlePageChange = async (event, newPage) => {
    if (page === newPage - 1) {
      const searchParams = new URLSearchParams(nextPage);
      const [offsetSearchParam, offset] = getOffsetAndParam(searchParams);
      if (offset) {
        if (parseInt(offset) === (page + 1) * selectedResultsPerPage) {
          setQueriedResults(nextPage, newPage);
          return;
        } else {
          var newOffset = newPage * selectedResultsPerPage;
          var urlQR = nextPage.replace(offsetSearchParam + '=' + offset, offsetSearchParam + '=' + newOffset);
          setQueriedResults(urlQR, newPage);
        }
      }
    } else if (page === newPage + 1) {
      const searchParams = new URLSearchParams(previousPage);
      const [offsetSearchParam, offset] = getOffsetAndParam(searchParams);
      if (offset) {
        if (parseInt(offset) === (page - 1) * selectedResultsPerPage) {
          setQueriedResults(previousPage, newPage);
          return;
        } else {
          var newOffset = newPage * selectedResultsPerPage;
          var urlQR = previousPage.replace(offsetSearchParam + '=' + offset, offsetSearchParam + '=' + newOffset);
          setQueriedResults(urlQR, newPage);
        }
      }
    }

    if (newPage === 0 && newPage < page - 1) {
      const searchParams = new URLSearchParams(previousPage);
      const [offsetSearchParam, offset] = getOffsetAndParam(searchParams);
      var newOffset = 0;
      var urlQR = previousPage.replace(offsetSearchParam + '=' + offset, offsetSearchParam + '=' + newOffset);
      setQueriedResults(urlQR, newPage);
    } else if (newPage > page + 1) {
      const searchParams = new URLSearchParams(nextPage);
      const [offsetSearchParam, offset] = getOffsetAndParam(searchParams);
      var newOffset = newPage * selectedResultsPerPage;
      var urlQR = nextPage.replace(offsetSearchParam + '=' + offset, offsetSearchParam + '=' + newOffset);
      setQueriedResults(urlQR, newPage);
    }
  };

  const getOffsetAndParam = (searchParams) => {
    var offsetSearchParam = '';
    var offset = '';
    if (searchParams.has('_getpagesoffset')) {
      offsetSearchParam = '_getpagesoffset';
      offset = searchParams.get('_getpagesoffset');
    } else if (searchParams.has('_offset')) {
      offsetSearchParam = '_offset';
      offset = searchParams.get('_offset');
    }
    return [offsetSearchParam, offset];
  };

  const setQueriedResults = async (url, pageNumber) => {
    var patientInfoRecords = [];

    try {
      setLoading(true);
      const response = await fetchNextRoute('organizationCDR', `/organizations/${ORGANIZATION_ID}${url}`);
      const dataResponse = await response.json();
      const data = dataResponse;
      var filteredTableRecords = data.entry.filter((record) => {
        if (record.resource.resourceType === 'QuestionnaireResponse') {
          return true;
        } else {
          patientInfoRecords.push(record);
        }
      });
      for (const link of data.link) {
        let url = link.url;
        const [baseUrl, queryString] = url.split('?');
        if (baseUrl.includes('/fhir/') && queryString && queryString.includes('_getpages')) {
          url = `/fhir?${queryString}`;
        }
        if (link.relation === 'next') {
          setNextPage(url);
        } else if (link.relation === 'previous') {
          setPreviousPage(url);
        } else if (link.relation === 'self') {
          setSelfPage(url);
        }
      }
      setPage(pageNumber);
      setQueriedTableRecords(filteredTableRecords);
      setPatientInfo(patientInfoRecords);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      displayErrorMessage(t('Error : Could not fetch Questionnaire Responses records'));
    }
  };

  const getSubjectName = (id) => {
    for (const patient of patientInfo) {
      if (patient.resource.id === id) {
        if (patient.resource.name && patient.resource.name.length > 0) {
          if (patient.resource.resourceType === 'Location') {
            return patient.resource.name;
          }
          for (const pName of patient.resource.name) {
            return pName.given[0] + ' ' + pName.family;
          }
        }
      }
    }

    return '--';
  };

  const getAuthorName = (row) => {
    if (row.resource.author && row.resource.author.reference) {
      const authorId = row.resource.author.reference.split('/')[1];
      for (const patient of patientInfo) {
        if (patient.resource.id === authorId) {
          if (patient.resource.name && patient.resource.name.length > 0) {
            if (patient.resource.resourceType === 'Location') {
              return patient.resource.name;
            }
            for (const pName of patient.resource.name) {
              return pName.given[0] + ' ' + pName.family;
            }
          }
        }
      }
    }
    return '--';
  };

  const getSourceName = (row) => {
    if (row.resource.source && row.resource.source.reference) {
      const sourceId = row.resource.source.reference.split('/')[1];
      for (const patient of patientInfo) {
        if (patient.resource.id === sourceId) {
          if (patient.resource.name && patient.resource.name.length > 0) {
            if (patient.resource.resourceType === 'Location') {
              return patient.resource.name;
            }
            for (const pName of patient.resource.name) {
              return pName.given[0] + ' ' + pName.family;
            }
          }
        }
      }
    }
    return '--';
  };

  const getClientIdentifier = (id, subject) => {
    let systemUrlsMapping = {
      // PractitionerIndex
      PCN_PHYSICIAN_URL: 'http://cambian.com/pcn/physician',
      ORGANIZATION_USER_URL: 'http://cambian.com/organization/user',
      PRACTITIONER_INDEX_URL: 'http://cambian.com/practitioner-index',
      // ClientIndex
      SCHEDULER_CLIENT_SYSTEM_URL: 'http://cambian.com/scheduler/client',
      CLIENT_SYSTEM_URL: 'http://cambian.com/client',
      // LocationIndex
      LOCATION_INDEX_SYSTEM: 'http://cambian.com/location',
      PCN_LOCATION_SYSTEM: 'http://cambian.com/pcn/location',
    };

    let systemUrlToMatch = '';
    if (subject.includes('Patient')) {
      systemUrlToMatch = systemUrlsMapping['CLIENT_SYSTEM_URL'];
    } else if (subject.includes('Practitioner')) {
      systemUrlToMatch = systemUrlsMapping['PCN_PHYSICIAN_URL'];
    } else if (subject.includes('Location')) {
      systemUrlToMatch = systemUrlsMapping['PCN_LOCATION_SYSTEM'];
    }

    for (const patient of patientInfo) {
      if (patient.resource.id === id) {
        if (patient.resource.identifier) {
          for (const pIdentifier of patient.resource.identifier) {
            if (pIdentifier.system === systemUrlToMatch) {
              return pIdentifier.value;
            }
          }
        }
      }
    }

    return '';
  };

  const onClickSubject = (identifier, subject) => {
    if (identifier) {
      if (subject.includes('Location')) {
        router.push(`/site/${identifier}`);
      } else if (subject.includes('Practitioner')) {
        router.push(`/physicians/${identifier}`);
      } else {
        setSelectedClientId(identifier);
        setViewMode(true);
        router.push(`/clients?client=${identifier}`);
      }
    } else {
      displayErrorMessage(t('Error : Subject does not have an identifier'));
    }
  };

  const onClickAuthor = (identifier, author) => {
    if (identifier) {
      if (author.includes('Location')) {
        router.push(`/site/${identifier}`);
      } else if (author.includes('Practitioner')) {
        router.push(`/physicians/${identifier}`);
      } else {
        setSelectedClientId(identifier);
        setViewMode(true);
        router.push(`/clients?client=${identifier}`);
      }
    } else {
      displayErrorMessage(t('Error : Author does not have an identifier'));
    }
  };

  const onClickSource = (identifier, source) => {
    if (identifier) {
      if (source.includes('Location')) {
        router.push(`/site/${identifier}`);
      } else if (source.includes('Practitioner')) {
        router.push(`/physicians/${identifier}`);
      } else {
        setSelectedClientId(identifier);
        setViewMode(true);
        router.push(`/clients?client=${identifier}`);
      }
    } else {
      displayErrorMessage(t('Error : Source does not have an identifier'));
    }
  };

  const handleClick = (identifier, row) => {
    setReportsPageLoading(true);
    sessionStorage.setItem('selectedRowResource', JSON.stringify(row.resource));
    if (identifier) {
      router.push(`/questionnaire-responses/questionnaire-report?identifier=${identifier}`);
    } else {
      displayErrorMessage(t('Error : Patient does not have client identifier'));
    }
    setReportsPageLoading(false);
  };

  return (
    <Box sx={{ width: '100%' }}>
      {loading && (
        <div>
          <Box sx={{ display: 'flex' }} display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
            <CircularProgress />
          </Box>
        </div>
      )}
      {reportsPageLoading && (
        <div>
          <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}>
            <CircularProgress color="inherit" />
          </Backdrop>
        </div>
      )}
      {!loading && (
        <div>
          <TableContainer component={Paper} height={{ height }} sx={{ height: height ? height : '' }}>
            <Table>
              <EnhancedTableHead
                order={order}
                orderBy={orderBy}
                onRequestSort={handleRequestSort}
                headCells={headCells}
                emptyHeadCellCount={emptyHeadCellCount}
              />
              <TableBody>
                {queriedTableRecords.map((row) => {
                  const cdrIdentifier = row.resource.subject.reference.split('/')[1];
                  return (
                    <TableRow key={row.resource.id}>
                      <TableCell align="left">
                        <Link
                          component="button"
                          variant="body2"
                          sx={{ textAlign: 'left' }}
                          onClick={() => {
                            handleClick(
                              row.resource.subject.reference.includes('Patient')
                                ? getClientIdentifier(cdrIdentifier, row.resource.subject.reference)
                                : cdrIdentifier,
                              row,
                            );
                          }}
                        >
                          {headCells[0].getRowValue(headCells[0].id, row)}
                        </Link>
                      </TableCell>
                      <TableCell align="left">{headCells[1].getRowValue(headCells[1].id, row)}</TableCell>
                      <TableCell
                        align="left"
                        onClick={() => {
                          onClickSubject(
                            getClientIdentifier(cdrIdentifier, row.resource.subject.reference),
                            row.resource.subject.reference,
                          );
                        }}
                      >
                        <Link component="button" variant="body2" sx={{ textAlign: 'left' }} onClick={() => {}}>
                          {getSubjectName(cdrIdentifier)}
                        </Link>
                      </TableCell>
                      <TableCell
                        align="left"
                        onClick={() => {
                          if (row.resource.source && row.resource.source.reference) {
                            const sourceId = row.resource.source.reference.split('/')[1];
                            onClickSource(
                              getClientIdentifier(sourceId, row.resource.source.reference),
                              row.resource.source.reference,
                            );
                          }
                        }}
                      >
                        <Link component="button" variant="body2" sx={{ textAlign: 'left' }} onClick={() => {}}>
                          {getSourceName(row)}
                        </Link>
                      </TableCell>
                      <TableCell
                        align="left"
                        onClick={() => {
                          if (row.resource.author && row.resource.author.reference) {
                            const authorId = row.resource.author.reference.split('/')[1];
                            onClickAuthor(
                              getClientIdentifier(authorId, row.resource.author.reference),
                              row.resource.author.reference,
                            );
                          }
                        }}
                      >
                        <Link component="button" variant="body2" sx={{ textAlign: 'left' }} onClick={() => {}}>
                          {getAuthorName(row)}
                        </Link>
                      </TableCell>
                      <TableCell align="left">{headCells[5].getRowValue(headCells[5].id, row)}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={totalRecords}
            onPageChange={handlePageChange}
            showFirstButton={true}
            showLastButton={true}
            page={page}
            rowsPerPage={selectedResultsPerPage.value ? selectedResultsPerPage.value : selectedResultsPerPage}
            rowsPerPageOptions={[selectedResultsPerPage]}
          />
        </div>
      )}
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        open={errorSnackbarOpen}
        autoHideDuration={6000}
        onClose={() => setErrorSnackbarOpen(false)}
      >
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => setErrorSnackbarOpen(false)}>
              {t('Dismiss')}
            </Button>
          }
        >
          {errorSnackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export { ResultsTable };
