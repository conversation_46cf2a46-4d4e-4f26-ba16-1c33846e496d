import React from 'react';
import { viewSettingsHelperText } from '@/lib/constant';
import { Paper, Typography, IconButton } from '@mui/material';
import { Close } from '@mui/icons-material';

export const ShowHelperTextFile = (props) => {
  const { onClose } = props;

  return (
    <>
      <Paper sx={{ p: 2, position: 'relative' }}>
        <IconButton onClick={onClose} style={{ position: 'absolute', top: '10px', right: '15px' }} size="large">
          <Close fontSize="medium" />
        </IconButton>
        <Typography dangerouslySetInnerHTML={{ __html: viewSettingsHelperText }}></Typography>
      </Paper>
    </>
  );
};
