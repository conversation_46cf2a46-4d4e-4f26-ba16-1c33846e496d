'use server';

import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';
import { ORG_MESSAGING_API_BASE_URL, NO_STORE, ORGANIZATION_ID } from '@/lib/constant';
import ApiError from '@/lib/error/ApiError';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';

export const server_orgMessaging = async (
  deliveryMechanism,
  recipient,
  recipientName,
  subject,
  messageBody,
  messageBodyHtml,
) => {
  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(`${ORG_MESSAGING_API_BASE_URL}/organizations/${ORGANIZATION_ID}/messages`, {
      method: 'POST',
      body: JSON.stringify({
        deliveryMechanism: deliveryMechanism,
        recipientEmailAddress: recipient,
        recipientPhoneNumber: recipient,
        recipientName: recipientName,
        subject: subject,
        messageBody: messageBody,
        messageBodyHtml: messageBodyHtml,
      }),
    });

    if (!res.ok) {
      throw new ApiError({ status: res.status, message: 'Message not sent' });
    }
    console.log('RESPONSE data', res);
    return;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const hie_orgMessaging = async (deliveryMechanism, recipient, narrative, requestData, recipientName) => {
  if (!requestData) {
    throw new ApiError({ status: 400, message: 'Request data is required for HIE messages' });
  }

  try {
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(`${ORG_MESSAGING_API_BASE_URL}/organizations/${ORGANIZATION_ID}/messages/questionnaire-requests`, {
      method: 'POST',
      body: JSON.stringify({
        deliveryMechanism: deliveryMechanism,
        recipientId: recipient,
        narrative: narrative,
        request: requestData,
        recipientName: recipientName,
      }),
    });

    if (!res.ok) {
      throw new ApiError({ status: res.status, message: 'Message not sent' });
    }
    console.log('RESPONSE data', res);
    return;
  } catch (err) {
    console.log(err);
    throw err;
  }
};
