{"Editors": "Editors", "Questionnaires": "Questionnaires", "News": "News", "Articles": "Articles", "Ads": "Ads", "Plans": "Plans", "Templates": "Templates", "Settings": "Settings", "Clients": "Clients", "Views": "Views", "Reports": "Reports", "General": "General", "Home": "Home", "User Management": "User Management", "btnLabel_Submit": "Submit", "btnLabel_Cancel": "Cancel", "btnLabel_Add": "Add", "btnLabel_New": "New", "btnLabel_AddMember": "Add Member", "btnLabel_AddPhysician": "Add Physician/NP", "physicianField_label": "Physician/NP", "Physicians/NP": "Physicians/NPs", "btnLabel_Dismiss": "<PERSON><PERSON><PERSON>", "typeField_label": "Type", "siteField_label": "Site", "btnLabel_Search": "Search", "btnLabel_Reset": "Reset", "btnLabel_Back": "Back", "btnLabel_Continue": "Continue", "btnLabel_ReturnToSearch": "Return to search", "signOut": "Sign Out", "changePassword": "Change Password", "userProfile": "User Profile", "Profile": "Profile", "Widgets": "Widgets", "Users": "Users", "Roles": "Roles", "loading_label": "Loading...", "primaryCareProviderField_label": "Primary Care Provider", "remoteAccessFundedByPCNField_label": "Remote Access Funded By PCN", "emrRemoteAccessField_label": "EMR Remote Access", "measureForField_label": "Measure For:", "areaDetailsField_label": "Area/Details", "doNotShare_label": "Do Not Share", "doNotShareAddress_label": "Do Not Share Address", "noProviderReasonField_label": "If provider unknown, specify reason", "referringMDTField_label": "Referring MDT", "mdtOrTeamMemberField_label": "MDT/Team Members", "mdtMemberField_label": "MDT Member", "teamMemberField_label": "Team Member", "teamMember_label": "Team Members", "dateField_label": "Date", "siteColumn_label": " Site Name", "scheduleColumn_label": "Schedule", "referringProviderField_label": "Referring Provider", "referralReasonField_label": "Referral Reason", "eocSummaryForm_title": "Create Episode of Care", "createPhysician_title": "Create Physician/NP", "prefixField_label": "Prefix", "statusConfigsWarning": "Please select a Status Type.", "referralReasonConfigsWarning": "Please provide a Referral Reason.", "primaryCareProviderConfigsWarning": "Please indicate the Primary Care Provider.", "siteConfigsWarning": "Please choose a Site.", "startDateConfigsWarning": "Please chose a start date.", "referringMDTConfigsWarning": "Please select a Referring MDT.", "referringProviderConfigsWarning": "Please specify a Referral Provider.", "typeConfigsWarning": "Please specify a Type.", "fteConfigsWarning": "Please enter a valid fte value", "dateConfigsWarning": "Please enter a Date.", "providerOrNoProviderWarning": "Please select either a Primary Care Provider or a No Provider Reason.", "episodeOfCareSuccessMessage": "Episode of care created successfully.", "errorMessage": "An error occurred. Please try again.", "successMessage": "Success", "fetchClientDetailsError": "Error: Could not fetch client details.", "fetchDisciplinesError": "Error: Could not fetch Disciplines.", "fetchStatusError": "Error: Could not fetch Status List.", "fetchMdtDetailsError": "Error: Could not fetch Team Member Details.", "fetchMdtSiteAssociationsError": "Error: Could not fetch Team Member Site Associations.", "fetchDisciplineTNAError": "Error: Could not fetch Discipline TNA.", "fetchPrimaryCareProviderDetailsError": "Error: Could not fetch primary care provider details.", "fetchReferringMDTDetailsError": "Error: Could not fetch Referring MDT/Team Member details.", "referralTypesError": "Error: Could not fetch referral types.", "sitesError": "Error: Could not fetch site details.", "statusError": "Error: Could not fetch status details.", "referralReasonsError": "Error: Could not fetch referral reasons.", "referringProvidersError": "Error: Could not fetch referring provider details.", "noProviderReasonsError": "Error: Could not fetch No Provider Reasons.", "mdtOrTeamMemberFetchError": "Error: Could not fetch Team Member Details.", "maxReferralReasonsExceededError": "Error: <PERSON><PERSON> select more than 5 referral reasons.", "addMdtOrTeamToSite": "Add MDT/Team Member To Site", "createMdtOrTeamForm_title": "Create MDT/Team Member", "mdtOrTeamCreateSuccess": "MDT/Team Member created successfully", "createTnaForm_title": "TNA", "disciplineField_label": "Discipline", "genderField_label": "Gender", "languageField_label": "Language", "provinceField_label": "Province", "mailingProvinceField_label": "Mailing Province", "mailingAddressField_label": " Mailing Address", "physicalAddressField_label": "Physical Address", "statusDateField_label": "Status Date", "dateMeasuredField_label": "Date Measured", "mdtMemberSiteAssociationSuccessMessage": "Team Member Site Association created successfully.", "createPhysicianErrorMessage": "Error creating physician/NP.", "createPhysicianSuccessMessage": "Physician/NP created successfully.", "editPhysicianErrorMessage": "Error editing physician/NP.", "editPhysicianSuccessMessage": "Physician/NP details updated successfully.", "mdtErrorMessage": "Error creating MDT.", "noMdtMemberFoundMessage": "No team member found.", "disciplineTnaErrorMessage": "Error creating discipline TNA.", "disciplineTnaSuccessMessage": "Discipline TNA created successfully.", "programTnaErrorMessage": "Error creating program TNA.", "programTnaSuccessMessage": "Program TNA created successfully.", "siteContactSuccessMessage": "Site Contact created successfully.", "siteContactErrorMessage": "Error creating Site Contact.", "createSiteContactForm_title": "Create Site Contact", "editSiteContactForm_title": "Edit Site Contact", "editSiteContactAssociationForm_title": "Edit Site Contact Association", "closingEoc": "Closing Episode Of Care", "searchMdtMembersTitle": "Search Team Members", "searchResultsText": "Search Results", "scheduleSuccessMessage": "Schedule created successfully.", "scheduleErrorMessage": "Error creating Schedule.", "editScheduleSuccessMessage": "Schedule changed successfully.", "editScheduleErrorMessage": "Error changing Schedule.", "createScheduleForm_title": "Create Schedule", "editScheduleForm_title": "Edit Schedule", "searchByFirstName": "Filter By First Name", "searchByMiddleName": "Filter By Middle Name", "searchByLastName": "Filter By Last Name", "siteContactsText": "Site Contacts", "mdtSchedulesText": "MDT/Team Member Schedules", "nameField_label": "Name", "rolesField_label": "Roles", "primaryPhoneField_label": "Primary Phone", "secondaryPhoneField_label": "Secondary Phone", "primaryEmailField_label": "Primary Email", "mailingAddressLine1Field_label": "Mailing Address Line 1", "mailingAddressLine2Field_label": "Mailing Address Line 2", "mailingAddressCityField_label": "Mailing Address City", "mailingAddressPostalCodeField_label": "Mailing Address Postal Code", "specialtyCaseloadField_label": "Specialty Caseload", "secondaryEmailField_label": "Secondary Email", "membershipTypeField_label": "Membership Type", "membershipStatusField_label": "Membership Status", "filterByPCNJoinedDate_label": "Filter by PC<PERSON> Join <PERSON>", "fetchSiteContactsError": "Error: Could not fetch list of Site Contacts", "fetchSiteContactAssociationError": "Error: Could not fetch Site Contact Association", "fetchRolesError": "Error: Could not fetch Roles", "openedDateField_label": "Opened Date", "closedDateField_label": "Closed Date", "ownershipError": "Error: Could not fetch ownership types.", "searchPhysicianTitle": "Search For Physician/NP", "paymentError": "Error: Could not fetch payment methods.", "paymentMethodField_label": "Payment Method", "bankingInfoField_label": "Banking Info", "startDateField_label": "Start Date", "endDateField_label": "End Date", "selectDateField_label": "Select Date", "submittedDateField_label": "Submitted Date", "opicApprovedDateField_label": "OIPC Approved Date", "statusStartDateField_label": "Status Start Date", "statusEndDateField_label": "Status End Date", "bankingInfoError": "Error: Could not fetch banking info.", "privacyReportError": "Error: Could not fetch privacy reports.", "openField_label": "Open", "closeField_label": "Close", "selectTypeField_label": "Select Type", "selectProgramField_label": "Select Program", "programField_label": "Program", "selectServiceField_label": "Select Service", "legalFirstNameField_label": "Legal First Name", "legalMiddleNameField_label": "Legal Middle Name", "legalLastNameField_label": "Legal Last Name", "preferredFirstNameField_label": "Preferred First Name", "preferredLastNameField_label": "Preferred Last Name", "legalNameField_label": "Legal Name", "preferredNameField_label": "Preferred Name", "pcnJoinDate_label": "PCN Join Date", "startDate_label": "Start Date", "endDate_label": "End Date", "general_label": "General", "address_label": "Address", "membership_label": "Membership", "otherDetails_label": "Other Details", "contactInfo_label": "Contact Information", "schedule_label": "Schedule", "memberSiteAssociation_label": "Team Member Site Association", "memberSiteSchedule_label": "Team Member Site Schedule", "memberSiteAssociationError_label": "No Member Site Associations Found.", "siteName_label": "Site Name", "searchBySiteName": "Filter By Site Name", "searchBySiteOwnership": "Filter By Site Ownership", "searchBySiteStatus": "Filter By Site Status", "sites": "Sites", "fetchSiteError": "Error: Could not fetch Site", "fetchSitesError": "Error: Could not fetch list of Sites", "ownershipField_label": "Ownership", "longAppointmentField_label": "Long / Initial Appointment", "shortAppointmentField_label": "Short / Follow Up Appointment", "intakeAppointmentField_label": "Intake Appointment", "thirdAppointmentField_label": "Date of Third Next Appt", "mySitesField_label": "My Sites", "daysField_label": "Days", "statusField_label": "Status", "leadershipPositionField_label": "Leadership Position", "specialistField_label": "Specialist", "practiceIdField_label": "Practice ID", "physicianSpecificNumberField_label": "Physician Specific Number", "contactCommentsField_label": "Contact Comments", "membershipCommentsField_label": "Membership Comments", "areaOfInterestField_label": "Area of Interest", "reasonForLeavingField_label": "Reason For Leaving", "pcnJoinDateField_label": "PCN Join Date", "AHJoinDateField_label": "<PERSON><PERSON> Join Date", "addLanguage_label": "Add New Language", "addAreaOfInterest_label": "Add New Area Of Interest", "addSpecialtyCaseload_label": "Add New Specialty Caseload", "fieldRequired_warning": "This field is required", "physicianNotFound": "No matching Physician/NP found", "acceptingNewPatientsField_label": "Accepting New Patients", "leadAtSite_label": "Lead At Site", "primarySite_label": "Primary Site", "fullTimeAtSite_label": "Full Time At Site", "isSiteBoardMember_label": "Is Site Board Member?", "isPhysicianLeader_label": "Is Physician Leader?", "physicianSiteModeField_label": "Mode", "emrSpecialistField_label": "EMR Specialist", "pfPlIfField_label": "Practice Facilitator / Physician Liaison / IF", "panelManagerField_label": "Panel Manager", "commentField_label": "Comment", "emrField_label": "EMR", "primaryFaxField_label": "Primary Fax", "secondaryFaxField_label": "Secondary Fax", "addPhysicianSite_title": "Add Physician/NP To Site", "physicianSiteDetails": "Physician/NP Site Details", "emrUse_label": "EMR Used For:", "billing_label": "Billing", "charting_label": "Charting", "scheduling_label": "Scheduling", "addPhysicianToSiteSuccessMessage": "Successfully Added Site", "editPhysicianSiteSuccessMessage": "Successfully Updated Physician/NP Site Details", "editPhysicianSiteErrorMessage": "Error Updating Site", "addPhysicianToSiteErrorMessage": "Error Adding Site", "fetchPhysicianSiteErrorMessage": "Error Fetching Physician/NP Site Association", "noMatchingSite": "No Site Found.", "searchField_label": "Search", "typeSearchField_label": "Type to search or select", "addSite_label": "Add Site", "FT/PT": "FT/PT", "fixFormErrors": "Please review and fix errors", "addPhysicianToSite_label": "Add Physician/NP To Site", "btnLabel_Edit": "Edit", "mdtUserTooltip": "An MDT/Team Member must be a user in the system. If you cannot find the name of the User you want to create as a Member, please create their user account first in the Users tab under settings.", "mdtUserEmailTooltip": "An MDT/Team Member must be a user in the system, and their email address should match their user account. If you wish to change this, please change the email for their user account first in the Users tab under settings.", "emrTooltip": "To remove an EMR, set an End Date or uncheck use cases.", "eocTypeTooltip": "Select who has sent the referral to the PCN", "unlinkedActivityTooltip": "This client has unlinked activities.", "problemsDropdownTooltip": "Start typing to filter options.", "selectSiteTooltip": "Please select a site to view options.", "clinicStaffTooltip": "List of contacts at site", "aggregateEncounterNumPatientsError": "At least 2 patients are required for an Aggregate Encounter. Link at least two clients to proceed.", "addEncounterLabel": "+ Encounter", "technicalError": "Unknown technical error. Please contact technical support.", "Cancel": "Cancel", "Start Date": "Start Date", "End Date": "End Date", "Lead At Site": "Lead At Site", "Primary Site": "Primary Site", "Full Time At Site": "Full Time At Site", "Is Site Board Member?": "Is Site Board Member?", "Is Physician Leader?": "Is Physician Leader?", "Contact": "Contact", "Primary Phone": "Primary Phone", "Do Not Share": "Do Not Share", "Secondary Phone": "Secondary Phone", "Primary Fax": "Primary Fax", "Secondary Fax": "Secondary Fax", "EMR": "EMR", "Billing": "Billing", "Charting": "Charting", "Scheduling": "Scheduling", "Comments": "Comments", "Mode": "Mode", "EMR Specialist": "EMR Specialist", "Practice Facilitator / Physician Liaison / IF": "Practice Facilitator / Physician Liaison / IF", "Panel Manager": "Panel Manager", "Copyright © Cambian Business Services, Inc.": "Copyright © Cambian Business Services, Inc.", "Help": "Help", "Physician/NP Site Details": "Physician/NP Site Details", "Other Details": "Other Details", "Site": "Site", "Accepting New Patients": "Accepting New Patients", "Contact Details": "Contact Details", "No": "No", "Yes": "Yes", "Search": "Search", "Add Site to Physician": "Add Site to Physician", "Site Name": "Site Name", "+ Add Response": "+ Add Response", "Dismiss": "<PERSON><PERSON><PERSON>", "No questionnaire response data": "No questionnaire response data", "Add Physician/NP To Site": "Add Physician/NP To Site"}