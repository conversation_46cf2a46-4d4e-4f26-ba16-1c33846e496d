'use client';

// https://tanstack.com/query/latest/docs/framework/react/guides/advanced-ssr#initial-setup
import { MutationCache, QueryCache, QueryClient } from '@tanstack/react-query';
import { enqueueSnackbar, closeSnackbar } from 'notistack';
import { Typography, IconButton } from '@mui/material';
import { Clear } from '@mui/icons-material';

export default function makeBrowserQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
        retry: 1, // At a development phase, our endpoints are faulty. Only retry once. Default is 3.
      },
      // dehydrate: {
      //   // This is to streaming with server component https://tanstack.com/query/latest/docs/framework/react/guides/advanced-ssr#streaming-with-server-components
      //   // per default, only successful Queries are included,
      //   // this includes pending Queries as well
      //   shouldDehydrateQuery: (query) => defaultShouldDehydrateQuery(query) || query.state.status === 'pending',
      // },
    },
    // Global onError callbacks for query and mutation
    queryCache: new QueryCache({
      onError: (error, query) => {
        console.error('Client/React Query Error:', error);
        const queryErrorMessage = query.meta?.errorMessage;
        if (queryErrorMessage) {
          displayErrorMessage(queryErrorMessage);
        } else {
          displayErrorMessage(error.message);
        }
      },
    }),
    mutationCache: new MutationCache({
      onError: (error, _variables, _context, mutation) => {
        console.error('Client/React Mutation Error:', error);
        const mutationErrorMessage = mutation.meta?.errorMessage;
        if (mutationErrorMessage) {
          displayErrorMessage(mutationErrorMessage);
        } else {
          displayErrorMessage(error.message);
        }
      },
    }),
  });
}
function displayErrorMessage(message = 'Something went wrong') {
  enqueueSnackbar(<Typography sx={{ pl: 1 }}>{message}</Typography>, {
    variant: 'error',
    autoHideDuration: 6000,
    action: (key) => (
      <IconButton
        onClick={() => {
          closeSnackbar(key);
        }}
      >
        <Clear fontSize="small" sx={{ color: '#fff' }} />
      </IconButton>
    ),
  });
}
