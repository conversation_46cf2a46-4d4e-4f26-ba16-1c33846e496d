'use client';
import React, { useEffect, useState, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { QuestionnaireReportViewerV2 } from '@cambianrepo/questionnaire';
import { CircularProgress, Box, Grid } from '@mui/material';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';
import { useQuestionnaire } from '@/context/QuestionnaireContext';
import CreateAndEditClientView from '@/app/(private)/clients/[id]/view/page';
import useNotification from '@/lib/hooks/useNotification';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID } from '@/lib/constant';

const QuestionnaireReport = (props) => {
  const { fhirQuestionnaireResponse, subjectType, clientInfoAvailable } = props;
  const {
    selectedQuestionnaire,
    setSelectedQuestionnaire,
    selectedQuestionnaireResponse,
    setSelectedQuestionnaireResponse,
    docGenerated,
    setDocGenerated,
  } = useQuestionnaire();
  const [loadingPDF, setLoadingPDF] = useState(true);
  const searchParams = useSearchParams();
  const identifier = searchParams.get('identifier') || props.identifier;
  const openSnackbar = useNotification();
  const [hasError, setHasError] = useState(false);
  const [row, setRow] = useState(() => JSON.parse(sessionStorage.getItem('selectedRowResource')));
  const [demographic, setDemographic] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    phn: '',
    dateOfBirth: '',
    address: '',
    email: '',
    phone: '',
    gender: '',
  });

  const prevIdentifierRef = useRef(null);
  const prevRowRef = useRef(null);
  let baseUrl = process.env.NEXT_PUBLIC_DEPLOYMENT_HOST;

  useEffect(() => {
    const fetchQuestionnaireData = async () => {
      // identifier is only required for Patients to fetch patient details
      if (!identifier || (!row && !fhirQuestionnaireResponse)) {
        console.log('Identifier or row not provided');
        return;
      }

      if (!fhirQuestionnaireResponse && prevIdentifierRef.current === identifier && prevRowRef.current === row) {
        return;
      }

      prevIdentifierRef.current = identifier;
      prevRowRef.current = row;

      try {
        let questionnaireResponse;
        if (row) {
          questionnaireResponse = await fetchNextRoute(
            'organizationCDR',
            `/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse/${row.id}`,
          );
          questionnaireResponse = await questionnaireResponse.json();
        } else {
          questionnaireResponse = fhirQuestionnaireResponse;
        }
        let questionnaireId = row ? row.questionnaire : fhirQuestionnaireResponse.questionnaire;
        if (questionnaireId.includes('/')) {
          questionnaireId = questionnaireId.split('/').pop();
        }
        const response = await fetchNextRoute(
          'organizationCDR',
          `/organizations/${ORGANIZATION_ID}/fhir/Questionnaire/${questionnaireId}`,
        );
        const dataResponse = await response.json();
        const data = dataResponse;
        setSelectedQuestionnaireResponse(questionnaireResponse.dataResponse || questionnaireResponse);
        setSelectedQuestionnaire(data);
        if (subjectType === 'Patient') {
          const clientDataResponse = await fetchClientDataAndSetDemographic();
          // currently we can only fetch pdf for patients/clients
          await fetchPdfIfAvailable(clientDataResponse, questionnaireResponse, data);
        }
      } catch (err) {
        setHasError(true);
        openSnackbar({
          variant: 'error',
          msg: 'Failed to fetch Questionnaire Report',
        });
        console.error('Error fetching data:', err.message);
      } finally {
        setLoadingPDF(false);
      }
    };

    fetchQuestionnaireData();
  }, [
    identifier,
    row,
    fhirQuestionnaireResponse,
    setSelectedQuestionnaire,
    setSelectedQuestionnaireResponse,
    setDocGenerated,
  ]);

  // only needed for clients
  const fetchClientDataAndSetDemographic = async () => {
    const clientDataResponse = await fetchNextRoute('clientIndex', `/${ORGANIZATION_ID}/clients/${identifier}`);

    const clientData = clientDataResponse;
    let clientPrimaryHealthCareId = '';
    let clientPrimaryAddress = '';
    let clientPrimaryEmailAddress = '';
    let clientPrimaryPhoneNumber = '';
    const healthCareIds = clientData.healthCareIds;

    for (let i = 0; i < healthCareIds.length; i++) {
      if (healthCareIds[i].primary) {
        clientPrimaryHealthCareId = healthCareIds[i].type || '' + ' ' + healthCareIds[i].value || '';
        break;
      }
    }

    const addresses = clientData.addresses;
    for (let i = 0; i < addresses.length; i++) {
      if (addresses[i].primary) {
        clientPrimaryAddress =
          addresses[i].address1 ||
          '' + ', ' + addresses[i].address2 ||
          '' + ', ' + addresses[i].city ||
          '' + ', ' + addresses[i].province ||
          '' + ', ' + addresses[i].postalCode ||
          '' + ', ' + addresses[i].country ||
          '';
        break;
      }
    }

    const emailAddresses = clientData.emailAddresses;
    for (let i = 0; i < emailAddresses.length; i++) {
      if (emailAddresses[i].primary) {
        clientPrimaryEmailAddress = emailAddresses[i].emailAddress || '';
        break;
      }
    }

    const phoneNumbers = clientData.phoneNumbers;
    for (let i = 0; i < phoneNumbers.length; i++) {
      if (phoneNumbers[i].primary) {
        clientPrimaryPhoneNumber = phoneNumbers[i].phoneNumber || '';
        break;
      }
    }

    setDemographic({
      firstName: clientData.firstName,
      middleName: clientData.middleName,
      lastName: clientData.lastName,
      phn: clientPrimaryHealthCareId,
      dateOfBirth: clientData.dateOfBirth,
      address: clientPrimaryAddress,
      email: clientPrimaryEmailAddress,
      phone: clientPrimaryPhoneNumber,
      gender: clientData.gender,
    });
    return clientDataResponse;
  };

  const fetchPdfIfAvailable = async (clientDataResponse, questionnaireResponse, questionnaireData) => {
    const pdfTemplateExtension = questionnaireData.extension?.find((ext) => ext.url === 'pdftemplate-base64');
    const pdfTemplateBase64 = pdfTemplateExtension?.valueBase64Binary || null;
    let formData = {
      client_data: clientDataResponse.dataResponse,
      questionnaire_response_data: questionnaireResponse.dataResponse || questionnaireResponse,
      binary_data: pdfTemplateBase64,
    };

    if (formData.client_data && formData.questionnaire_response_data && formData.binary_data) {
      setLoadingPDF(true);
      const retrievePDFDocumentUrl = `${baseUrl}/api/questionnaireResponses/retrievePDFDocument`;
      const pdfResponse = await fetch(retrievePDFDocumentUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ formData }),
      });

      const pdfResponseData = await pdfResponse.json();
      setDocGenerated(pdfResponseData.dataResponse.content);
    } else {
      setDocGenerated('');
      console.log('No PDF version available');
    }
  };
  return (
    <>
      {!fhirQuestionnaireResponse && <HeaderStyle>Questionnaire Report</HeaderStyle>}
      <PanelBorder sx={!fhirQuestionnaireResponse ? { border: '1px solid #ccc' } : { border: 'none' }}>
        <Box>
          <Grid container>
            {subjectType === 'Patient' && !clientInfoAvailable && (
              <Grid item xs={12} sx={{ paddingTop: 1 }}>
                <CreateAndEditClientView params={{ id: identifier }} />
              </Grid>
            )}
            <Grid item xs={12} sx={{ paddingTop: 2, paddingLeft: 2, paddingRight: 2 }}>
              {loadingPDF ? (
                <Box sx={{ display: 'flex' }} justifyContent="center" alignItems="center" minHeight="50vh">
                  <CircularProgress />
                </Box>
              ) : (
                !hasError && (
                  <QuestionnaireReportViewerV2
                    fhirQuestionnaire={selectedQuestionnaire}
                    fhirResponse={selectedQuestionnaireResponse}
                    isWebReportAvailable={true}
                    pdf={docGenerated}
                    demographic={demographic}
                    browserTimezone={Intl.DateTimeFormat().resolvedOptions().timeZone}
                  />
                )
              )}
            </Grid>
          </Grid>
        </Box>
      </PanelBorder>
    </>
  );
};

export default QuestionnaireReport;
