'use client';

import { useEffect, useState } from 'react';
import { OrgRoleEditor } from './OrgRoleEditor';
import { fetchNextRoute } from '@/lib/api/services/clientFetchRoutes';
import { ORGANIZATION_ID } from '@/lib/constant';

function ClientPage() {
  const [roleNameData, setRoleNameData] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [isRightColumnVisible, setIsRightColumnVisible] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(null);

  const handleItemClickCallback = (selectedRoleId) => {
    const role = roleNameData.find((item) => item.id === selectedRoleId);
    setSelectedRole(role);
    setIsRightColumnVisible(true);
  };

  const handleButtonClickCallback = () => {
    setSelectedRole({ id: undefined, name: '', features: [] });
    setIsRightColumnVisible(true);
  };

  const handleDeleteCallback = () => {
    setSelectedRole(null);
    updateRoleList();
    setIsRightColumnVisible(false);
  };

  const fetchFeaturesData = async () => {
    try {
      const responseData = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}`);
      const data = await responseData.json();
      return data.features || [];
    } catch (error) {
      console.error('Error fetching data from API!:', error.message || error);
      return [];
    }
  };

  const fetchRoleFeaturesData = async () => {
    try {
      const responseData = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/roles`);
      const data = await responseData.json();
      const matchedRole = data.roles.find((role) => role.id === selectedRole.id) || { features: [] };
      return matchedRole.features || [];
    } catch (error) {
      console.error('Error fetching role features from API:', error.message || error);
      return [];
    }
  };

  const updateRoleList = async () => {
    try {
      const updatedRolesResponse = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/roles`);
      if (updatedRolesResponse.ok) {
        const updatedRolesData = await updatedRolesResponse.json();
        setRoleNameData(updatedRolesData.roles || []);
      } else {
        console.error('Failed to fetch updated roles list:', updatedRolesResponse.status);
      }
    } catch (error) {
      console.error('Error fetching data from API!!:', error.message || error);
    }
  };

  useEffect(() => {
    const fetchRoleName = async () => {
      try {
        const responseData = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/roles`);
        const data = await responseData.json();
        const roles = data.roles || [];
        setRoleNameData(roles);
      } catch (error) {
        console.error('Error fetching data from /organizations/${ORGANIZATION_ID}/roles API:', error.message || error);
      }
    };

    fetchRoleName();
  }, []);

  const handleFormSaveCallback = async ({ allValues, dirtyValues, selectedRole }) => {
    try {
      let responseData;
      let updatedRole;

      if (!selectedRole || !selectedRole.id) {
        responseData = await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/roles`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: allValues.name,
            features: allValues.features,
          }),
        });
      } else {
        responseData = await fetchNextRoute(
          'organizationData',
          `/organizations/${ORGANIZATION_ID}/roles/${selectedRole.id}`,
          {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name: allValues.name,
              features: allValues.features,
            }),
          },
        );
      }

      console.log(!selectedRole || !selectedRole.id ? 'Role Added Successfully!' : 'The role was successfully saved');

      await updateRoleList();

      if (!selectedRole || !selectedRole.id) {
        const responseDataJson = await responseData.json();
        updatedRole = {
          ...selectedRole,
          id: responseDataJson.roleId,
          ...dirtyValues,
          name: allValues.name,
        };
      } else {
        const responseDataJson = await responseData.json();
        updatedRole = { ...selectedRole, ...responseDataJson, ...dirtyValues, name: allValues.name };
      }

      setSelectedRole(updatedRole);
      setIsRightColumnVisible(true);
    } catch (error) {
      console.error('Error fetching data from API?:', error.message || error);
    }
  };

  const proceedWithDelete = async () => {
    try {
      await fetchNextRoute('organizationData', `/organizations/${ORGANIZATION_ID}/roles/${selectedRole.id}`, {
        method: 'DELETE',
      });
      updateRoleList();
      handleDeleteCallback();
    } catch (error) {
      console.error('Error deleting role from API:', error.message || error);
    }
  };

  useEffect(() => {
    const newIndex = roleNameData.findIndex((role) => role.id === selectedRole?.id);
    setSelectedIndex(newIndex);
  }, [roleNameData, selectedRole]);

  return (
    <OrgRoleEditor
      listData={roleNameData}
      roleNameData={roleNameData}
      selectedRole={selectedRole}
      setSelectedRole={setSelectedRole}
      fetchFeaturesData={fetchFeaturesData}
      fetchRoleFeaturesData={fetchRoleFeaturesData}
      handleItemClickCallback={handleItemClickCallback}
      handleButtonClickCallback={handleButtonClickCallback}
      handleFormSaveCallback={handleFormSaveCallback}
      proceedWithDelete={proceedWithDelete}
      isRightColumnVisible={isRightColumnVisible}
      setIsRightColumnVisible={setIsRightColumnVisible}
      selectedIndex={selectedIndex}
      setSelectedIndex={setSelectedIndex}
    />
  );
}

export default ClientPage;
