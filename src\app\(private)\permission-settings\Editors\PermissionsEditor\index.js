import React from 'react';
import FeaturesPanel from './FeaturesPanel';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';

export function PermissionsEditor({ orgMetaData, allFeaturesList, handleFeaturesFormSaveCallback }) {
  return (
    <>
      <HeaderStyle>Permission Settings</HeaderStyle>
      <PanelBorder sx={{ padding: 2 }}>
        <FeaturesPanel
          orgFeaturesList={orgMetaData?.features}
          allFeaturesList={allFeaturesList}
          handleFormSaveCallback={handleFeaturesFormSaveCallback}
        />
      </PanelBorder>
    </>
  );
}
