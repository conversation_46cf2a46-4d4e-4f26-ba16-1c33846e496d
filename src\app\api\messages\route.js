import { NextResponse } from 'next/server';
import { ORG_MESSAGING_API_BASE_URL } from '@/lib/constant';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import { getToken } from 'next-auth/jwt';
import AwsError from '@/lib/error/AwsError';

export async function GET(request) {
  try {
    // Get the token which contains organization ID
    const token = await getToken({ req: request });
    const organizationId = token.orgId;

    if (!organizationId) {
      return NextResponse.json({ message: 'Unauthorized: Organization ID not found in session' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const queryParams = new URLSearchParams(url.search);

    // Log query parameters for debugging
    console.log('API route query parameters:', Object.fromEntries(queryParams.entries()));

    // Build the API URL
    const apiUrl = `${ORG_MESSAGING_API_BASE_URL}/organizations/${organizationId}/messages?${queryParams.toString()}`;
    console.log('Calling organization messaging service at:', apiUrl);

    // Use the middleware pattern with proper token handling
    const response = await fetchWithMiddleware(
      addMachineAccessToken(request),
      addUserToken(request, { replaceOrgId: true }),
    )(apiUrl, {
      method: 'GET',
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const responseData = await response.json();
    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error in messages API route:', error);
    const awsError = new AwsError(error);
    return awsError.toNextResponse();
  }
}
