import { NextResponse } from 'next/server';
import { NETWORK, ORGANIZATION } from '@/lib/constant';
import AwsError from '@/lib/error/AwsError';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';

export const fetchFromOrganizationApi = async (req, url, options = {}) => {
  try {
    const response = await fetchWithMiddleware(
      addMachineAccessToken(req, ORGANIZATION),
      addUserToken(req, { replaceOrgId: true, replaceOrgUserId: true }),
    )(url, options);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('fFOA', { errorText });
      const error = new Error(errorText);
      error.status = response.status;
      throw error;
    }

    const responseData = await response.json();
    console.log('fFOA', { responseData });
    return NextResponse.json(responseData, { status: response.status });
  } catch (error) {
    console.error('fFOA Error from:', url, error);
    return NextResponse.json(error.message, { status: error.status });
  }
};

export const fetchFromNetworkApi = async (req, url, options = {}) => {
  try {
    const response = await fetchWithMiddleware(
      addMachineAccessToken(req, NETWORK),
      addUserToken(req, { replaceOrgId: true, replaceOrgUserId: true }),
    )(url, options);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('fFNA', { errorText });
      const error = new Error(errorText);
      error.status = response.status;
      throw error;
    }

    const responseData = await response.json();
    console.log('fFNA', { responseData });
    return NextResponse.json(responseData, { status: response.status });
  } catch (error) {
    console.error('fFNA Error from:', url, error);
    return NextResponse.json(error.message, { status: error.status });
  }
};
