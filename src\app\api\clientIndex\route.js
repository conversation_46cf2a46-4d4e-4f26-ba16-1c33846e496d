import { fetchFromOrganizationApi } from '@/app/api/utility';
import { CLIENT_INDEX_BASE_URL } from '@/lib/constant';

const baseURL = CLIENT_INDEX_BASE_URL;

export const GET = async (request) => {
  const { searchParams } = new URL(request.url);
  const endpoint = searchParams.get('endpoint');
  console.log('clientIndex Next route GET', { endpoint });

  const response = await fetchFromOrganizationApi(request, `${baseURL}${endpoint}`, {
    method: 'GET',
  });
  return response;
};

export const PUT = async (request) => {
  const { searchParams } = new URL(request.url);
  const endpoint = searchParams.get('endpoint');
  console.log('clientIndex Next route PUT', { endpoint });
  const requestBody = await request.json();

  const response = await fetchFromOrganizationApi(request, `${baseURL}${endpoint}`, {
    method: 'PUT',
    body: JSON.stringify(requestBody),
  });
  return response;
};

export const POST = async (request) => {
  const { searchParams } = new URL(request.url);
  const endpoint = searchParams.get('endpoint');
  console.log('clientIndex Next route POST', { endpoint });
  const requestBody = await request.json();

  const response = await fetchFromOrganizationApi(request, `${baseURL}${endpoint}`, {
    method: 'POST',
    body: JSON.stringify(requestBody),
  });
  return response;
};

export const DELETE = async (request) => {
  const { searchParams } = new URL(request.url);
  const endpoint = searchParams.get('endpoint');
  console.log('clientIndex Next route DELETE', { endpoint });

  const response = await fetchFromOrganizationApi(request, `${baseURL}${endpoint}`, {
    method: 'DELETE',
  });
  return response;
};
