'use server';

import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';
import {
  ADS,
  ARTICLES,
  CONSENT_AGREEMENTS,
  NETWORK,
  NEWS_ITEMS,
  NO_STORE,
  ORGANI<PERSON>ATION_ID,
  ORGANI<PERSON>ATION,
  PLANS,
  PRIVATE,
  QUESTIONNAIRES,
} from '@/lib/constant';
import ApiError from '@/lib/error/ApiError';
import CustomError from '@/lib/error/CustomError';
import { addMachineAccessToken, addUserToken, fetchWithMiddleware } from '@/lib/fetch/server';
import { getArtifactRepoUrlByVisibility } from '@/lib/utility';

const validArtifactTypesForListApi = [QUESTIONNAIRES, NEWS_ITEMS, ARTICLES, PLANS, ADS];
const validArtifactTypesForSingleArtifactApi = [QUESTIONNAIRES, NEWS_ITEMS, ARTICLES, PLANS, ADS, CONSENT_AGREEMENTS];

const validateArtifactType = (artifactType, validArtifactTypes) => {
  if (!validArtifactTypes.includes(artifactType)) {
    throw new CustomError({ message: 'Unsupported Artifact Type', status: 400 });
  }
};

export const server_getArtifactListByVisibility = async ({ artifactType, visibility, queryStringParams }) => {
  const BASE_URL = getArtifactRepoUrlByVisibility(visibility);
  if (!BASE_URL) {
    throw new CustomError({ status: 400, message: 'visibility should be private or public' });
  }

  validateArtifactType(artifactType, validArtifactTypesForListApi);

  const { contentStatus, publishStatuses } = queryStringParams || {};
  const queryStrings = [];
  if (contentStatus) {
    queryStrings.push(`contentStatus=${contentStatus}`);
  }
  if (publishStatuses && publishStatuses.length > 0) {
    queryStrings.push(`publishStatus=${publishStatuses.join(',')}`);
  }

  const finalQueryString = queryStrings.length > 0 ? queryStrings.join('&') : '';

  try {
    const env = visibility === PRIVATE ? ORGANIZATION : NETWORK;
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions, env),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(`${BASE_URL}/organizations/${ORGANIZATION_ID}/${artifactType}?${finalQueryString}`, {
      cache: NO_STORE,
    });
    if (res.status !== 200) {
      throw new ApiError({
        status: res.status,
        message: await res.json(),
      });
    }
    const data = await res.json();
    return data;
  } catch (error) {
    console.error('failed to get artifact list');
    throw error;
  }
};

export const server_getArtifact = async ({ visibility, artifactType, artifactId, includeMetadata }) => {
  const BASE_URL = getArtifactRepoUrlByVisibility(visibility);
  if (!BASE_URL) {
    throw new CustomError({ status: 400, message: 'visibility should be private or public' });
  }
  validateArtifactType(artifactType, validArtifactTypesForSingleArtifactApi);

  try {
    const env = visibility === PRIVATE ? ORGANIZATION : NETWORK;
    const res = await fetchWithMiddleware(
      addMachineAccessToken(authOptions, env),
      addUserToken(authOptions, { replaceOrgId: true }),
    )(
      `${BASE_URL}/organizations/${ORGANIZATION_ID}/${artifactType}/${artifactId}${includeMetadata ? '?includeMetadata=true' : ''}`,
      {
        cache: NO_STORE,
      },
    );
    const data = await res.json();
    if (res.status !== 200) {
      throw new ApiError({
        status: res.status,
        message: data,
      });
    }
    return data;
  } catch (error) {
    console.error('failed to get artifact');
    throw error;
  }
};
