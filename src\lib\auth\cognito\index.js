import {
  CognitoIdentityProviderClient,
  Admin<PERSON><PERSON><PERSON><PERSON>ser<PERSON>ommand,
  AssociateSoftwareTokenCommand,
  VerifySoftwareTokenCommand,
  Initiate<PERSON><PERSON><PERSON>ommand,
  Admin<PERSON><PERSON>UserCommand,
  RespondToAuthChallengeCommand,
  AdminUpdateUserAttributesCommand,
  RevokeTokenCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { createHmac } from 'crypto';
import AwsError from '@/lib/error/AwsError';
import CustomError from '@/lib/error/CustomError';
import { MFA_SETUP, SOFTWARE_TOKEN_MFA, NEW_PASSWORD_REQUIRED } from './constant';
import { HUMAN, NETWORK, ORGANIZATION } from '@/lib/constant';

const {
  COGNITO_REGION,
  COGNITO_ORG_USER_POOL_ID,
  COGNITO_ORG_APP_CLIENT_ID,
  COGNITO_ORG_APP_CLIENT_SECRET,
  COGNITO_ORG_MACHINE_USER_POOL_ID,
  COGNITO_ORG_MACHINE_APP_CLIENT_ID,
  COGNITO_ORG_MACHINE_APP_CLIENT_SECRET,
  COGNITO_NETWORK_MACHINE_USER_POOL_ID,
  COGNITO_NETWORK_MACHINE_APP_CLIENT_ID,
  COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET,
} = process.env;

const cognitoClientWithIAM = new CognitoIdentityProviderClient({
  region: COGNITO_REGION,
  credentials: {
    accessKeyId: process.env.COORDINATOR_ALLOW_COGNITO_ACCESS_IAM_ACCESS_KEY,
    secretAccessKey: process.env.COORDINATOR_ALLOW_COGNITO_ACCESS_IAM_SECRET_KEY,
  },
});

const cognitoClient = new CognitoIdentityProviderClient({
  region: COGNITO_REGION,
});

export const setUpMfa = async ({ session }) => {
  try {
    if (!session) {
      return [null, new CustomError({ status: 500, message: 'AccessToken must be provided when setting up MFA' })];
    }
    const command = new AssociateSoftwareTokenCommand({
      Session: session,
    });
    const response = await cognitoClient.send(command);
    const status = response.$metadata.httpStatusCode;
    if (status === 200) {
      return [response, null];
    }
    return [null, new CustomError({ status, message: 'Something went wrong when setting up MFA QR code' })];
  } catch (error) {
    return [null, new AwsError(error)];
  }
};

export const parseIdToken = (idToken, accessToken, refreshToken) => {
  // Decode idToken to extract claims
  const cognitoIDTokenParts = idToken.split('.');
  const claimsBuff = Buffer.from(cognitoIDTokenParts[1], 'base64');
  const cognitoIDTokenClaims = JSON.parse(claimsBuff.toString('utf8'));

  // TODO: names are no longer saved in Cognito. Need to retrieve them from Org Data service.
  return {
    orgId: cognitoIDTokenClaims['custom:org_id'],
    orgUserId: cognitoIDTokenClaims['cognito:username'],
    email: cognitoIDTokenClaims.email,
    idToken,
    accessToken,
    refreshToken,
  };
};

export const confirmMfaSetup = async ({ session, verificationCode }) => {
  const params = {
    Session: session,
    UserCode: verificationCode,
  };
  try {
    const command = new VerifySoftwareTokenCommand(params);
    const response = await cognitoClient.send(command);

    const status = response.$metadata.httpStatusCode;
    if (status === 200) {
      return [{ session: response.Session }, null];
    }
    return [null, new Error(`Something went wrong with status ${status}`)];
  } catch (error) {
    console.log('VERIFY ERROF', error);
    return [null, new AwsError(error)];
  }
};

export const signInWithMfa = async ({ username, session, verificationCode, challengeName }) => {
  const respondToChallengeParams = {
    ClientId: process.env.COGNITO_ORG_APP_CLIENT_ID,
    UserPoolId: COGNITO_ORG_USER_POOL_ID,
    ChallengeName: challengeName,
    ChallengeResponses: {
      SECRET_HASH: generateSecretHash(username),
      USERNAME: username.toLowerCase(),
    },
  };

  if (challengeName === MFA_SETUP) {
    const [newRes, error] = await confirmMfaSetup({ verificationCode, session });
    if (error) {
      return [null, error];
    }
    respondToChallengeParams.Session = newRes.session;
  } else if (challengeName === SOFTWARE_TOKEN_MFA) {
    respondToChallengeParams.ChallengeResponses.SOFTWARE_TOKEN_MFA_CODE = verificationCode;
    respondToChallengeParams.Session = session;
  } else {
    return [null, new CustomError({ status: 400, message: 'Unsupported challenge type' })];
  }

  try {
    const command = new RespondToAuthChallengeCommand(respondToChallengeParams);
    const response = await cognitoClient.send(command);

    if (response.$metadata.httpStatusCode === 200) {
      const parsedResponse = parseIdToken(
        response.AuthenticationResult.IdToken,
        response.AuthenticationResult.AccessToken,
        response.AuthenticationResult.RefreshToken,
      );
      return [{ ...parsedResponse, expires_in: response.AuthenticationResult.ExpiresIn }, null];
    }
    return [
      null,
      new CustomError({ status: 500, message: 'Something went wrong while responding to auth challenge.' }),
    ];
  } catch (cognitoError) {
    return [null, new AwsError(cognitoError)];
  }
};

export const newPasswordRequiredResponse = async ({ session, userId, newPassword }) => {
  const respondToChallengeParams = {
    ClientId: COGNITO_ORG_APP_CLIENT_ID,
    UserPoolId: COGNITO_ORG_USER_POOL_ID,
    ChallengeName: NEW_PASSWORD_REQUIRED,
    Session: session,
    ChallengeResponses: {
      NEW_PASSWORD: newPassword,
      USERNAME: userId,
      SECRET_HASH: generateSecretHash(userId),
    },
  };
  try {
    const res = await cognitoClient.send(new RespondToAuthChallengeCommand(respondToChallengeParams));
    if (res.$metadata.httpStatusCode === 200) {
      return [res, null];
    }
    return [null, new CustomError({ status, message: response.toString() })];
  } catch (cognitoError) {
    return [null, new AwsError(cognitoError)];
  }
};

export const initiateSignInWithCredentials = async ({
  username,
  password,
  userPoolId = COGNITO_ORG_USER_POOL_ID,
  appClientId = COGNITO_ORG_APP_CLIENT_ID,
  appClientSecret = COGNITO_ORG_APP_CLIENT_SECRET,
  isEdgeRuntime = false,
}) => {
  if (username.includes('@')) {
    username = sanitizeEmailAddressForCognito(username);
  }
  const params = {
    AuthFlow: 'USER_PASSWORD_AUTH',
    ClientId: appClientId,
    UserPoolId: userPoolId,
    AuthParameters: {
      USERNAME: username,
      PASSWORD: password,
    },
  };
  if (isEdgeRuntime) {
    params.AuthParameters.SECRET_HASH = await generateSecretHashForEdgeRuntime(username, appClientId, appClientSecret);
  } else {
    params.AuthParameters.SECRET_HASH = generateSecretHash(username, appClientId, appClientSecret);
  }
  console.log('params sign in ', params);
  try {
    const initiateAuthCommand = new InitiateAuthCommand(params);
    const response = await cognitoClient.send(initiateAuthCommand);

    const status = response.$metadata.httpStatusCode;

    if (status === 200) {
      return [response, null];
    }
    return [null, new CustomError({ status, message: response.toString() })];
  } catch (cognitoError) {
    return [null, new AwsError(cognitoError)];
  }
};

export const adminCreateUser = async ({ username, emailAddress, phoneNumber, orgId }) => {
  if (!username || !(phoneNumber || emailAddress)) {
    return [
      null,
      new CustomError({
        status: 400,
        message: 'email or phone_number needs to be provided',
      }),
    ];
  }

  let userAttributes = [
    {
      Name: 'custom:org_id',
      Value: orgId,
    },
  ];
  let desiredDeliveryMediums = [];

  if (emailAddress !== '') {
    desiredDeliveryMediums.push('EMAIL');
    userAttributes.push(
      {
        Name: 'email_verified',
        Value: 'True',
      },
      {
        Name: 'email',
        Value: sanitizeEmailAddressForCognito(emailAddress),
      },
    );
  }
  if (phoneNumber !== '') {
    // desiredDeliveryMediums.push('SMS'); // can't do this for now because our cognito does not have sms set up
    userAttributes.push(
      {
        Name: 'phone_number_verified',
        Value: 'True',
      },
      {
        Name: 'phone_number',
        Value: phoneNumber,
      },
    );
  }

  const params = {
    DesiredDeliveryMediums: desiredDeliveryMediums,
    UserPoolId: COGNITO_ORG_USER_POOL_ID,
    Username: username.toLowerCase(),
    UserAttributes: userAttributes,
  };

  const cognitoCommand = new AdminCreateUserCommand(params);

  try {
    let res = await cognitoClientWithIAM.send(cognitoCommand);
    return [
      {
        status: res.$metadata.httpStatusCode,
      },
      null,
    ];
  } catch (cognitoError) {
    console.error(cognitoError);
    return [null, new AwsError(cognitoError)];
  }
};

export const getAttributeValue = (attributes, attributeName) => {
  const attribute = attributes.find((attr) => attr.Name === attributeName);
  return attribute ? attribute.Value : null;
};

export const adminDeleteUser = async ({ username }) => {
  const params = {
    UserPoolId: COGNITO_ORG_USER_POOL_ID,
    Username: username.toLowerCase(),
  };
  const command = new AdminDeleteUserCommand(params);
  try {
    const res = await cognitoClientWithIAM.send(command);

    return [
      {
        status: res.$metadata.httpStatusCode,
      },
      null,
    ];
  } catch (cognitoError) {
    return [null, new AwsError(cognitoError)];
  }
};

export const sanitizeEmailAddressForCognito = (emailAddress) => emailAddress.trim().toLowerCase();

/**
 * @param username
 */
export const generateSecretHash = (
  username,
  appClientId = COGNITO_ORG_APP_CLIENT_ID,
  appClientSecret = COGNITO_ORG_APP_CLIENT_SECRET,
) => {
  const hasher = createHmac('sha256', appClientSecret);
  hasher.update(`${username.toLowerCase()}${appClientId}`);
  const res = hasher.digest('base64');
  return res;
};

/**
 *
 * Next.js Middleware is using an edge runtime and thus not have all the node.js APIs.
 * Thus, we need to use Web Crypto API instead of Node Crypto API.
 *
 * @param {*} username
 * @param {*} appClientId
 * @param {*} appClientSecret
 * @returns
 *
 */
export const generateSecretHashForEdgeRuntime = async (
  username,
  appClientId = COGNITO_ORG_APP_CLIENT_ID,
  appClientSecret = COGNITO_ORG_APP_CLIENT_SECRET,
) => {
  const message = new TextEncoder().encode(username.toLowerCase() + appClientId);
  const secretKey = new TextEncoder().encode(appClientSecret);

  // Generate HMAC SHA-256
  const keyObj = await crypto.subtle.importKey('raw', secretKey, { name: 'HMAC', hash: { name: 'SHA-256' } }, false, [
    'sign',
  ]);

  const hashBytes = await crypto.subtle.sign('HMAC', keyObj, message);

  // Convert hash to base64
  const hashBase64 = btoa(String.fromCharCode(...new Uint8Array(hashBytes)));
  return hashBase64;
};

export const getMachineAccessTokenForEnv = async (env) => {
  'use server';

  if (env !== NETWORK && env !== ORGANIZATION) {
    return [null, new CustomError({ status: 400, message: 'env is either network or organization' })];
  }

  let params = {};

  if (env === NETWORK) {
    const networkMachineCredentials = JSON.parse(process.env.COGNITO_NETWORK_MACHINE_CREDENTIALS || '{}');

    params = {
      username: networkMachineCredentials.username || '',
      password: networkMachineCredentials.password,
      userPoolId: COGNITO_NETWORK_MACHINE_USER_POOL_ID,
      appClientId: COGNITO_NETWORK_MACHINE_APP_CLIENT_ID,
      appClientSecret: COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET,
    };
  } else {
    const orgMachineCredentials = JSON.parse(process.env.COGNITO_ORG_MACHINE_CREDENTIALS || '{}');

    params = {
      username: orgMachineCredentials.username || '',
      password: orgMachineCredentials.password,
      userPoolId: COGNITO_ORG_MACHINE_USER_POOL_ID,
      appClientId: COGNITO_ORG_MACHINE_APP_CLIENT_ID,
      appClientSecret: COGNITO_ORG_MACHINE_APP_CLIENT_SECRET,
    };
  }
  params.isEdgeRuntime = true;

  const [res, error] = await initiateSignInWithCredentials(params);
  if (error) {
    console.log(`Failed authenticating to ${env} machine userpool`);
    console.log(error);
    return [{ env }, error];
  }
  const { AccessToken: accessToken, RefreshToken: refreshToken, ExpiresIn: expiresIn } = res?.AuthenticationResult;

  return [{ accessToken, refreshToken, expiresIn, env }, null];
};

export const refreshTokenAuth = async ({ cognitoPool, refreshToken, userId }) => {
  let params = {};
  console.log('Attempting to refresh', cognitoPool);
  switch (cognitoPool) {
    case HUMAN:
      console.log('REFRESHING HUMAN TOKENS');
      params = {
        AuthFlow: 'REFRESH_TOKEN_AUTH',
        ClientId: COGNITO_ORG_APP_CLIENT_ID,
        UserPoolId: COGNITO_ORG_USER_POOL_ID,
        AuthParameters: {
          REFRESH_TOKEN: refreshToken,
          SECRET_HASH: generateSecretHash(userId),
        },
      };
      break;
    case ORGANIZATION:
      console.log('REFRESHING ORGANIZATION TOKENS');
      params = {
        AuthFlow: 'REFRESH_TOKEN_AUTH',
        ClientId: COGNITO_ORG_MACHINE_APP_CLIENT_ID,
        UserPoolId: COGNITO_ORG_MACHINE_USER_POOL_ID,
        AuthParameters: {
          REFRESH_TOKEN: refreshToken,
        },
      };
      params.AuthParameters.SECRET_HASH = await generateSecretHashForEdgeRuntime(
        userId,
        COGNITO_ORG_MACHINE_APP_CLIENT_ID,
        COGNITO_ORG_MACHINE_APP_CLIENT_SECRET,
      );
      break;
    case NETWORK:
      console.log('REFRESHING NETWORK TOKENS');
      params = {
        AuthFlow: 'REFRESH_TOKEN_AUTH',
        ClientId: COGNITO_NETWORK_MACHINE_APP_CLIENT_ID,
        UserPoolId: COGNITO_NETWORK_MACHINE_USER_POOL_ID,
        AuthParameters: {
          REFRESH_TOKEN: refreshToken,
        },
      };
      params.AuthParameters.SECRET_HASH = await generateSecretHashForEdgeRuntime(
        userId,
        COGNITO_NETWORK_MACHINE_APP_CLIENT_ID,
        COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET,
      );
      break;
    default:
      return [
        null,
        new CustomError({
          status: 400,
          message: `cognitoPool needs to be either ${HUMAN}, ${ORGANIZATION}, or ${NETWORK}`,
          cognitoPool,
        }),
      ];
  }

  try {
    const res = await cognitoClient.send(new InitiateAuthCommand(params));
    if (res.$metadata.httpStatusCode === 200) {
      console.log('Refreshed', cognitoPool);
      return [res, null, cognitoPool];
    }
    return [null, new CustomError({ status: res.$metadata.httpStatusCode, message: res.toString() }), cognitoPool];
  } catch (cognitoError) {
    return [null, new AwsError({ ...cognitoError }), cognitoPool];
  }
};

export const revokeToken = async (params) => {
  const { refreshToken, clientId, clientSecret } = params;
  if (!refreshToken) {
    return { error: null };
  }

  const command = new RevokeTokenCommand({
    Token: refreshToken,
    ClientId: clientId,
    ClientSecret: clientSecret,
  });

  try {
    const res = await cognitoClient.send(command);
    console.log('revokeToken()', { params, res });
    console.log('!!Refresh token revoked successfully.');
    return { error: null };
  } catch (cognitoError) {
    return { error: new AwsError(cognitoError) };
  }
};

export const adminUpdateUserEmail = async ({ username, newEmail }) => {
  const params = {
    UserPoolId: COGNITO_ORG_USER_POOL_ID,
    Username: username.toLowerCase(),
    UserAttributes: [
      {
        Name: 'email',
        Value: sanitizeEmailAddressForCognito(newEmail),
      },
      {
        Name: 'email_verified',
        Value: 'True',
      },
    ],
  };

  console.log('Cognito Update Email Params:', JSON.stringify(params, null, 2));

  try {
    const command = new AdminUpdateUserAttributesCommand(params);
    console.log('Sending AdminUpdateUserAttributesCommand...');
    const response = await cognitoClientWithIAM.send(command);

    console.log('Cognito Update Email Response:', JSON.stringify(response, null, 2));

    if (response.$metadata.httpStatusCode === 200) {
      return [response, null];
    }
    return [
      null,
      new CustomError({ status: response.$metadata.httpStatusCode, message: 'Failed to update email in Cognito' }),
    ];
  } catch (error) {
    console.error('Cognito Update Email Error:', error);
    return [null, new AwsError(error)];
  }
};
