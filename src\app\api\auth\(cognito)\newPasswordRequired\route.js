import { NextResponse } from 'next/server';
import { handler } from '@/lib/middleware/handler';
import { newPasswordRequiredResponse, setUpMfa } from '@/lib/auth/cognito';
import { csrfProtected } from '@/lib/middleware/csrfProtected';
import { MFA_SETUP, SOFTWARE_TOKEN_MFA } from '@/lib/auth/cognito/constant';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  const { session: sessionToken, userId, newPassword, usernameForMfa } = await req.json();

  if (!newPassword) {
    return NextResponse.json(
      {
        message: 'newPassword must be provided',
      },
      {
        status: 400,
      },
    );
  }

  const [res, error] = await newPasswordRequiredResponse({
    session: sessionToken,
    userId,
    newPassword,
  });

  if (error) {
    return error.toNextResponse();
  }

  const { ChallengeName: challengeName, Session: session } = res;

  switch (challengeName) {
    case MFA_SETUP:
      // When the user signs in for the very first time, provide QR code to set up user's authenticator app
      const [mfaResponse, mfaError] = await setUpMfa({ session });
      if (mfaError) {
        return mfaError.toNextResponse();
      }
      const mfaUrl = mfaResponse.SecretCode
        ? `otpauth://totp/${process.env.NEXT_PUBLIC_MFA_APP_NAME}:${usernameForMfa}?secret=${mfaResponse.SecretCode}`
        : undefined;
      return NextResponse.json({ session: mfaResponse.Session, mfaUrl, challengeName: MFA_SETUP }, { status: 200 });

    case SOFTWARE_TOKEN_MFA:
      return NextResponse.json({ session: session, challengeName: SOFTWARE_TOKEN_MFA }, { status: 200 });

    default:
      return NextResponse.json(
        {
          message: 'MFA is not enabled in this userpool',
        },
        {
          status: 500,
        },
      );
  }
};

export const PUT = handler(postEndpoint);
