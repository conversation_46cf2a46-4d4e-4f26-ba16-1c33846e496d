'use client';

import React from 'react';
import SelectableMenuList from './SelectableMenuList';
import { TwoColumnPage } from '@cambianrepo/ui';
import UserEditor from './UserEditor';
import { HeaderStyle } from '@cambianrepo/ui';

export function OrgUserEditor({
  usersData,
  rolesData,
  selectedUserId,
  selectedUserInfo,
  handleItemClickCallback,
  isRightColumnVisible,
  handleButtonClickCallback,
  handleUserFormSaveCallback,
  deleteUserCallback,
}) {
  return (
    <>
      <HeaderStyle>User Settings</HeaderStyle>
      <TwoColumnPage
        leftColumn={
          <SelectableMenuList
            headerButtonText="New User"
            usersData={usersData}
            getItemName={(item) => `${item.firstName} ${item.lastName}`}
            getItemId={(item) => item.id}
            handleItemClickCallback={handleItemClickCallback}
            handleButtonClickCallback={handleButtonClickCallback}
            selectedUserId={selectedUserId}
          />
        }
        rightColumn={
          isRightColumnVisible && (
            <UserEditor
              selectedUserId={selectedUserId}
              selectedUser={selectedUserInfo}
              rolesData={rolesData}
              handleUserFormSaveCallback={handleUserFormSaveCallback}
              deleteUserCallback={deleteUserCallback}
            />
          )
        }
      />
    </>
  );
}
