'use client';

// Because the callbacks for this component are invoked in Client Component's event handlers, we can invoke server action functions inside these callbacks.
import React, { useState } from 'react';
import { Widgets } from '@cambianrepo/widget-editor-v2';
import {
  prepareMapPlaceholderImage,
  BOOKING_CAPS,
  BOOKING_WIDGET_QUERY_KEY,
  QUESTIONNAIRE_CAPS,
  QUESTIONNAIRE_WIDGET_QUERY_KEY,
  REG<PERSON><PERSON>ATION_CAPS,
  REGISTRATION_WIDGET_QUERY_KEY,
} from '@/lib/widget-editor/utils/constants';
import { WIDGET_V2_BASE_URL } from '@/lib/widget-editor/utils/constants/awsApiEndpoints';
import {
  downloadFileInJsonFormat,
  extractGUID,
  getWidgetURL,
  sortWidgets,
  getImageBase64DataFromPresignedUrl,
} from '@/lib/utility';
import useNotification from '@/lib/hooks/useNotification';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  server_deleteWidget,
  server_createBookingWidget,
  server_importBookingWidget,
  server_createQuestionnaireWidget,
  server_createRegistrationWidget,
  server_fetchBookingWidgetsList,
  server_fetchQuestionnaireWidgetsList,
  server_fetchRegistrationWidgetsList,
  server_fetchBookingWidgetById,
  server_fetchQuestionnaireWidgetById,
  server_fetchRegistrationWidgetById,
} from '@/actions/widgetConfig';
import { useMutation, useQuery, useQueries, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogTitle, DialogContent, DialogActions, Typography, Button } from '@mui/material';
import { getOrgSettings } from '@/lib/api/orgData';
import { ORGANIZATION_SETTINGS } from '@/lib/constant';

export default function ClientPage({}) {
  const openSnackbar = useNotification();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { data: session } = useSession();
  const { user } = session || {};
  const orgId = user.orgId;
  const orgSettingsQuery = useQuery({ queryKey: [ORGANIZATION_SETTINGS], queryFn: getOrgSettings });
  const [deleteWarningOpen, setDeleteWarningOpen] = useState(false);
  const [widgetToDelete, setWidgetToDelete] = useState(null);

  const allWidgetsQueries = useQueries({
    queries: [
      {
        queryKey: [QUESTIONNAIRE_WIDGET_QUERY_KEY],
        queryFn: () => server_fetchQuestionnaireWidgetsList(),
        enabled: !!orgSettingsQuery.data,
        meta: {
          errorMessage: 'Failed to retrieve questionnaire widgets',
        },
      },
      {
        queryKey: [REGISTRATION_WIDGET_QUERY_KEY],
        queryFn: () => server_fetchRegistrationWidgetsList(),
        enabled: !!orgSettingsQuery.data,
        meta: {
          errorMessage: 'Failed to retrieve registration widgets',
        },
      },
      {
        queryKey: [BOOKING_WIDGET_QUERY_KEY],
        queryFn: () => server_fetchBookingWidgetsList(),
        enabled: !!orgSettingsQuery.data,
        meta: {
          errorMessage: 'Failed to retrieve booking widgets',
        },
      },
    ],
    combine: (results) => {
      if (!orgSettingsQuery.data) return { data: [] };
      const mergedWidgets = results.map((result) => result.data).flat(1);
      const sortedWidgets = sortWidgets(mergedWidgets, 'name');
      return {
        data: sortedWidgets,
        //pending: results.some((result) => result.isPending),
      };
    },
  });

  const createQuestionnaireWidgetMutation = useMutation({
    mutationFn: ({ widgetData, isImport }) => server_createQuestionnaireWidget(widgetData, isImport),
    onSuccess: (data, _variables, _context) => {
      queryClient.setQueryData([QUESTIONNAIRE_WIDGET_QUERY_KEY], (oldData) => [...oldData, data]);
      console.log('Questionnaire widget created successfully');
      return { success: true };
    },
    meta: {
      errorMessage: 'Failed to create questionnaire widget',
    },
  });

  const importBookingWidgetMutation = useMutation({
    mutationFn: (widgetData) => {
      const preparedWidgetData = prepareMapPlaceholderImage(widgetData);
      return server_importBookingWidget(preparedWidgetData);
    },
    onSuccess: (data, _variables, _context) => {
      queryClient.setQueryData([BOOKING_WIDGET_QUERY_KEY], (oldData) => [...oldData, data]);
      console.log('Booking widget created successfully');
      return { success: true };
    },
    meta: {
      errorMessage: 'Failed to create booking widget',
    },
  });

  const createBookingWidgetMutation = useMutation({
    mutationFn: (widgetData) => server_createBookingWidget(widgetData),
    onSuccess: (data, _variables, _context) => {
      queryClient.setQueryData([BOOKING_WIDGET_QUERY_KEY], (oldData) => [...oldData, data]);
      console.log('Booking widget created successfully');
      return { success: true };
    },
    meta: {
      errorMessage: 'Failed to create booking widget',
    },
  });

  const createRegistrationWidgetMutation = useMutation({
    mutationFn: (widgetData) => server_createRegistrationWidget(widgetData),
    onSuccess: (data, _variables, _context) => {
      queryClient.setQueryData([REGISTRATION_WIDGET_QUERY_KEY], (oldData) => [...oldData, data]);
      console.log('Registration widget created successfully');
      return { success: true };
    },
    meta: {
      errorMessage: 'Failed to create registration widget',
    },
  });

  const deleteWidgetMutation = useMutation({
    mutationFn: ({ widgetId, widgetType }) => {
      server_deleteWidget(widgetId, widgetType);
    },
    onSuccess: (_data, variables, _context) => {
      const { widgetId, widgetType } = variables;
      switch (widgetType) {
        case BOOKING_CAPS:
          var widgetSK = `BOOKING_WIDGET#${widgetId}`;
          queryClient.setQueryData([BOOKING_WIDGET_QUERY_KEY], (oldData) =>
            oldData.filter((widget) => widget.SK !== widgetSK),
          );
        case QUESTIONNAIRE_CAPS:
          var widgetSK = `QUESTIONNAIRE_WIDGET#${widgetId}`;
          queryClient.setQueryData([QUESTIONNAIRE_WIDGET_QUERY_KEY], (oldData) =>
            oldData.filter((widget) => widget.SK !== widgetSK),
          );
        case REGISTRATION_CAPS:
          var widgetSK = `REGISTRATION_WIDGET#${widgetId}`;
          queryClient.setQueryData([REGISTRATION_WIDGET_QUERY_KEY], (oldData) =>
            oldData.filter((widget) => widget.SK !== widgetSK),
          );
      }
      console.log('Widget deleted successfully');
      return { success: true };
    },
    meta: {
      errorMessage: 'Failed to delete widget',
    },
  });

  const getWidgetByTypeWithReactQuery = async (widgetId, widgetType) => {
    switch (widgetType) {
      case BOOKING_CAPS:
        return await queryClient.fetchQuery({
          queryKey: [BOOKING_WIDGET_QUERY_KEY, widgetId],
          queryFn: () => server_fetchBookingWidgetById(widgetId),
          meta: {
            errorMessage: 'Unable to retrieve selected booking widget',
          },
        });

      case QUESTIONNAIRE_CAPS:
        return await queryClient.fetchQuery({
          queryKey: [QUESTIONNAIRE_WIDGET_QUERY_KEY, widgetId],
          queryFn: () => server_fetchQuestionnaireWidgetById(widgetId),
          meta: {
            errorMessage: 'Unable to retrieve selected questionnaire widget',
          },
        });
      case REGISTRATION_CAPS:
        return await queryClient.fetchQuery({
          queryKey: [REGISTRATION_WIDGET_QUERY_KEY, widgetId],
          queryFn: () => server_fetchRegistrationWidgetById(widgetId),
          meta: {
            errorMessage: 'Unable to retrieve selected registration widget',
          },
        });
    }
  };

  const canDeleteWidget = async (widgetData, widgetType) => {
    const widgetId = extractGUID(widgetData?.SK);
    if (widgetType === QUESTIONNAIRE_CAPS && orgSettingsQuery?.data.dynamicWidgetId === widgetId) {
      setWidgetToDelete({ widgetData, widgetType });
      setDeleteWarningOpen(true);
      return false;
    }
    return true;
  };

  const handleDeleteWidgetCallback = async (widgetData, widgetType) => {
    const widgetId = extractGUID(widgetData?.SK);
    openSnackbar({
      msg: `Deleting ${widgetType.toLowerCase()} widget "${widgetData.name}..."`,
    });
    if (widgetType !== BOOKING_CAPS && widgetType !== QUESTIONNAIRE_CAPS && widgetType !== REGISTRATION_CAPS) {
      console.log('Invalid widget type');
      return { success: false, error: 'Invalid widget type' };
    }

    deleteWidgetMutation.mutate({ widgetId, widgetType });
  };

  const handleCancelDelete = () => {
    setDeleteWarningOpen(false);
    setWidgetToDelete(null);
  };

  const handleImportWidgetCallback = async (widgetData, widgetType) => {
    console.log('import clicked');

    openSnackbar({
      msg: `Importing ${widgetType} widget ${widgetData.name}...`,
    });
    let response;
    switch (widgetType) {
      case BOOKING_CAPS:
        response = await importBookingWidgetMutation.mutateAsync(widgetData);
        break;

      case QUESTIONNAIRE_CAPS:
        response = await createQuestionnaireWidgetMutation.mutateAsync({ widgetData, isImport: true });
        break;

      case REGISTRATION_CAPS:
        response = await createRegistrationWidgetMutation.mutateAsync(widgetData);
        break;

      default:
        console.log('Invalid widget type');
        response = { success: false, error: 'Invalid widget type' };
        break;
    }

    return response;
  };

  const handlePreviewWidgetCallback = async (widgetId, widgetType) => {
    console.log('preview clicked');
    const base_url = WIDGET_V2_BASE_URL;
    let widgetData = await getWidgetByTypeWithReactQuery(widgetId, widgetType);
    if (!widgetData) {
      console.error('Could not fetch widget data for preview');
      return;
    }
    let dynamicWidget = widgetData.dynamicWidget || false;
    const widgetURL = getWidgetURL(base_url, orgId, widgetId, widgetType, dynamicWidget);
    window.open(widgetURL);
  };

  const handleExportWidgetCallback = async (widgetId, widgetType) => {
    console.log('Export clicked');
    try {
      openSnackbar({
        msg: `Exporting ${widgetType.toLowerCase()} widget...`,
      });
      const widgetData = await getWidgetByTypeWithReactQuery(widgetId, widgetType);
      const fileName = widgetData.name;

      if (widgetData.mapPlaceholderImage?.imagePresignedUrl) {
        try {
          widgetData.mapPlaceholderImage.base64 = await getImageBase64DataFromPresignedUrl(
            widgetData.mapPlaceholderImage.imagePresignedUrl,
          );
          delete widgetData.mapPlaceholderImage.imagePresignedUrl;
        } catch (error) {
          console.error('Error converting image to base64:', error);
        }
      }

      delete widgetData.type;
      delete widgetData.PK;
      delete widgetData.SK;

      const widgetJson = JSON.stringify(widgetData, null, 2);
      downloadFileInJsonFormat(widgetJson, fileName);
      console.log('Successfully exported');
    } catch (error) {
      console.log('Error exporting widget', error);
      openSnackbar({
        variant: 'error',
        msg: 'Error exporting widget',
      });
    }
  };

  const handleDuplicateWidgetCallback = async (updatedWidgetData, widgetType) => {
    console.log('duplicate clicked');

    const widgetId = extractGUID(updatedWidgetData?.SK);
    const widgetData = await getWidgetByTypeWithReactQuery(widgetId, widgetType);

    delete widgetData.SK;
    delete widgetData.PK;
    widgetData.widgetTitle = widgetData.widgetTitle + ' COPY';
    widgetData.name = widgetData.name + ' COPY';
    openSnackbar({
      msg: `Duplicating ${widgetType.toLowerCase()} widget ${widgetData.name}...`,
    });
    let response;
    switch (widgetType) {
      case BOOKING_CAPS:
        if (widgetData.mapPlaceholderImage?.imagePresignedUrl) {
          try {
            widgetData.mapPlaceholderImage.base64 = await getImageBase64DataFromPresignedUrl(
              widgetData.mapPlaceholderImage.imagePresignedUrl,
            );
            delete widgetData.mapPlaceholderImage.imagePresignedUrl;
          } catch (error) {
            console.error('Error converting image to base64 during duplication:', error);
          }
        }
        response = await createBookingWidgetMutation.mutateAsync(widgetData);
        break;

      case QUESTIONNAIRE_CAPS:
        response = await createQuestionnaireWidgetMutation.mutateAsync({ widgetData, isImport: false });
        break;

      case REGISTRATION_CAPS:
        response = await createRegistrationWidgetMutation.mutateAsync(widgetData);
        break;

      default:
        console.log('Invalid widget type');
        response = { success: false, error: 'Invalid widget type' };
        break;
    }

    return response;
  };

  const handleEditWidgetCallback = (_unused, widgetData) => {
    let orgId = extractGUID(widgetData.PK);
    const widgetId = extractGUID(widgetData?.SK);
    console.log(widgetData.widgetType, 'in edit');
    console.log(widgetId, 'id');
    router.push(`/widget-editor/${widgetData.widgetType.toLowerCase()}/${widgetId}`);
  };

  const handleNavigationCallback = (_widgetPage, widgetType) => {
    console.log(widgetType, 'add new');
    router.push(`/widget-editor/${widgetType.toLowerCase()}/create`);
  };

  const DeleteWarningDialog = () => (
    <Dialog
      open={deleteWarningOpen}
      onClose={handleCancelDelete}
      sx={{
        '& .MuiDialog-paper': {
          width: '400px',
          maxWidth: '90vw',
          borderRadius: '5px',
          overflow: 'hidden',
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 1, px: 2 }}>
        <Typography variant="subtitle1" fontWeight="bold">
          Unable to Delete the Widget
        </Typography>
      </DialogTitle>
      <DialogContent dividers sx={{ padding: 2, borderTop: '1px solid #e0e0e0', borderBottom: 'none' }}>
        <Typography variant="body1">
          This widget cannot be deleted because it&apos;s currently set as your default dynamic widget. To delete it,
          please first set a new default dynamic widget in Settings &gt; General.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button variant="contained" onClick={handleCancelDelete}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <>
      <Widgets
        widgetsList={allWidgetsQueries.data || []}
        organizationId={orgId}
        widgetBaseUrl={WIDGET_V2_BASE_URL}
        handleEditWidgetCallback={handleEditWidgetCallback}
        handleNavigationCallback={handleNavigationCallback}
        handleImportWidgetCallback={handleImportWidgetCallback}
        handlePreviewWidgetCallback={handlePreviewWidgetCallback}
        handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
        handleDeleteWidgetCallback={handleDeleteWidgetCallback}
        handleExportWidgetCallback={handleExportWidgetCallback}
        canDeleteWidget={canDeleteWidget}
      />
      <DeleteWarningDialog />
    </>
  );
}
