import React from 'react';
import ProfileTabPanel from './ProfileTabPanel';
import { HeaderStyle } from '@cambianrepo/ui';
import { PanelBorder } from '@cambianrepo/ui';

export function OrgProfileEditor({ orgMetaData, handleProfileFormSaveCallback }) {
  return (
    <>
      <HeaderStyle>Profile Settings</HeaderStyle>
      <PanelBorder sx={{ padding: 2 }}>
        <ProfileTabPanel orgMetaData={orgMetaData} handleFormSaveCallback={handleProfileFormSaveCallback} />
      </PanelBorder>
    </>
  );
}
