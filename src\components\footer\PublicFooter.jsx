'use client';

// TODO: Change internatiolization to fit Next app router
import { AppBar, Box, Divider, Grid, Link, Stack, Toolbar, Typography } from '@mui/material';

import { useTranslation } from 'react-i18next';
import React from 'react';
import { COMPONENT_UI_VERSION } from '@/lib/version';
import './style.css';
// IMPORTANT: SSR does not support useMediaQuery well.
// If we use that instead, it will render AppBar in initial render no matter what.
// Thus, using css file with div

function PublicFooter() {
  const test = 10;
  const { t } = useTranslation();
  const applicationName = process.env.NEXT_PUBLIC_APP_NAME;
  const applicationVersion = process.env.NEXT_PUBLIC_VERSION;
  const privacyStatementDocument = process.env.NEXT_PUBLIC_PRIVACYSTATEMENT_DOCUMENT_PDF;
  const componentUiPatchVersion = COMPONENT_UI_VERSION.split('.')[2];

  return (
    <AppBar
      position="sticky"
      color="default"
      display={{ xs: 'none', sm: 'block' }}
      sx={{
        bottom: 0,
        minHeight: '66px',
        zIndex: (theme) => theme.zIndex.drawer + 1,
      }}
    >
      <Toolbar>
        <Grid container justifyContent="flex-start" sx={{ flexGrow: 1, width: 1 / 4 }}>
          <Typography variant="caption">{t('Copyright © Cambian Business Services, Inc.')}</Typography>
        </Grid>
        <Box sx={{ flexGrow: 1 }} />
        <Stack direction="row" divider={<Divider orientation="vertical" flexItem />} spacing={2}>
          <Link variant="caption" href="mailto:<EMAIL>">
            {t('Help')}
          </Link>
          {privacyStatementDocument && (
            <Link variant="caption" href={privacyStatementDocument} target="_blank">
              {t('Privacy Statement')}
            </Link>
          )}
        </Stack>
        <Box sx={{ flexGrow: 1 }} />
        <Grid container justifyContent="flex-end" sx={{ flexGrow: 1, width: 1 / 4 }}>
          <Typography variant="caption">
            {applicationName} {applicationVersion}/{componentUiPatchVersion}
          </Typography>
        </Grid>
      </Toolbar>
    </AppBar>
  );
}

export default PublicFooter;
