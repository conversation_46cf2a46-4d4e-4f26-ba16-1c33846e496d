import {
  BOOKING_CAPS,
  BOOKING_WIDGET_PREVIEW_URL,
  ORGANIZATION_ID,
  QUESTIONNAIRE_CAPS,
  QUESTIONNAIRE_WIDGET_PREVIEW_URL,
  REGISTRATION_CAPS,
  REGISTRATION_WIDGET_PREVIEW_URL,
  WIDGET_ID,
} from '@/lib/widget-editor/utils/constants';

// TODO: This utility function should be also moved to the other repo with Profile Editor and other Sharable component.
// Map RHF's dirtyFields over the `data` received by `handleSubmit` and return the changed subset of that data.
export function dirtyValues(dirtyFields, allValues) {
  // If *any* item in an array was modified, the entire array must be submitted,
  // because there's no way to indicate "placeholders" for unchanged elements.
  // `dirtyFields` is `true` for leaves.
  if (dirtyFields === true || Array.isArray(dirtyFields)) return allValues;

  // Here, we have an object
  return Object.fromEntries(
    Object.keys(dirtyFields).map((key) => [key, dirtyValues(dirtyFields[key], allValues[key])]),
  );
}

export function isValidV4UUID(uuidString) {
  const idRegex = /^[0-9A-F]{8}-[0-9A-F]{4}-[4][0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i;
  return idRegex.test(uuidString);
}

export function containsOnlyDigits(str) {
  const result = /^\d+$/.test(str);

  if (result) return [true, parseInt(str)];

  return [false, ''];
}

export const sortWidgets = (mergedWidgets, key) => {
  mergedWidgets.sort(function (a, b) {
    if (b[key] < a[key]) {
      return -1;
    } else if (a[key] > b[key]) {
      return 1;
    } else {
      return 0;
    }
  });

  return mergedWidgets;
};

export const extractGUID = (inputString) => {
  // * expected inputString format "SOME_PREFIX#GUID"
  if (!inputString) return '';
  const parts = inputString.split('#');

  if (parts.length >= 2) {
    return parts[1];
  } else {
    return null;
  }
};

export const getWidgetURL = (BASE_URL, organizationId, widgetId, widgetType, isDynamicWidget) => {
  let URL = '';
  const WIDGET_BASE_URL = BASE_URL || process.env.REACT_APP_WIDGET_BASE_URL;

  switch (widgetType) {
    case QUESTIONNAIRE_CAPS:
      URL = `${WIDGET_BASE_URL}${QUESTIONNAIRE_WIDGET_PREVIEW_URL.replace(ORGANIZATION_ID, organizationId).replace(
        WIDGET_ID,
        widgetId,
      )}`;
      if (isDynamicWidget) URL += `?qid=`;
      return URL;
    case BOOKING_CAPS:
      URL = `${WIDGET_BASE_URL}${BOOKING_WIDGET_PREVIEW_URL.replace(ORGANIZATION_ID, organizationId).replace(
        WIDGET_ID,
        widgetId,
      )}`;
      return URL;
    case REGISTRATION_CAPS:
      URL = `${WIDGET_BASE_URL}${REGISTRATION_WIDGET_PREVIEW_URL.replace(ORGANIZATION_ID, organizationId).replace(
        WIDGET_ID,
        widgetId,
      )}`;
      return URL;
    default:
      break;
  }
};

export const getArtifactRepoUrlByVisibility = (visibility) => {
  const validVisibilities = ['private', 'public'];

  if (!validVisibilities.includes(visibility)) {
    return null;
  }

  return visibility === 'private'
    ? process.env.NEXT_PUBLIC_PRIVATE_ARTIFACT_REPOSITORY_BASE_URL
    : process.env.NEXT_PUBLIC_PUBLIC_ARTIFACT_REPOSITORY_BASE_URL;
};

export const downloadFileInJsonFormat = (jsonString, fileName) => {
  const blob = new Blob([jsonString], { type: 'application/json' });
  const href = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = href;
  link.download = `${fileName}.json`;
  document.body.appendChild(link);
  link.click();

  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

export const getImageBase64DataFromPresignedUrl = async (imagePresignedUrl) => {
  const response = await fetch(imagePresignedUrl);
  const blob = await response.blob();

  const base64data = await new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });

  return base64data;
};
