import React, { useEffect, useState, useCallback } from 'react';
import {
  Stack,
  Button,
  Box,
  Grid,
  Typography,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  OutlinedInput,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { FormContainer, TextFieldElement } from 'react-hook-form-mui';
import { dirtyValues } from '../../../../lib/utility';
import { useForm, useWatch } from 'react-hook-form';
import { DoublePanelBorder } from '@cambianrepo/ui';
import { usePermissions } from '@/context/UserPermissions';
import * as Constants from '@/app/globalConstants';
import { ContactField, CambianTooltip } from '@cambianrepo/ui';

function DeleteConfirmationDialog({ open, onClose, onConfirm, selectedUser }) {
  return (
    <Dialog sx={{ '& .MuiDialog-paper': { width: 'auto' } }} maxWidth="xs" open={open}>
      <DialogTitle>
        Delete user &quot;{selectedUser?.firstName} {selectedUser?.lastName}&quot;?
      </DialogTitle>
      <DialogContent>
        <Typography>This action cannot be undone.</Typography>
      </DialogContent>
      <DialogActions>
        <Button autoFocus variant="outlined" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={onConfirm} variant="contained" color="error">
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function UserEditor({ selectedUserId, selectedUser, rolesData, handleUserFormSaveCallback, deleteUserCallback }) {
  const [isDialogOpen, setDialogOpen] = useState(false);
  const openDialog = () => setDialogOpen(true);

  const formContext = useForm({
    defaultValues: {
      emailAddress: '',
      phoneNumber: '',
      firstName: '',
      lastName: '',
      roles: [],
      roleIds: [],
    },
  });
  const { fetchPermissionsData } = usePermissions();
  const { formState, control, setValue, clearErrors, setError } = formContext;
  const { isDirty, errors } = formState;

  const currentRoles = useWatch({
    control,
    name: 'roles',
    defaultValue: [],
  });

  useEffect(() => {
    const userRoles = selectedUser?.roles ? selectedUser.roles.map((role) => role.name) : [];
    formContext.reset({
      emailAddress: selectedUser?.emailAddress || '',
      phoneNumber: selectedUser?.phoneNumber || '',
      firstName: selectedUser?.firstName || '',
      lastName: selectedUser?.lastName || '',
      roles: userRoles,
      roleIds: selectedUser?.roles ? selectedUser.roles.map((role) => role.id) : [],
    });
  }, [selectedUserId, selectedUser, formContext]);

  const handleSubmit = useCallback(
    async (data) => {
      const dirtyData = dirtyValues(formState.dirtyFields, data);
      if (data.emailAddress === '' && data.phoneNumber === '') {
        setError('emailAddress', {
          type: 'manual',
          message: 'You must fill out either Email address or Phone number',
        });
        setError('phoneNumber', {
          type: 'manual',
          message: 'You must fill out either Email address or Phone number',
        });
      } else {
        if (await handleUserFormSaveCallback({ allValues: data, dirtyValues: dirtyData })) {
          if (formState.dirtyFields.roles) {
            await fetchPermissionsData();
          }
          formContext.reset(data, { keepIsSubmitted: true });
        }
      }
    },
    [formContext, handleUserFormSaveCallback, fetchPermissionsData, formState.dirtyFields, setError],
  );

  const handleRoleChange = (e) => {
    const newSelectedRoles = e.target.value;
    setValue('roles', newSelectedRoles, { shouldDirty: true });
    setValue(
      'roleIds',
      newSelectedRoles.map((roleName) => rolesData.find((role) => role.name === roleName)?.id),
      { shouldDirty: true },
    );
  };

  const handleContactChange = (field) => (newValue) => {
    setValue(field, newValue.value, { shouldDirty: true });
    if (newValue.validationError) {
      setError(field, {
        type: 'manual',
        message: newValue.validationHelperText,
      });
    } else {
      clearErrors(field);
    }
    if (newValue.value) {
      clearErrors(field === 'emailAddress' ? 'phoneNumber' : 'emailAddress');
    }
  };

  return (
    <>
      <DoublePanelBorder>
        <FormContainer onSuccess={handleSubmit} formContext={formContext}>
          <Stack spacing={Constants.formFieldSpacing}>
            <Grid container spacing={0}>
              <Box sx={{ paddingBottom: '12px' }}>
                <Typography variant="subtitle1">Username</Typography>
              </Box>
              <Grid container spacing={Constants.formFieldSpacing}>
                <Grid item xs={12}>
                  <ContactField
                    type="email"
                    label="Email address"
                    value={formContext.watch('emailAddress')}
                    onChange={handleContactChange('emailAddress')}
                    required
                    error={!!errors.emailAddress}
                    helperText={errors.emailAddress?.message}
                    otpVerificationEnabled={false}
                    showStarIcon={false}
                    CambianTooltip={CambianTooltip}
                  />
                </Grid>
                <Grid item xs={12}>
                  <ContactField
                    type="phone"
                    label="Phone number"
                    value={formContext.watch('phoneNumber')}
                    onChange={handleContactChange('phoneNumber')}
                    error={!!errors.phoneNumber}
                    helperText={errors.phoneNumber?.message}
                    otpVerificationEnabled={false}
                    showStarIcon={false}
                    CambianTooltip={CambianTooltip}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ margin: 0, width: '500px', maxWidth: '100%' }} />
                </Grid>
              </Grid>
            </Grid>
            <TextFieldElement label="First Name" name="firstName" required autoComplete="off" />
            <TextFieldElement label="Last Name" name="lastName" required autoComplete="off" />
            <FormControl sx={{ width: '100%' }} size="small">
              <InputLabel id="roles-label">User Roles</InputLabel>
              <Select
                labelId="roles-label"
                id="roles"
                multiple
                required
                autoComplete="off"
                value={currentRoles}
                onChange={handleRoleChange}
                input={<OutlinedInput label="User Roles" />}
                renderValue={(selected) => <Typography>{selected.join(', ')}</Typography>}
              >
                {rolesData.map((role) => (
                  <MenuItem key={role.id} value={role.name}>
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <div sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-even', width: '500px' }}>
              {selectedUserId && (
                <Button
                  autoFocus
                  variant="outlined"
                  color="error"
                  size="small"
                  onClick={openDialog}
                  sx={{ marginRight: '8px' }}
                >
                  Delete
                </Button>
              )}
              <Button variant="contained" type="submit" size="small" disabled={!isDirty}>
                {selectedUserId ? 'Save' : 'Create'}
              </Button>
            </div>
          </Stack>
          <DeleteConfirmationDialog
            open={isDialogOpen}
            onClose={() => setDialogOpen(false)}
            onConfirm={deleteUserCallback}
            selectedUser={selectedUser}
          />
        </FormContainer>
      </DoublePanelBorder>
    </>
  );
}

export default UserEditor;
