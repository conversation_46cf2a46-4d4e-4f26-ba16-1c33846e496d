export const questionnaireList = [
  {
    artifactId: 'ea44b8bb-aba4-4c9d-a839-1efedec1a9b5',
    shortName: 'questionnaire test 1',
    title: 'titile',
    description: 'description',
    publishDate: '2024-03-19T12:55:34.869Z',
    status: 'draft',
    publishedStatus: 'NO',
  },
  {
    artifactId: '90528155-b40b-4d7f-b84a-f2d3a28d6494',
    shortName: 'CV_risk_2023 15 March_IMPORT',
    title: 'Cardiovascular Risk Assessment',
    description:
      "The cardiovascular assessment estimates your cardiovascular (CV) risk and heart age. The CV risk is the chance of having a heart attack or stroke in the next 10 years. The heart age gives you an idea of how healthy your heart is compared to your age.  If you don't know your cholesterol levels you can use the body mass index (BMI) which is based on weight and height.",
    publishDate: '2024-02-13T18:51:08.914Z',
    status: 'draft',
    publishedStatus: 'NO',
  },
  {
    artifactId: 'b7e1276a-380e-404f-8977-d18ef7a969b5',
    shortName: 'CV_risk_2024 15 Feb report',
    title: 'Cardiovascular Risk Assessment',
    description:
      "The cardiovascular assessment estimates your cardiovascular (CV) risk and heart age. The CV risk is the chance of having a heart attack or stroke in the next 10 years. The heart age gives you an idea of how healthy your heart is compared to your age.  If you don't know your cholesterol levels you can use the body mass index (BMI) which is based on weight and height.",
    publishDate: '2024-02-21T10:49:23.926Z',
    status: 'final',
    publishedStatus: 'PUBLIC',
  },
];

export const questionnaireDetailList = [
  {
    id: 'ea44b8bb-aba4-4c9d-a839-1efedec1a9b5',
    resourceType: 'Questionnaire',
    date: '2024-03-19T12:55:33.059Z',
    name: 'questionnaire test 1',
    title: 'titile',
    description: 'description',
    extension: [
      {
        url: '/display-dial',
        valueBoolean: false,
      },
      {
        url: '/display-description',
        valueBoolean: true,
      },
      {
        url: '/display-large-buttons',
        valueBoolean: false,
      },
      {
        url: '/display-progress-bar',
        valueBoolean: true,
      },
      {
        url: '/display-score',
        valueBoolean: false,
      },
      {
        url: '/display-score-category',
        valueBoolean: false,
      },
      {
        url: '/display-title',
        valueBoolean: true,
      },
      {
        url: '/questionnaire-type',
        valueCode: 'Instrument',
      },
      {
        url: '/question-unit-per-page',
        valueBoolean: true,
      },
      {
        url: '/trendable',
        valueBoolean: false,
      },
      {
        url: '/pdftemplate-id',
        valueString: '',
      },
      {
        url: '/pdftemplate-name',
        valueString: '',
      },
      {
        url: '/question-identifier-prefix',
        valueString: 'Item',
      },
      {
        url: '/question-identifier-next-sequence',
        valueInteger: 5,
      },
      {
        url: '/htmltemplate-base64',
        valueString: '',
      },
      {
        url: '/pdftemplate-base64',
        valueString: '',
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '3606176e-1794-41a9-bdf2-9d78520905b0',
          },
          {
            url: '/score-sequence',
            valueInteger: 0,
          },
          {
            url: '/score-name',
            valueString: 'NewVariable',
          },
          {
            url: '/list-of-formula-definitions',
            extension: {
              extension: [
                {
                  extension: [
                    {
                      url: '/formula-name',
                      valueString: 'NewVariable-F1',
                    },
                    {
                      url: '/mathematical-expression',
                      valueString: '',
                    },
                    {
                      url: '/selection-rule',
                      valueString: 'Select Rule',
                    },
                  ],
                  url: '/set-of-api-formula',
                },
              ],
              url: '/list-of-formula-definitions',
            },
          },
        ],
      },
    ],
    identifier: [
      {
        use: 'old',
        system: 'http://www.cambian.com/questionnaire/identifier',
        value: 'd3ea065c-2d4f-4d38-b4c7-fc4b28d21f78',
        period: {
          start: '2023-12-13T14:06:06+00:00',
          end: '2023-12-13T14:15:33+00:00',
        },
      },
      {
        use: 'usual',
        system: 'urn:uuid',
        value: 'f6356947-59d8-495b-abf7-47973d3a1968',
        period: {
          start: '2023-12-13T14:06:06+00:00',
        },
      },
    ],
    item: [
      {
        linkId: 'Group1',
        item: [
          {
            linkId: 'Item1',
            type: 'decimal',
            text: 'number',
            extension: [
              {
                url: '/Item/description',
                valueString: null,
              },
              {
                url: '/Item/explanation',
                valueString: null,
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: '1',
              },
              {
                url: '/Item/max-value',
                valueDecimal: '10',
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 1,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
          },
          {
            linkId: 'Item2',
            type: 'text',
            text: 'text',
            extension: [
              {
                url: '/Item/description',
                valueString: null,
              },
              {
                url: '/Item/explanation',
                valueString: null,
              },
              {
                url: '/Item/explanation-flag',
                valueString: null,
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5520,
              },
              {
                url: '/Item/min-length',
                valueInteger: 0,
              },
              {
                url: '/Item/max-length',
                valueInteger: 255,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 2,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            enableWhen: [
              {
                operator: '<',
                answerString: '10',
                question: 'Item1',
              },
            ],
          },
        ],
        type: 'group',
        extension: [
          {
            url: '/Item/question-group-sequence',
            valueInteger: 1,
          },
        ],
      },
    ],
    publisher: 'App-Scoop',
    status: 'draft',
  },
  {
    resourceType: 'Questionnaire',
    id: '90528155-b40b-4d7f-b84a-f2d3a28d6494',
    extension: [
      {
        url: '/display-dial',
        valueBoolean: false,
      },
      {
        url: '/display-description',
        valueBoolean: true,
      },
      {
        url: '/display-large-buttons',
        valueBoolean: false,
      },
      {
        url: '/display-progress-bar',
        valueBoolean: true,
      },
      {
        url: '/display-score',
        valueBoolean: false,
      },
      {
        url: '/display-score-category',
        valueBoolean: false,
      },
      {
        url: '/display-title',
        valueBoolean: true,
      },
      {
        url: '/questionnaire-type',
        valueCode: 'Instrument',
      },
      {
        url: '/question-unit-per-page',
        valueBoolean: true,
      },
      {
        url: '/trendable',
        valueBoolean: false,
      },
      {
        url: '/result-page',
        valueString:
          '{"sections":[{"type":"Label","displayName":"<p>Header</p>","showInReport":true,"htmlText":"","fields":[],"variables":[]},{"name":"DateTime","displayName":"","type":"Date","showInReport":false,"htmlText":"","fields":[{"format":"YYYY-MM-DD HH:mm:ss","name":"","displayName":"","sequence":1,"showInReport":true}],"variables":[]},{"type":"Demographics","displayName":"","showInReport":true,"htmlText":"","fields":[{"name":"firstName","displayName":"First Name","sequence":1,"showInReport":true,"code":"FIRST_NAME","format":""},{"name":"lastName","displayName":"Last Name","sequence":2,"showInReport":true,"code":"LAST_NAME","format":""},{"name":"phn","displayName":"PHN","sequence":3,"showInReport":true,"code":"PHN","format":""},{"name":"dateOfBirth","displayName":"Date of Birth","sequence":4,"showInReport":true,"code":"DOB","format":""},{"name":"gender","displayName":"Gender","sequence":5,"showInReport":true,"code":"GENDER","format":""},{"name":"email","displayName":"Email","sequence":6,"showInReport":true,"code":"EMAIL","format":""},{"name":"participantId","displayName":"Participant ID","sequence":7,"showInReport":true,"code":"PARTICIPANT_ID","format":""}],"variables":[]},{"type":"Questions & Answers","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[]}]}',
      },
      {
        url: '/htmltemplate-base64',
        valueString:
          '<p></p>\n<h1><b>{Questionnaire.title}</b></h1>\n<p> Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD hh:mm:ss a)}</p>\n\n<table width="100%">\n  <tbody>\n    <tr>\n      <td style="text-align: center;">\n        <img src="data:image/gif;base64,R0lGODlhMwAsAFUAACH5BAkAACkALAAAAAAzACwAhf78+PLhuPXoxvPju/PkwOTAaOPAZeK9YPLgs/z58evQjeXDcOjKgOvRkPDbqPXoye7YoO3UmPny4ejIe+rOiObFdOfIeu3WnPz47fHesOK6Wvjt1PDdrfnw2/v16PDap+/aqPju1ufHePfr0Pjv2Pbqy+nMhebEc+S/Z////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb/QIBwKPQQIhSRaNLIbIhQoieAVDIzpKh2KIkUDmDDF1xgELaA7gJMBmvKDzQR8S0wPw4HCLk+UCRRGXUTERAZeXxgCoBbCQ0HBhQOBJSVlFQnBwsjQ44HZRmWlgENCwYLHlsKnxCirgQIDJ9xCRSsr5UCsAwLjFARmg66lwIBxcWWFGIEygvClcYBxNLFAR1RD5+T08fR3QSrXwsIl9ze5woXnRMoDbrn3fGVFAULofHw57ALDEMBBxOkEYA3oGDBbgOKfQg1EB++hNEQxBGyKkI8gwcTGvxGwCFBiMgqJRBSoAA5bweNQQSpDyXGYilhHoOwIM4pjzMZINA4IN/F/4sxBUbQEAHAAQY4AwSoYMBdAJBBNz5l6ZDABwP9sAooqNIbBAMGJuDMmPRYT2QOsALQgPSjAAZiTHpkCfXl1GME0mYV65NDSVMQzMaCQJXg1sIEIBxoAOCUUoS6FBgw0cCAgoYDJFfIF/Rp1QYaPgCwUCAwSgEIFhQAcTWgMQQVSl4wu3VrNKUuBwyccCAOBA0UugVAQMqAheGqOQgQ8LWA8YjEhT+dGhOEgRMYAHQoKawYadUGLnScEN6YiTJMIcCOzcDz8BAhop+TBWEIsOOoSce2gEBXZaR0mNRAARaQVxIDBj01EmoxXXCKLxiYogB0CPRnjAP1BOCgBfuAVf8BB8RBFAAjEihVUF7OBQDFCM5NSNttsTkAlzsEVFbAbDz1R8QDj4HgXFFRBACWAjidx0sBysHCBEpKZdeJUn4thoYALfrk4IFJKfUEAA4A2UEAaTUwEhpCWhYUaiWFl6WKACRQgAapJDCQA3IMQaVl+ZBXwU4qpdSTNUK4eQCJ0tTpz5BmGSOZGUBVM1GbX6QiRAkIZGEoAAR84WJECnDQ0Dl/OgnpoE8GIGqdBAzpk0vFILBloF+MKUQIATxqaKZ4JiViALJOKkAUsPhiaJlEusXViJemUWiyAFB5gItSCWfrEB1wgo2rzGKKaJ9dNanFm70GqlS4qFZ5kVIhbIFzAAhbkCBRtpi2eC6b8GKKbLYCGPCsN8RtkAAGCQQs8MAEC7yBMfWmapl0Aw2E28MQQ4yAtfA6u2lPCORRKwEPdORxxyB7LEAJ5CarsAK6CeDACQUwVu/LdspLU1Mlw8yswrxIafPOzTq3WM08M0ulyzwHAQA7" data-filename="CV-risk.gif" style="width: 51px;"><br>\n        <br>{QuestionnaireResponse.variable.cv_percentage:round(2)} %\n        <br>CV Risk\n      </td>\n      <td style="text-align: center;">\n        <img src="data:image/gif;base64,R0lGODlhOAAwAGYAACH5BAkAAFUALAAAAAA4ADAAhvHesP38+PPgsf7wyv/zzvrouf/z0Pnmtvz48PPiufjlsPnw2NiYAPry4fju09ihGt6qJvTmw9meDdSYAvXoyProtvDaqPvrwOnNie7QgP/66/jv2OG4U/DQgNifEv7vxvDapevRkNqoK/jiqfr16Pbgo+O5T+TBaOCxPOfEct+uN9+uNNuqMeCfAPPZk//44dmjIffr0PXnxtWZCfTame7Qf9ysNtaeEvvsv/vuy/DShe3Pf/Xdnu7YouTAZ+a8WOnFaNqhFujGbujCYvLXkPDUjfbfofDcq+3Wn+3VmezKc+vKc+nNhvjlr+a/XNqmLNylHdOWAOC0SOCyRt6sMf///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/gAGCg4SFhoeIiYqLjI2Oj4MIC5MLDZCFDZQLCJeDGhQJAgWjBQcCEQaOCBsJAAqkBQIJOZyqFKICAgC6ALu4MYsOCQUKvL25AgcJEbWKGwexx73T06LMhwjDxtLUogmWiQ6xud3U1AoHJIUkB6bm79UFDoji5NXw3d+DDQkH+Pi5CiwwhICcrm3b3glQMHCBu3//cqkjdMEdQojVDlDwhxHigQuEGhQwd7Ejx44QC4ALEMFYSZQwMSqQIUiDvXs4Y+okmYCTyHI5dwqdpjLAhgoKgQ4VemAgBQVKdy2dqpHlyaAJp6Ks6uDqS606D8xbMJJkVLAdiza4+RUtSgXg/0J9zeoWnqxBBspKDVo3ZSpBBef2hbiwWYCu3BIPxifWUEWsQ0Gk4ACDgeUHUlIgeXeAJsEExc7CBHFCBAMPD1Kn9jABBhALACxYaGeYULarijtiYBHFAwcaCoIrqADihwcJU1LYeECkdiEEEU7ShYjhCQMTRi4MMDCgu3cQKiaIlxCiUQS9e3VXBqKd+/b3BLp3CGJZQhJHOUZOhwfCBoMhF3An4AAjKFFCfDyoYBkDEvTwSAzR7EfNCR6oMMJ7A+oAQQ0DEJCBCKqJYAEk9XQkgAUszNBBdwPGlwEEGXT3QQEVEPMXidEQFsINLFw4oAHxabhDd/F5N1AnDoRmV/8vGFxXQIcEEMCdizBCKeV2N16ywU0KNWlCBSx2uB0BHUAwpJjxcdfJPrIkJQATLXwZ5ZzxlYCCBCvOKWCWnbCD2zEYTIDChfANQAMVLaxQApRo8tmJBv0A1YMIN7hA5wBFQJDogfFdqsGaz4FGkgU/zMDBk9vxAMEEHDRBJKPxObfmbfcIEAIEeHrnAgpL4EBnp1K+ACo20JTjgwQPuCDmBx+8Cux2wyJCKy8CHHEsFDU0a+WcUBoga7SCRADVXhacEMQEK2TQbKeNfgpuIhsdlEsFIXAwwwRO+PqrsO8uAuFBBVAgiAwcCKFdmi98228hDlSw0AYLL0wWxBEv7O4DwoEAADs=" data-filename="HeartAge.gif" style="width: 56px;"><br>\n        <br>{QuestionnaireResponse.variable.heart_validation:round(3)} years\n        <br>Heart age\n      </td>\n    </tr>\n  </tbody>\n</table>\n<br>\n<p></p>\n<p><br>\n</p>\n<table width="100%">\n  <tbody>\n    <tr data-visible="{QuestionnaireResponse.variable.bmi:boolean()}">\n      <td>Age:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS2} years</td>\n    </tr>\n    <tr>\n      <td>Sex:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1}</td>\n    </tr>\n    <tr>\n      <td>Male</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1.score:compare(&lt;,2)}</td>\n    </tr>\n    <tr>\n      <td>Female:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1:equal(Female)}</td>\n    </tr>\n    <tr>\n      <td>Systolic blood pressure (weekly average):</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS3} mmHg</td>\n    </tr>\n    <tr>\n      <td>Blood pressure treated with medication:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS4}</td>\n    </tr>\n    <tr>\n      <td>Smoker:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS5}</td>\n    </tr>\n    <tr>\n      <td>Diabetes:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS6}</td>\n    </tr>\n    <tr>\n      <td>Assessment type:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS7}</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS8.display}">\n      <td>HDL:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS8} mmol/L</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS9.display}">\n      <td>Total cholesterol:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS9} mmol/L</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS10.display}">\n      <td>Height:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS10} cm</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS11.display}">\n      <td>Weight:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS11} kg</td>\n    </tr>\n  </tbody>\n</table>\n\n<p style="color:gray; font-size:11px;"><br></p><p style="color:gray; font-size:11px;"><br></p>\n<p style="color:gray; font-size:11px;">{Questionnaire.description}</p>\n\n\n<p></p>\n<p><br></p>\n',
      },
      {
        url: '/html-report-active',
        valueBoolean: false,
      },
      {
        url: '/question-identifier-prefix',
        valueString: 'Item',
      },
      {
        url: '/question-identifier-next-sequence',
        valueInteger: 1,
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 0,
          },
          {
            url: '/score-name',
            valueString: 'bl_treated',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.93303',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS4}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.82263',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS4}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.99881',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS4}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F4',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.76157',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS4}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 2,
          },
          {
            url: '/score-name',
            valueString: 'bl_smoker',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_smoker-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_smoker-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.52873',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_smoker-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.65451',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 3,
          },
          {
            url: '/score-name',
            valueString: 'bl_diabetes',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_diabetes-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.69154',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_diabetes-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.57367',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_diabetes-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 4,
          },
          {
            url: '/score-name',
            valueString: 'bB_treated',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.88267',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.92672',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F4',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.81291',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==2 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.85508',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==2 && #{QUS1QGS1}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 5,
          },
          {
            url: '/score-name',
            valueString: 'bB_smoker',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_smoker-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_smoker-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.70953',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_smoker-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.61868',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 6,
          },
          {
            url: '/score-name',
            valueString: 'bB_diabetes',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_diabetes-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.77763',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_diabetes-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.53160',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_diabetes-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 7,
          },
          {
            url: '/score-name',
            valueString: 'bmi',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bmi-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '#{QUS1QGS11} / ((#{QUS1QGS10}/100) * (#{QUS1QGS10}/100))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 8,
          },
          {
            url: '/score-name',
            valueString: 'sum_lipid',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_lipid-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '2.32888 * log(#{QUS1QGS2}) + 1.20904 * log(#{QUS1QGS9}*38.67) - 0.70833 * log(#{QUS1QGS8}*38.67) + #{bl_treated} * log(#{QUS1QGS3}) + #{bl_smoker} + #{bl_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_lipid-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '3.06117 * log(#{QUS1QGS2}) + 1.12370 * log(#{QUS1QGS9}*38.67) - 0.93263 * log(#{QUS1QGS8}*38.67) + #{bl_treated} * log(#{QUS1QGS3}) + #{bl_smoker} + #{bl_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 9,
          },
          {
            url: '/score-name',
            valueString: 'sum_bmi',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_bmi-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '3.11296 * log(#{QUS1QGS2}) + 0.79277 * log(#{bmi}) + #{bB_treated} * log(#{QUS1QGS3}) + #{bB_smoker} + #{bB_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2  && #{QUS1QGS7}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_bmi-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '2.72107 * log(#{QUS1QGS2}) + 0.51125 * log(#{bmi}) + #{bB_treated} * log(#{QUS1QGS3}) + #{bB_smoker} + #{bB_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 10,
          },
          {
            url: '/score-name',
            valueString: 'cv',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F4',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.94833,(exp(#{sum_bmi} - 26.0145)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.88936,(exp(#{sum_lipid} - 23.9802)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.95012,(exp(#{sum_lipid} - 26.1931)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.88431,(exp(#{sum_bmi} - 23.9388)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 11,
          },
          {
            url: '/score-name',
            valueString: 'cv_percentage',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv_percentage-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '#{cv}*100',
                  },
                  {
                    url: '/selection-rule',
                    valueString: '1XXXXXXXXXX',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 12,
          },
          {
            url: '/score-name',
            valueString: 'heart_age',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.3267) * 114.2579',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.3675) * 158.1102',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.4294) * 192.4820',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F5',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.3212) * 109.1966',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 13,
          },
          {
            url: '/score-name',
            valueString: 'heart_validation',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_validation-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '86',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{heart_age}>85',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_validation-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '#{heart_age}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{heart_age}<=85',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
    identifier: [
      {
        use: 'usual',
        system: 'urn:uuid',
        value: '13071bd3-0636-4877-8071-46169e7e41ee',
        period: {
          start: '2023-03-13T15:10:02+00:00',
        },
      },
      {
        use: 'old',
        system: 'http://www.cambian.com/questionnaire/identifier',
        value: '13071bd3-0636-4877-8071-46169e7e41ee',
        period: {
          start: '2023-03-13T15:10:02+00:00',
          end: '2023-12-27T15:05:51+00:00',
        },
      },
    ],
    name: 'CV_risk_2023_IMPORT 13 march',
    title: 'Cardiovascular Risk Assessment',
    status: 'draft',
    date: '2023-12-27T15:05:51+00:00',
    publisher: 'cambian',
    description:
      "The cardiovascular assessment estimates your cardiovascular (CV) risk and heart age. The CV risk is the chance of having a heart attack or stroke in the next 10 years. The heart age gives you an idea of how healthy your heart is compared to your age.  If you don't know your cholesterol levels you can use the body mass index (BMI) which is based on weight and height.",
    item: [
      {
        id: 'group-565725',
        extension: [
          {
            url: '/Item/description',
            valueString: 'cannot locate string',
          },
          {
            url: '/Item/question-group-sequence',
            valueInteger: 1,
          },
        ],
        linkId: 'QUS1QGS0',
        text: 'cannot locate string',
        type: 'group',
        item: [
          {
            id: '565728',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 1,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS1',
            text: 'Sex assigned at birth',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565731',
                  code: '1',
                  display: 'Female',
                },
              },
              {
                valueCoding: {
                  id: '565733',
                  code: '2',
                  display: 'Male',
                },
              },
            ],
          },
          {
            id: '565737',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 2,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS2',
            text: 'Age (years)',
            type: 'decimal',
            required: true,
          },
          {
            id: '565742',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 3,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS3',
            text: 'Systolic blood pressure weekly average (mmHg)',
            type: 'decimal',
            required: true,
          },
          {
            id: '565747',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 4,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS4',
            text: 'Blood pressure treated with medication',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565750',
                  code: '1',
                  display: 'Yes',
                },
              },
              {
                valueCoding: {
                  id: '565752',
                  code: '2',
                  display: 'No',
                },
              },
            ],
          },
          {
            id: '565756',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 5,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS5',
            text: 'Smoker',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565759',
                  code: '1',
                  display: 'Yes',
                },
              },
              {
                valueCoding: {
                  id: '565761',
                  code: '2',
                  display: 'No',
                },
              },
            ],
          },
          {
            id: '565765',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 6,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS6',
            text: 'Diabetes',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565768',
                  code: '1',
                  display: 'Yes',
                },
              },
              {
                valueCoding: {
                  id: '565770',
                  code: '2',
                  display: 'No',
                },
              },
            ],
          },
          {
            id: '565774',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 7,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS7',
            text: 'Assessment type',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565777',
                  code: '1',
                  display: 'Lipids profile',
                },
              },
              {
                valueCoding: {
                  id: '565779',
                  code: '2',
                  display: 'Body mass index (BMI)',
                },
              },
            ],
          },
          {
            id: '565783',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 8,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS8',
            text: 'HDL (mmol/L) ',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '1',
              },
            ],
            required: false,
          },
          {
            id: '565790',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 9,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS9',
            text: 'Total cholesterol (mmol/L) ',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '1',
              },
            ],
            required: false,
          },
          {
            id: '565797',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 10,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS10',
            text: 'Height (cm)',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '2',
              },
            ],
            required: false,
          },
          {
            id: '565804',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 11,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS11',
            text: 'Weight (kg)',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '2',
              },
            ],
            required: false,
          },
        ],
      },
    ],
  },
  {
    resourceType: 'Questionnaire',
    id: 'b7e1276a-380e-404f-8977-d18ef7a969b5',
    extension: [
      {
        url: '/display-dial',
        valueBoolean: false,
      },
      {
        url: '/display-description',
        valueBoolean: true,
      },
      {
        url: '/display-large-buttons',
        valueBoolean: false,
      },
      {
        url: '/display-progress-bar',
        valueBoolean: true,
      },
      {
        url: '/display-score',
        valueBoolean: false,
      },
      {
        url: '/display-score-category',
        valueBoolean: false,
      },
      {
        url: '/display-title',
        valueBoolean: true,
      },
      {
        url: '/questionnaire-type',
        valueCode: 'Instrument',
      },
      {
        url: '/question-unit-per-page',
        valueBoolean: true,
      },
      {
        url: '/trendable',
        valueBoolean: false,
      },
      {
        url: '/result-page',
        valueString:
          '{"sections":[{"type":"Label","displayName":"<p>Header</p>","showInReport":true,"htmlText":"","fields":[],"variables":[]},{"name":"DateTime","displayName":"","type":"Date","showInReport":false,"htmlText":"","fields":[{"format":"YYYY-MM-DD HH:mm:ss","name":"","displayName":"","sequence":1,"showInReport":true}],"variables":[]},{"type":"Demographics","displayName":"","showInReport":true,"htmlText":"","fields":[{"name":"firstName","displayName":"First Name","sequence":1,"showInReport":true,"code":"FIRST_NAME","format":""},{"name":"lastName","displayName":"Last Name","sequence":2,"showInReport":true,"code":"LAST_NAME","format":""},{"name":"phn","displayName":"PHN","sequence":3,"showInReport":true,"code":"PHN","format":""},{"name":"dateOfBirth","displayName":"Date of Birth","sequence":4,"showInReport":true,"code":"DOB","format":""},{"name":"gender","displayName":"Gender","sequence":5,"showInReport":true,"code":"GENDER","format":""},{"name":"email","displayName":"Email","sequence":6,"showInReport":true,"code":"EMAIL","format":""},{"name":"participantId","displayName":"Participant ID","sequence":7,"showInReport":true,"code":"PARTICIPANT_ID","format":""}],"variables":[]},{"type":"Questions & Answers","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[]}]}',
      },
      {
        url: '/htmltemplate-base64',
        valueString:
          '<p></p>\n<h1><b>{Questionnaire.title}</b></h1>\n<p> Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD hh:mm:ss a)}</p>\n\n<table width="100%">\n  <tbody>\n    <tr>\n      <td style="text-align: center;">\n        <img src="data:image/gif;base64,R0lGODlhMwAsAFUAACH5BAkAACkALAAAAAAzACwAhf78+PLhuPXoxvPju/PkwOTAaOPAZeK9YPLgs/z58evQjeXDcOjKgOvRkPDbqPXoye7YoO3UmPny4ejIe+rOiObFdOfIeu3WnPz47fHesOK6Wvjt1PDdrfnw2/v16PDap+/aqPju1ufHePfr0Pjv2Pbqy+nMhebEc+S/Z////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb/QIBwKPQQIhSRaNLIbIhQoieAVDIzpKh2KIkUDmDDF1xgELaA7gJMBmvKDzQR8S0wPw4HCLk+UCRRGXUTERAZeXxgCoBbCQ0HBhQOBJSVlFQnBwsjQ44HZRmWlgENCwYLHlsKnxCirgQIDJ9xCRSsr5UCsAwLjFARmg66lwIBxcWWFGIEygvClcYBxNLFAR1RD5+T08fR3QSrXwsIl9ze5woXnRMoDbrn3fGVFAULofHw57ALDEMBBxOkEYA3oGDBbgOKfQg1EB++hNEQxBGyKkI8gwcTGvxGwCFBiMgqJRBSoAA5bweNQQSpDyXGYilhHoOwIM4pjzMZINA4IN/F/4sxBUbQEAHAAQY4AwSoYMBdAJBBNz5l6ZDABwP9sAooqNIbBAMGJuDMmPRYT2QOsALQgPSjAAZiTHpkCfXl1GME0mYV65NDSVMQzMaCQJXg1sIEIBxoAOCUUoS6FBgw0cCAgoYDJFfIF/Rp1QYaPgCwUCAwSgEIFhQAcTWgMQQVSl4wu3VrNKUuBwyccCAOBA0UugVAQMqAheGqOQgQ8LWA8YjEhT+dGhOEgRMYAHQoKawYadUGLnScEN6YiTJMIcCOzcDz8BAhop+TBWEIsOOoSce2gEBXZaR0mNRAARaQVxIDBj01EmoxXXCKLxiYogB0CPRnjAP1BOCgBfuAVf8BB8RBFAAjEihVUF7OBQDFCM5NSNttsTkAlzsEVFbAbDz1R8QDj4HgXFFRBACWAjidx0sBysHCBEpKZdeJUn4thoYALfrk4IFJKfUEAA4A2UEAaTUwEhpCWhYUaiWFl6WKACRQgAapJDCQA3IMQaVl+ZBXwU4qpdSTNUK4eQCJ0tTpz5BmGSOZGUBVM1GbX6QiRAkIZGEoAAR84WJECnDQ0Dl/OgnpoE8GIGqdBAzpk0vFILBloF+MKUQIATxqaKZ4JiViALJOKkAUsPhiaJlEusXViJemUWiyAFB5gItSCWfrEB1wgo2rzGKKaJ9dNanFm70GqlS4qFZ5kVIhbIFzAAhbkCBRtpi2eC6b8GKKbLYCGPCsN8RtkAAGCQQs8MAEC7yBMfWmapl0Aw2E28MQQ4yAtfA6u2lPCORRKwEPdORxxyB7LEAJ5CarsAK6CeDACQUwVu/LdspLU1Mlw8yswrxIafPOzTq3WM08M0ulyzwHAQA7" data-filename="CV-risk.gif" style="width: 51px;"><br>\n        <br>{QuestionnaireResponse.variable.cv_percentage:round(2)} %\n        <br>CV Risk\n      </td>\n      <td style="text-align: center;">\n        <img src="data:image/gif;base64,R0lGODlhOAAwAGYAACH5BAkAAFUALAAAAAA4ADAAhvHesP38+PPgsf7wyv/zzvrouf/z0Pnmtvz48PPiufjlsPnw2NiYAPry4fju09ihGt6qJvTmw9meDdSYAvXoyProtvDaqPvrwOnNie7QgP/66/jv2OG4U/DQgNifEv7vxvDapevRkNqoK/jiqfr16Pbgo+O5T+TBaOCxPOfEct+uN9+uNNuqMeCfAPPZk//44dmjIffr0PXnxtWZCfTame7Qf9ysNtaeEvvsv/vuy/DShe3Pf/Xdnu7YouTAZ+a8WOnFaNqhFujGbujCYvLXkPDUjfbfofDcq+3Wn+3VmezKc+vKc+nNhvjlr+a/XNqmLNylHdOWAOC0SOCyRt6sMf///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/gAGCg4SFhoeIiYqLjI2Oj4MIC5MLDZCFDZQLCJeDGhQJAgWjBQcCEQaOCBsJAAqkBQIJOZyqFKICAgC6ALu4MYsOCQUKvL25AgcJEbWKGwexx73T06LMhwjDxtLUogmWiQ6xud3U1AoHJIUkB6bm79UFDoji5NXw3d+DDQkH+Pi5CiwwhICcrm3b3glQMHCBu3//cqkjdMEdQojVDlDwhxHigQuEGhQwd7Ejx44QC4ALEMFYSZQwMSqQIUiDvXs4Y+okmYCTyHI5dwqdpjLAhgoKgQ4VemAgBQVKdy2dqpHlyaAJp6Ks6uDqS606D8xbMJJkVLAdiza4+RUtSgXg/0J9zeoWnqxBBspKDVo3ZSpBBef2hbiwWYCu3BIPxifWUEWsQ0Gk4ACDgeUHUlIgeXeAJsEExc7CBHFCBAMPD1Kn9jABBhALACxYaGeYULarijtiYBHFAwcaCoIrqADihwcJU1LYeECkdiEEEU7ShYjhCQMTRi4MMDCgu3cQKiaIlxCiUQS9e3VXBqKd+/b3BLp3CGJZQhJHOUZOhwfCBoMhF3An4AAjKFFCfDyoYBkDEvTwSAzR7EfNCR6oMMJ7A+oAQQ0DEJCBCKqJYAEk9XQkgAUszNBBdwPGlwEEGXT3QQEVEPMXidEQFsINLFw4oAHxabhDd/F5N1AnDoRmV/8vGFxXQIcEEMCdizBCKeV2N16ywU0KNWlCBSx2uB0BHUAwpJjxcdfJPrIkJQATLXwZ5ZzxlYCCBCvOKWCWnbCD2zEYTIDChfANQAMVLaxQApRo8tmJBv0A1YMIN7hA5wBFQJDogfFdqsGaz4FGkgU/zMDBk9vxAMEEHDRBJKPxObfmbfcIEAIEeHrnAgpL4EBnp1K+ACo20JTjgwQPuCDmBx+8Cux2wyJCKy8CHHEsFDU0a+WcUBoga7SCRADVXhacEMQEK2TQbKeNfgpuIhsdlEsFIXAwwwRO+PqrsO8uAuFBBVAgiAwcCKFdmi98228hDlSw0AYLL0wWxBEv7O4DwoEAADs=" data-filename="HeartAge.gif" style="width: 56px;"><br>\n        <br>{QuestionnaireResponse.variable.heart_validation:round(3)} years\n        <br>Heart age\n      </td>\n    </tr>\n  </tbody>\n</table>\n<br>\n<p></p>\n<p><br>\n</p>\n<table width="100%">\n  <tbody>\n    <tr data-visible="{QuestionnaireResponse.variable.bmi:boolean()}">\n      <td>Age:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS2} years</td>\n    </tr>\n    <tr>\n      <td>Sex:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1}</td>\n    </tr>\n    <tr>\n      <td>Male</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1.score:compare(&lt;,2)}</td>\n    </tr>\n    <tr>\n      <td>Female:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1:equal(Female)}</td>\n    </tr>\n    <tr>\n      <td>Systolic blood pressure (weekly average):</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS3} mmHg</td>\n    </tr>\n    <tr>\n      <td>Blood pressure treated with medication:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS4}</td>\n    </tr>\n    <tr>\n      <td>Smoker:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS5}</td>\n    </tr>\n    <tr>\n      <td>Diabetes:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS6}</td>\n    </tr>\n    <tr>\n      <td>Assessment type:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS7}</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS8.display}">\n      <td>HDL:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS8} mmol/L</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS9.display}">\n      <td>Total cholesterol:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS9} mmol/L</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS10.display}">\n      <td>Height:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS10} cm</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS11.display}">\n      <td>Weight:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS11} kg</td>\n    </tr>\n  </tbody>\n</table>\n\n<p style="color:gray; font-size:11px;"><br></p><p style="color:gray; font-size:11px;"><br></p>\n<p style="color:gray; font-size:11px;">{Questionnaire.description}</p>\n\n\n<p></p>\n<p><br></p>\n',
      },
      {
        url: '/html-report-active',
        valueBoolean: false,
      },
      {
        url: '/question-identifier-prefix',
        valueString: 'Item',
      },
      {
        url: '/question-identifier-next-sequence',
        valueInteger: 1,
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 0,
          },
          {
            url: '/score-name',
            valueString: 'bl_treated',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.93303',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS4}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.82263',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS4}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.99881',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS4}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_treated-F4',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.76157',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS4}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 2,
          },
          {
            url: '/score-name',
            valueString: 'bl_smoker',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_smoker-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_smoker-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.52873',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_smoker-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.65451',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 3,
          },
          {
            url: '/score-name',
            valueString: 'bl_diabetes',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_diabetes-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.69154',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_diabetes-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.57367',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bl_diabetes-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 4,
          },
          {
            url: '/score-name',
            valueString: 'bB_treated',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.88267',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.92672',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F4',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '2.81291',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==2 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_treated-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1.85508',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS4}==2 && #{QUS1QGS1}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 5,
          },
          {
            url: '/score-name',
            valueString: 'bB_smoker',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_smoker-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_smoker-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.70953',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_smoker-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.61868',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 6,
          },
          {
            url: '/score-name',
            valueString: 'bB_diabetes',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_diabetes-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.77763',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_diabetes-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0.53160',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bB_diabetes-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '0',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS6}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 7,
          },
          {
            url: '/score-name',
            valueString: 'bmi',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'bmi-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '#{QUS1QGS11} / ((#{QUS1QGS10}/100) * (#{QUS1QGS10}/100))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 8,
          },
          {
            url: '/score-name',
            valueString: 'sum_lipid',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_lipid-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '2.32888 * log(#{QUS1QGS2}) + 1.20904 * log(#{QUS1QGS9}*38.67) - 0.70833 * log(#{QUS1QGS8}*38.67) + #{bl_treated} * log(#{QUS1QGS3}) + #{bl_smoker} + #{bl_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_lipid-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '3.06117 * log(#{QUS1QGS2}) + 1.12370 * log(#{QUS1QGS9}*38.67) - 0.93263 * log(#{QUS1QGS8}*38.67) + #{bl_treated} * log(#{QUS1QGS3}) + #{bl_smoker} + #{bl_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 9,
          },
          {
            url: '/score-name',
            valueString: 'sum_bmi',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_bmi-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '3.11296 * log(#{QUS1QGS2}) + 0.79277 * log(#{bmi}) + #{bB_treated} * log(#{QUS1QGS3}) + #{bB_smoker} + #{bB_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2  && #{QUS1QGS7}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'sum_bmi-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString:
                      '2.72107 * log(#{QUS1QGS2}) + 0.51125 * log(#{bmi}) + #{bB_treated} * log(#{QUS1QGS3}) + #{bB_smoker} + #{bB_diabetes}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 10,
          },
          {
            url: '/score-name',
            valueString: 'cv',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F4',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.94833,(exp(#{sum_bmi} - 26.0145)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.88936,(exp(#{sum_lipid} - 23.9802)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.95012,(exp(#{sum_lipid} - 26.1931)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '1 - pow(0.88431,(exp(#{sum_bmi} - 23.9388)))',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 11,
          },
          {
            url: '/score-name',
            valueString: 'cv_percentage',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'cv_percentage-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '#{cv}*100',
                  },
                  {
                    url: '/selection-rule',
                    valueString: '1XXXXXXXXXX',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 12,
          },
          {
            url: '/score-name',
            valueString: 'heart_age',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F3',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.3267) * 114.2579',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.3675) * 158.1102',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.4294) * 192.4820',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_age-F5',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: 'pow(-log(1-#{cv}),0.3212) * 109.1966',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        url: '/list-of-score-definitions',
        extension: [
          {
            url: '/score-id',
            valueCode: '13071bd3-0636-4877-8071-46169e7e41ee',
          },
          {
            url: '/score-sequence',
            valueInteger: 13,
          },
          {
            url: '/score-name',
            valueString: 'heart_validation',
          },
          {
            url: '/list-of-formula-definitions',
            extension: [
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_validation-F2',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '86',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{heart_age}>85',
                  },
                ],
              },
              {
                url: '/set-of-api-formula',
                extension: [
                  {
                    url: '/formula-name',
                    valueString: 'heart_validation-F1',
                  },
                  {
                    url: '/mathematical-expression',
                    valueString: '#{heart_age}',
                  },
                  {
                    url: '/selection-rule',
                    valueString: 'VAR_COMPARE$#{heart_age}<=85',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
    identifier: [
      {
        use: 'usual',
        system: 'urn:uuid',
        value: '13071bd3-0636-4877-8071-46169e7e41ee',
        period: {
          start: '2023-03-13T15:10:02+00:00',
        },
      },
      {
        use: 'old',
        system: 'http://www.cambian.com/questionnaire/identifier',
        value: '13071bd3-0636-4877-8071-46169e7e41ee',
        period: {
          start: '2023-03-13T15:10:02+00:00',
          end: '2023-12-27T15:05:51+00:00',
        },
      },
    ],
    name: 'CV_risk_2023_IMPORT 13 march',
    title: 'Cardiovascular Risk Assessment',
    status: 'final',
    date: '2023-12-27T15:05:51+00:00',
    publisher: 'cambian',
    description:
      "The cardiovascular assessment estimates your cardiovascular (CV) risk and heart age. The CV risk is the chance of having a heart attack or stroke in the next 10 years. The heart age gives you an idea of how healthy your heart is compared to your age.  If you don't know your cholesterol levels you can use the body mass index (BMI) which is based on weight and height.",
    item: [
      {
        id: 'group-565725',
        extension: [
          {
            url: '/Item/description',
            valueString: 'cannot locate string',
          },
          {
            url: '/Item/question-group-sequence',
            valueInteger: 1,
          },
        ],
        linkId: 'QUS1QGS0',
        text: 'cannot locate string',
        type: 'group',
        item: [
          {
            id: '565728',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 1,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS1',
            text: 'Sex assigned at birth',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565731',
                  code: '1',
                  display: 'Female',
                },
              },
              {
                valueCoding: {
                  id: '565733',
                  code: '2',
                  display: 'Male',
                },
              },
            ],
          },
          {
            id: '565737',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 2,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS2',
            text: 'Age (years)',
            type: 'decimal',
            required: true,
          },
          {
            id: '565742',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 3,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS3',
            text: 'Systolic blood pressure weekly average (mmHg)',
            type: 'decimal',
            required: true,
          },
          {
            id: '565747',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 4,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS4',
            text: 'Blood pressure treated with medication',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565750',
                  code: '1',
                  display: 'Yes',
                },
              },
              {
                valueCoding: {
                  id: '565752',
                  code: '2',
                  display: 'No',
                },
              },
            ],
          },
          {
            id: '565756',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 5,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS5',
            text: 'Smoker',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565759',
                  code: '1',
                  display: 'Yes',
                },
              },
              {
                valueCoding: {
                  id: '565761',
                  code: '2',
                  display: 'No',
                },
              },
            ],
          },
          {
            id: '565765',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 6,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS6',
            text: 'Diabetes',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565768',
                  code: '1',
                  display: 'Yes',
                },
              },
              {
                valueCoding: {
                  id: '565770',
                  code: '2',
                  display: 'No',
                },
              },
            ],
          },
          {
            id: '565774',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5515,
              },
              {
                url: '/Item/multiple-answer-choice',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 7,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS7',
            text: 'Assessment type',
            type: 'choice',
            required: true,
            answerOption: [
              {
                valueCoding: {
                  id: '565777',
                  code: '1',
                  display: 'Lipids profile',
                },
              },
              {
                valueCoding: {
                  id: '565779',
                  code: '2',
                  display: 'Body mass index (BMI)',
                },
              },
            ],
          },
          {
            id: '565783',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 8,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS8',
            text: 'HDL (mmol/L) ',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '1',
              },
            ],
            required: false,
          },
          {
            id: '565790',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 9,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS9',
            text: 'Total cholesterol (mmol/L) ',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '1',
              },
            ],
            required: false,
          },
          {
            id: '565797',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 10,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS10',
            text: 'Height (cm)',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '2',
              },
            ],
            required: false,
          },
          {
            id: '565804',
            extension: [
              {
                url: '/Item/description',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/explanation',
                valueString: 'cannot locate string',
              },
              {
                url: '/Item/trendable',
                valueBoolean: false,
              },
              {
                url: '/Item/horizontal-orientation',
                valueBoolean: false,
              },
              {
                url: '/Item/hide-question',
                valueBoolean: false,
              },
              {
                url: '/Item/question-type-id',
                valueInteger: 5513,
              },
              {
                url: '/Item/min-value',
                valueDecimal: 0,
              },
              {
                url: '/Item/min-exclusion',
                valueBoolean: true,
              },
              {
                url: '/Item/max-exclusion',
                valueBoolean: false,
              },
              {
                url: '/Item/question-in-group-sequence',
                valueInteger: 11,
              },
              {
                url: '/Item/question-group-sequence',
                valueInteger: 1,
              },
            ],
            linkId: 'QUS1QGS11',
            text: 'Weight (kg)',
            type: 'decimal',
            enableWhen: [
              {
                question: 'QUS1QGS7',
                operator: '=',
                answerString: '2',
              },
            ],
            required: false,
          },
        ],
      },
    ],
  },
];
