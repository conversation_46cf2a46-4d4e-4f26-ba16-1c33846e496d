import { redirect } from 'next/navigation';

export default async function handler(req) {
  let callbackUrl = req.searchParams.callbackUrl;

  const baseUrl = process.env.NEXT_PUBLIC_DEPLOYMENT_HOST;

  redirect(
    `/?callbackUrl=${encodeURIComponent(
      validateUrl({
        url: callbackUrl.startsWith('/') ? baseUrl + callbackUrl : callbackUrl,
        baseUrl,
        defaultCallbackPath: '/home',
      }),
    )}`,
  );
}

const validateUrl = ({ url, baseUrl, defaultCallbackPath }) => {
  console.log({ url, baseUrl, defaultCallbackPath });
  try {
    if (new URL(url).origin === baseUrl) {
      url = new URL(url).href;
    } else {
      url = new URL(baseUrl + defaultCallbackPath).href;
    }
  } catch (error) {
    url = new URL(baseUrl + defaultCallbackPath).href;
    console.log('Caught Invalid URL:', url, 'Setting to ' + baseUrl + defaultCallbackPath + ' instead');
  }

  console.log({ url });
  return url;
};
