import AwsError from '@/lib/error/AwsError';
import { adminDeleteUser } from '@/lib/auth/cognito';

const deleteEndpoint = async (req) => {
  try {
    const headersList = req.headers;
    const userId = headersList.get('userId');
    const [_adminDeleteUserResult, adminDeleteUserError] = await adminDeleteUser({ username: userId });
    console.log(_adminDeleteUserResult);
    if (adminDeleteUserError) {
      return adminDeleteUserError.toNextResponse();
    }
    console.log('User successfully deleted from Cognito');
  } catch (err) {
    console.error('Error in deleteEndpoint:', err);
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
  return new Response(JSON.stringify({ success: true }), { status: 200 });
};

export const DELETE = deleteEndpoint;
