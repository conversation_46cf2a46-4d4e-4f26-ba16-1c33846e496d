'use client';
import * as React from 'react';
import { Paper, Box, useTheme, useMediaQuery } from '@mui/material';
import PrivateSidebar from '@/components/sideBar/PrivateSidebar';
import PublicFooter from '@/components/footer/PublicFooter';
import PrivateToolbar from '@/components/header/PrivateToolbar';
import { BreadcrumbsDisplay } from '@cambianrepo/breadcrumbs';
import { BodyContainer } from '@cambianrepo/ui';
import { usePathname } from 'next/navigation';
import { useClientSearchResults } from '@/context/ClientSearchResults';

export default function MainLayout({ children }) {
  const theme = useTheme();
  const pathname = usePathname();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const { setClientsData, setSearchParams, setQuery, setExpand } = useClientSearchResults();

  const [selectedTab, setSelectedTab] = React.useState(() => {
    const sessionTab = sessionStorage.getItem('selectedTab');
    return sessionTab || 'home';
  });

  const [selectedSubMenu, setSelectedSubMenu] = React.useState(() => {
    const sessionSubMenu = sessionStorage.getItem('selectedSubMenu');
    return sessionSubMenu || false;
  });

  const [expandedSubmenu, setExpandedSubmenu] = React.useState(false);
  const [showArrow, setShowArrow] = React.useState(false);

  React.useEffect(() => {
    sessionStorage.setItem('selectedTab', selectedTab);
    sessionStorage.setItem('selectedSubMenu', selectedSubMenu);
  }, [selectedTab, selectedSubMenu]);

  React.useEffect(() => {
    if (!pathname.includes('/questionnaire-responses')) {
      sessionStorage.removeItem('searchParams');
    }
    if (!pathname.includes('/questionnaire-report')) {
      sessionStorage.removeItem('selectedRowResource');
    }
    if (!pathname.includes('/clients')) {
      setSearchParams(null);
      setClientsData(null);
      setQuery('');
      setExpand(false);
    }
  }, [pathname]);

  return (
    <Paper
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        overflowX: 'hidden',
        borderRadius: '0px',
        border: '0px',
        backgroundColor: 'var(--grey)',
      }}
    >
      <PrivateToolbar
        selectedTab={selectedTab}
        setSelectedTab={setSelectedTab}
        selectedSubMenu={selectedSubMenu}
        setSelectedSubMenu={setSelectedSubMenu}
      />
      <Box sx={{ flex: 1 }}>
        <PrivateSidebar
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          selectedSubMenu={selectedSubMenu}
          setSelectedSubMenu={setSelectedSubMenu}
          expandedSubmenu={expandedSubmenu}
          setExpandedSubmenu={setExpandedSubmenu}
          showArrow={showArrow}
          setShowArrow={setShowArrow}
        />
        <Box
          sx={{ flex: 1, paddingLeft: showArrow ? (isSmallScreen ? '270px' : '310px') : '60px', paddingTop: '75px' }}
        >
          <BodyContainer>
            <BreadcrumbsDisplay />
            {children}
          </BodyContainer>
        </Box>
      </Box>
      <PublicFooter />
    </Paper>
  );
}
