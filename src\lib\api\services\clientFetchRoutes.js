import { NO_STORE } from '@/lib/constant';

let openSnackbar;

export const setOpenSnackbar = (fn) => {
  openSnackbar = fn;
};

export const fetchNextRoute = async (
  api,
  endpoint,
  { method = 'GET', body = null, errorsToIgnore = [] } = {},
  errorMsgForClient = 'Request failed',
) => {
  const url = `${process.env.NEXT_PUBLIC_DEPLOYMENT_HOST}/api/${api}?endpoint=${encodeURIComponent(endpoint)}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      cache: NO_STORE,
    },
  };
  if (body) {
    options.body = body;
  }
  console.log('fetchNextRoute', { url, options });

  try {
    const response = await fetch(url, options);
    console.log('fetchNextRoute', { response });
    if (!response.ok) {
      if (errorsToIgnore.length && errorsToIgnore.includes(response.status)) {
        return response;
      }
      const errorData = await response.json();
      const error = new Error(errorData || 'Something went wrong. Please contact technical support.');
      error.status = response.status;
      if (openSnackbar) {
        const failedUrl = new URL(url);
        console.log({ failedUrl });
        openSnackbar({
          variant: 'error',
          msg: errorMsgForClient,
          details: {
            apiRouteUrl: failedUrl.pathname + failedUrl.searchParams.get('endpoint'),
            options,
            apiErrorMsg: errorData,
            timestamp: new Date(Date.now()).toString(),
          },
        });
      }
      throw error;
    }
    return response;
  } catch (error) {
    // console.error(`Error in /api/${api} route: ${error.message}`);
    if (openSnackbar) {
      const failedUrl = new URL(url);
      console.log({ failedUrl });
      openSnackbar({
        variant: 'error',
        msg: errorMsgForClient,
        details: {
          apiRouteUrl: failedUrl.pathname + failedUrl.searchParams.get('endpoint'),
          options,
          apiErrorMsg: error.message,
          timestamp: new Date(Date.now()).toString(),
        },
      });
    }
    throw error;
  }
};
